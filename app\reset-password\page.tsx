'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Eye, EyeOff } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useResetPassword } from '@/lib/hooks/use-auth';
import { useAuth, useAuthActions } from '@/lib/stores/auth-store';
import { GuestGuard } from '@/lib/components/AuthGuard';

// Zod Validation Schema
const resetPasswordSchema = z.object({
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
  passwordConfirm: z.string(),
}).refine((data) => data.password === data.passwordConfirm, {
  message: "Passwords don't match",
  path: ["passwordConfirm"],
});

const ResetPasswordForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();

  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      passwordConfirm: "",
    },
  });

  const resetPasswordMutation = useResetPassword();
  const { isLoading, error } = useAuth();
  const { clearError } = useAuthActions();

  // Clear any existing errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (!tokenParam) {
      router.push('/login');
      return;
    }
    setToken(tokenParam);
  }, [searchParams, router]);

  const onSubmit = (data: { password: string; passwordConfirm: string }) => {
    if (!token) {
      return;
    }

    resetPasswordMutation.mutate({
      token,
      credentials: {
        password: data.password,
        passwordConfirm: data.passwordConfirm,
      },
    });
  };

  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <section className='flex flex-col items-center w-full max-w-md my-20'>
        {/* Header */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Reset Your Password</h1>

        {/* About */}
        <p className="text-sm text-center mb-6 text-gray-600">
          Enter your new password below to complete the reset process.
        </p>

        {/* Form */}
        <div className="md:max-w-lg mx-auto w-11/12 py-4 bg-white rounded-lg space-y-4">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full">
            {/* Error Display */}
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            {/* Password Field */}
            <div className="w-full">
              <label className="text-sm font-medium text-gray-800">New Password</label>
              <div className="mt-2 relative">
                <Controller
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter new password"
                      className="w-full pr-10"
                      disabled={isLoading || resetPasswordMutation.isPending}
                    />
                  )}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
                {errors.password && <span className="text-red-500 text-sm">{errors.password.message}</span>}
              </div>
            </div>

            {/* Confirm Password Field */}
            <div className="w-full">
              <label className="text-sm font-medium text-gray-800">Confirm New Password</label>
              <div className="mt-2 relative">
                <Controller
                  name="passwordConfirm"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm new password"
                      className="w-full pr-10"
                      disabled={isLoading || resetPasswordMutation.isPending}
                    />
                  )}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
                {errors.passwordConfirm && <span className="text-red-500 text-sm">{errors.passwordConfirm.message}</span>}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              size="lg"
              className="w-full h-12 text-lg"
              disabled={isLoading || resetPasswordMutation.isPending}
            >
              {isLoading || resetPasswordMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Resetting...
                </>
              ) : (
                'Reset Password'
              )}
            </Button>
          </form>
        </div>
      </section>
  );
};

const ResetPasswordPage = () => {
  return (
    <GuestGuard>
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }>
        <ResetPasswordForm />
      </Suspense>
    </GuestGuard>
  );
};

export default ResetPasswordPage;
