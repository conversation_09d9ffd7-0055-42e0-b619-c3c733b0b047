'use client';

import React, { useState } from 'react';
import { FaStar, FaStarHalfAlt, FaRegStar, FaShoppingCart } from 'react-icons/fa';
import { IoHeartOutline, IoShareSocialOutline } from 'react-icons/io5';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

import DeliverySelector from '@/app/(buyers)/(main)/_components/DeliverySelector';
import ProductDescriptionSection from '@/app/(buyers)/(main)/_components/ProductDescriptionSection';
import ProductDetailsProductsComponents from '@/app/(buyers)/(main)/_components/ProductDetailsProductsComponents';
import ShopDetailsSection from '@/app/(buyers)/(main)/_components/ShopDetailsSection';
import SizeVariationSelector from '@/app/(buyers)/(main)/_components/SizeVariationSelector';
import VerifiedComments from '@/app/(buyers)/(main)/_components/VerifiedComments';
import VariationSheet from '@/app/(buyers)/(main)/_components/VariationSheet';

// Define Product type
type ProductType = {
  id: number;
  name: string;
  brand: string;
  price: number;
  discount: number;
  availableSizes: string[];
  ratings: number;
};

type ProductDetailsComponentProps = {
  product: ProductType;
  unavailableSizes: string[];
};

const ProductDetailsComponent: React.FC<ProductDetailsComponentProps> = ({
  product,
  unavailableSizes,
}) => {
  const [openSheet, setOpenSheet] = useState(false);
  const [sheetActionType, setSheetActionType] = useState<'cart' | 'wishlist'>('cart');

  const handleOpenSheet = (type: 'cart' | 'wishlist') => {
    setSheetActionType(type);
    setOpenSheet(true);
  };

  return (
    <section className="w-full">
      {/* Product Details Section */}
      <section className="bg-white my-2 py-2 px-2">
        <h1>{product.name}</h1>
        <h2 className="text-sm py-1">Brand: {product.brand}</h2>

        <div className="flex gap-2 items-center">
          <p className="text-lg font-bold">GH₵{product.price}</p>
          <p className="text-sm text-gray-500 line-through">
            GH₵{(product.price * (1 + product.discount / 100)).toFixed(2)}
          </p>
          <div className="bg-primary-50 text-primary text-xs font-bold px-2 py-1 rounded">
            -{product.discount}%
          </div>
        </div>

        <h1 className="text-gray-500 text-sm">In Stock</h1>

        {/* Ratings + Share & Wishlist Icons */}
        <div className="flex justify-between border-b">
          <div className="flex items-center gap-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <span key={star}>
                {product.ratings >= star ? (
                  <FaStar className="text-yellow-500" size={20} />
                ) : product.ratings >= star - 0.5 ? (
                  <FaStarHalfAlt className="text-yellow-500" size={20} />
                ) : (
                  <FaRegStar className="text-yellow-500" size={20} />
                )}
              </span>
            ))}
            <Link href="/items/2" className="text-sm text-gray-500 ml-2">
              (40 verified ratings)
            </Link>
          </div>

          <div className="flex gap-1">
            <button className="rounded-full px-2 py-2 hover:bg-primary/10">
              <IoShareSocialOutline className="text-primary" size={23} />
            </button>
            <button
              className="rounded-full px-2 py-2 hover:bg-primary/10"
              onClick={() => handleOpenSheet('wishlist')}
            >
              <IoHeartOutline className="text-primary" size={23} />
            </button>
          </div>
        </div>

        {/* Sizes Pills */}
        <SizeVariationSelector
          sizes={product.availableSizes}
          unavailableSizes={unavailableSizes}
          openSheet={() => handleOpenSheet('cart')}
        />
      </section>

      <DeliverySelector />
      <ProductDescriptionSection productId={`${product.id}`} />
      <ProductDetailsProductsComponents title="Customers also viewed" />

      <VerifiedComments
        averageRating={product.ratings}
        totalReviews={120}
        productId={`${product.id}`}
        reviews={[
            { id: 1, user: 'John D.', title: 'Excellent Product', comment: 'Great quality, fast delivery!', rating: 5, date: 'Jan 2, 2025' },
            { id: 2, user: 'Ama K.', title: 'Satisfied', comment: 'Fits perfectly, I love it!', rating: 4, date: 'Feb 5, 2025' },
            { id: 3, user: 'Adwoa K.', title: 'Good', comment: 'Fits perfectly, I love it!', rating: 2, date: 'Feb 5, 2025' },
            { id: 4, user: 'Fosua K.', title: 'Satisfied', comment: 'Fits perfectly, I love it!', rating: 3, date: 'Feb 2, 2025' },
          ]
        }
        viewAllLink={`/items/${product.id}/comments`}
      />

      <ShopDetailsSection
        id="254"
        shopName="The Thrift Hub"
        sellerScore="97%"
        numFollowers={4532}
        performance={{
          shipping: 'Excellent',
          quality: 'Excellent',
          customerRating: 'Good',
        }}
      />

      <ProductDetailsProductsComponents title="You may also like" />

      {/* Bottom Bar */}
      <section className="bottombar z-50 w-full left-0 right-0 fixed bottom-0 bg-white border-t">
        <section className="bottombar_container flex w-full items-center justify-between">
          <Button
            className="h-10 w-full bg-primary hover:bg-primary/90 text-primary-foreground flex items-center justify-center gap-4 transition-colors duration-200"
            onClick={() => handleOpenSheet('cart')}
          >
            <FaShoppingCart size={20} />
            Add to Cart
          </Button>
        </section>
      </section>

      {/* VariationSheet */}
      <VariationSheet
        open={openSheet}
        onOpenChange={setOpenSheet}
        actionType={sheetActionType}
        sizes={product.availableSizes.map((size) => ({
          size,
          price: product.price,
          stock: unavailableSizes.includes(size) ? 'unavailable' : 'in stock',
        }))}
        unavailableSizes={unavailableSizes}
      />
    </section>
  );
};

export default ProductDetailsComponent;
