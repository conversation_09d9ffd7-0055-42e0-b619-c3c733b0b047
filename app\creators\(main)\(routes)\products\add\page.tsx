'use client';

import React, { Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import VerificationGuard from '@/lib/components/VerificationGuard';

import ProductCreationLayout from './_components/ProductCreationLayout';
import ProductInfoForm from './_components/ProductInfoForm';
import SpecificationsForm from './_components/SpecificationsForm';
import VariationsForm from './_components/VariationsForm';

const ProductCreationPageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentStep = searchParams.get("step") || "product-info";

  const handleStepComplete = (nextStep?: string) => {
    if (nextStep) {
      router.push(`/creators/products/add?step=${nextStep}`);
    } else {
      // Product creation complete, redirect to products list
      router.push('/creators/products');
    }
  };

  const handleStepBack = (previousStep: string) => {
    router.push(`/creators/products/add?step=${previousStep}`);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'product-info':
        return (
          <ProductInfoForm 
            onNext={() => handleStepComplete('specifications')}
          />
        );
      case 'specifications':
        return (
          <SpecificationsForm 
            onNext={() => handleStepComplete('variations')}
            onBack={() => handleStepBack('product-info')}
          />
        );
      case 'variations':
        return (
          <VariationsForm 
            onComplete={() => handleStepComplete()}
            onBack={() => handleStepBack('specifications')}
          />
        );
      default:
        return (
          <ProductInfoForm 
            onNext={() => handleStepComplete('specifications')}
          />
        );
    }
  };

  return (
    <VerificationGuard feature="products">
      <ProductCreationLayout>
        {renderCurrentStep()}
      </ProductCreationLayout>
    </VerificationGuard>
  );
};

const ProductCreationPage = () => {
  return (
    <Suspense fallback={<FullScreenLoader text="Loading product creation..." />}>
      <ProductCreationPageContent />
    </Suspense>
  );
};

export default ProductCreationPage;
