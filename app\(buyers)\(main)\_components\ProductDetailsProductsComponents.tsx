import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import { products } from '@/lib/data';
import ProductCard from '@/components/ui/custom/ProductCard';

interface ProductDetailsProductsComponentsProps {
  title: string;
}

const ProductDetailsProductsComponents: React.FC<ProductDetailsProductsComponentsProps> = ({ title }) => {
  return (
    <section className='bg-white my-2 py-3 px-3 rounded shadow-sm'>
      <h1 className="text-sm font-semibold text-gray-600 pb-2">{title}</h1>
      <ProductsCarousel>
        {products.map((product) => (
          <div key={product.id} className="p-1">
            <ProductCard product={product} cardType='normal' />
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default ProductDetailsProductsComponents;
