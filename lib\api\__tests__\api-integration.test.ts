/**
 * API Integration Tests
 * Tests to verify the new API structure is working correctly
 */

import { 
  authApi, 
  onboardingApi, 
  categoriesApi,
  BaseApiClient,
  AuthManager,
  ApiErrorHandler,
  handleApiError,
  getSessionInfo,
  hasRole,
  isValidToken,
  API_CONFIG,
  API_ENDPOINTS
} from '../index';

// Mock fetch for testing
global.fetch = jest.fn();

describe('API Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  describe('API Exports', () => {
    test('should export all required API modules', () => {
      expect(authApi).toBeDefined();
      expect(onboardingApi).toBeDefined();
      expect(categoriesApi).toBeDefined();
    });

    test('should export core infrastructure', () => {
      expect(BaseApiClient).toBeDefined();
      expect(AuthManager).toBeDefined();
      expect(ApiErrorHandler).toBeDefined();
    });

    test('should export utility functions', () => {
      expect(handleApiError).toBeDefined();
      expect(getSessionInfo).toBeDefined();
      expect(hasRole).toBeDefined();
      expect(isValidToken).toBeDefined();
    });

    test('should export configuration', () => {
      expect(API_CONFIG).toBeDefined();
      expect(API_ENDPOINTS).toBeDefined();
      expect(API_CONFIG.BASE_URL).toBeDefined();
    });
  });

  describe('BaseApiClient', () => {
    test('should create client with default config', () => {
      const client = new BaseApiClient();
      expect(client).toBeInstanceOf(BaseApiClient);
    });

    test('should create client with custom base URL', () => {
      const customUrl = 'https://custom-api.com/v1';
      const client = new BaseApiClient(customUrl);
      expect(client).toBeInstanceOf(BaseApiClient);
    });
  });

  describe('AuthManager', () => {
    test('should handle token storage and retrieval', () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RKzJl8h6SW5ZAeOCbWCZgb5H8VNhzMzjGgJGJY';
      
      // Mock localStorage
      const mockLocalStorage = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      };
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      });

      AuthManager.setToken(mockToken, 'buyer');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('auth_token', mockToken);
    });

    test('should validate token format', () => {
      const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      const invalidToken = 'invalid.token';

      expect(AuthManager.isValidTokenFormat(validToken)).toBe(true);
      expect(AuthManager.isValidTokenFormat(invalidToken)).toBe(false);
    });

    test('should get session info', () => {
      const sessionInfo = AuthManager.getSessionInfo();
      expect(sessionInfo).toHaveProperty('isValid');
      expect(sessionInfo).toHaveProperty('isExpired');
      expect(sessionInfo).toHaveProperty('expiresIn');
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors correctly', () => {
      const error = new Error('Test error');
      const apiError = ApiErrorHandler.handleError(error);
      
      expect(apiError).toHaveProperty('message');
      expect(apiError.message).toBe('Test error');
    });

    test('should handle network errors', () => {
      const networkError = new TypeError('fetch failed');
      const apiError = ApiErrorHandler.handleError(networkError);
      
      expect(ApiErrorHandler.isNetworkError(apiError)).toBe(true);
    });

    test('should provide user-friendly messages', () => {
      const error = { code: 'NETWORK_ERROR', message: 'Network failed' };
      const userMessage = ApiErrorHandler.getUserMessage(error);
      
      expect(userMessage).toBe('Please check your internet connection and try again.');
    });
  });

  describe('API Configuration', () => {
    test('should have correct endpoint structure', () => {
      expect(API_ENDPOINTS.AUTH).toBeDefined();
      expect(API_ENDPOINTS.ONBOARDING).toBeDefined();
      expect(API_ENDPOINTS.CATEGORIES).toBeDefined();
      
      expect(API_ENDPOINTS.AUTH.LOGIN).toBe('/auth/login');
      expect(API_ENDPOINTS.ONBOARDING.STATUS).toBe('/creators/onboarding/status');
      expect(API_ENDPOINTS.CATEGORIES.HIERARCHY).toBe('/categories/hierarchy');
    });

    test('should have correct base URL', () => {
      expect(API_CONFIG.BASE_URL).toBeDefined();
      expect(typeof API_CONFIG.BASE_URL).toBe('string');
    });
  });

  describe('Backward Compatibility', () => {
    test('should maintain backward compatibility for error handlers', () => {
      const error = new Error('Test error');
      
      // Test that old error handler functions still work
      const authError = handleApiError(error);
      expect(authError).toHaveProperty('message');
    });
  });
});

describe('API Module Functions', () => {
  beforeEach(() => {
    (global.fetch as jest.Mock).mockClear();
  });

  test('authApi should have all required methods', () => {
    expect(authApi.login).toBeDefined();
    expect(authApi.register).toBeDefined();
    expect(authApi.forgotPassword).toBeDefined();
    expect(authApi.resetPassword).toBeDefined();
    expect(authApi.getProfile).toBeDefined();
    expect(authApi.logout).toBeDefined();
  });

  test('onboardingApi should have all required methods', () => {
    expect(onboardingApi.getOnboardingStatus).toBeDefined();
    expect(onboardingApi.getBusinessInfo).toBeDefined();
    expect(onboardingApi.updateBusinessInfo).toBeDefined();
  });

  test('categoriesApi should have all required methods', () => {
    expect(categoriesApi.getHierarchy).toBeDefined();
  });
});

// Integration test to verify the API structure works end-to-end
describe('End-to-End API Integration', () => {
  test('should be able to import and use API from index', async () => {
    // Mock successful response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => ({
        status: 'success',
        data: { categories: [] }
      }),
    });

    // This should work without any issues
    try {
      await categoriesApi.getHierarchy();
      expect(global.fetch).toHaveBeenCalled();
    } catch (error) {
      // Expected to potentially fail due to mocking, but should not have import errors
    }
  });
});
