'use client';

import { useRouter } from 'next/navigation';
import { FaStar } from 'react-icons/fa';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

interface Notification {
  id: string;
  type: 'shipping' | 'review';
  date: string;
  title: string;
  message: string;
  image?: string;
  productName?: string;
  orderId?: string;
}

const notifications: Notification[] = [
  {
    id: '1',
    type: 'shipping',
    date: '20th January',
    title: 'Confirmed',
    message:
      'Item(s) from your order 343219896 have been shipped and are expected to be delivered between 22-Jan-2025 and 24-Jan-2025. We will inform you when the items are ready for delivery.',
    image: '/images/ladies/jeans.jpg',
    productName: 'Stylish Dress',
    orderId: '343219896',
  },
  {
    id: '2',
    type: 'review',
    date: '20th January',
    title: 'How was your Experience?',
    message:
      'Thank you for shopping with us! We hope you were happy with your item(s). Our customers rely on reviews from insightful customers such as yourself to decide which of our products is best for them. We would love to hear your feedback on the seller and item, please click on the "Pending Reviews" section to share. Thank you for shopping on Jumia!',
  },
  {
    id: '3',
    type: 'shipping',
    date: '20th January',
    title: 'Confirmed',
    message:
      'Item(s) from your order 343219896 have been shipped and are expected to be delivered between 22-Jan-2025 and 24-Jan-2025. We will inform you when the items are ready for delivery.',
    image: '/images/ladies/short.jpg',
    productName: 'Stylish Dress',
    orderId: '343219896',
  },
  {
    id: '4',
    type: 'review',
    date: '20th January',
    title: 'How was your Experience?',
    message:
      'Thank you for shopping with us! We hope you were happy with your item(s). Our customers rely on reviews from insightful customers such as yourself to decide which of our products is best for them. We would love to hear your feedback on the seller and item, please click on the "Pending Reviews" section to share. Thank you for shopping on Jumia!',
  },
  {
    id: '5',
    type: 'shipping',
    date: '20th January',
    title: 'Confirmed',
    message:
      'Item(s) from your order 343219896 have been shipped and are expected to be delivered between 22-Jan-2025 and 24-Jan-2025. We will inform you when the items are ready for delivery.',
    image: '/images/ladies/skirts.jpg',
    productName: 'Stylish Dress',
    orderId: '343219896',
  },
  {
    id: '6',
    type: 'shipping',
    date: '20th January',
    title: 'Confirmed',
    message:
      'Item(s) from your order 343219896 have been shipped and are expected to be delivered between 22-Jan-2025 and 24-Jan-2025. We will inform you when the items are ready for delivery.',
    image: '/images/men/Suits_TN.png',
    productName: 'Stylish Dress',
    orderId: '343219896',
  },
  {
    id: '7',
    type: 'shipping',
    date: '20th January',
    title: 'Confirmed',
    message:
      'Item(s) from your order 343219896 have been shipped and are expected to be delivered between 22-Jan-2025 and 24-Jan-2025. We will inform you when the items are ready for delivery.',
    image: '/images/men/Shirts_TN.png',
    productName: 'Stylish Dress',
    orderId: '343219896',
  },
  {
    id: '8',
    type: 'review',
    date: '20th January',
    title: 'How was your Experience?',
    message:
      'Thank you for shopping with us! We hope you were happy with your item(s). Our customers rely on reviews from insightful customers such as yourself to decide which of our products is best for them. We would love to hear your feedback on the seller and item, please click on the "Pending Reviews" section to share. Thank you for shopping on Jumia!',
  }
];

const InboxPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="account" title="Your Inbox" description="Stay updated with order notifications, delivery updates, and exclusive offers. Never miss important updates about your orders!" />;
  }

  const handleGoToOrderDetails = (orderId: string) => {
    router.push(`/orders/${orderId}`);
  };

  const handleGoToReviews = () => {
    router.push(`/pendingReviews`);
  };

  return (
    <section className="w-full mt-20 py-4">
      <h1 className="text-lg font-semibold text-gray-700 mb-4">Inbox Messages (6)</h1>

      {notifications.map((notification) => (
        <div
          key={notification.id}
          className="bg-white px-2 py-4 rounded shadow-sm mb-2 cursor-pointer hover:bg-gray-50 transition"
          onClick={() =>
            notification.type === 'shipping'
              ? handleGoToOrderDetails(notification.orderId!)
              : handleGoToReviews()
          }
        >
          <p className="text-sm text-gray-600 my-1">{notification.date}</p>
          <h2 className="font-medium text-gray-800 flex items-center gap-1">
            {notification.title}
            {notification.type === 'review' && (
              <span className="flex items-center ml-2">
                {[...Array(5)].map((_, i) => (
                  <FaStar key={i} className="text-yellow-400 text-xs" />
                ))}
              </span>
            )}
          </h2>
          <p className="text-sm text-gray-700 mt-1">{notification.message}</p>

          {notification.type === 'shipping' && notification.image && (
            <div className="flex items-center gap-3 border rounded-md mt-3">
              <div className="w-16 h-16 bg-gray-200 rounded">
                <img
                  src={notification.image}
                  alt={notification.productName}
                  className="w-full h-full object-cover rounded"
                />
              </div>
              <p className="text-sm font-medium text-gray-800">{notification.productName}</p>
            </div>
          )}
        </div>
      ))}
    </section>
  );
};

export default InboxPage;
