'use client';

import { useEffect } from 'react';
import { useAuthActions } from '@/lib/stores/auth-store';

/**
 * Component that validates authentication token on app initialization
 * This prevents infinite redirect loops caused by expired tokens
 */
export const AuthInitializer = ({ children }: { children: React.ReactNode }) => {
  const { validateToken } = useAuthActions();

  useEffect(() => {
    // Validate token on app initialization
    validateToken();
  }, [validateToken]);

  return <>{children}</>;
};
