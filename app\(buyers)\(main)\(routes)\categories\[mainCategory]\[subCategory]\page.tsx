import FilterSort from '@/app/(buyers)/(main)/_components/FilterSort';
import ProductList from '@/components/ui/custom/ProductsList';
import { fashionCategories } from '@/lib/navlinks';

export default async function SubCategoryPage({
  params,
}: {
  params: Promise<{ mainCategory: string, subCategory: string }>;
}) {
  const { mainCategory, subCategory } = await params;

  const category = fashionCategories.find(cat => cat.headerLink.split('/').pop() === mainCategory);
  const subCat = category?.links.find(link => link.route.split('/').pop() === subCategory);
  
  if (!subCat) {
    return <div>Subcategory not found</div>;
  }

  return (
    <section className="w-full">
       <FilterSort />
      <h1 className="text-gray-800 uppercase font-bold mb-4">{subCat.label}</h1>
      <ProductList/>
    </section>
  );
}

