'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import CustomerAddressSection from '../../_components/CustomerAddressSection';
import DeliveryTypeSelector from '../../_components/DeliveryTypeSelector';
import ShipmentSection from '../../_components/ShipmentSection';
import { useRouter } from 'next/navigation';

const CheckoutPage = () => {
  const [paymentMethod, setPaymentMethod] = useState('');
  const router = useRouter()


  return (
    <section className="p-2 mt-20  min-h-screen w-full">
      {/* ORDER SUMMARY */}
      <h2 className="font-medium text-gray-700 mb-1">ORDER SUMMARY</h2>
      <section className="bg-white text-gray-600 px-2 py-4 mb-4 rounded-lg shadow-sm">

        <div className="flex justify-between mb-2">
          <p>Items's total</p>
          <p className="font-bold">GH₵100</p>
        </div>
        <div className="flex justify-between mb-2">
          <p>Delivery fees</p>
          <p className="font-bold">GH₵24</p>
        </div>
        <hr className="my-2" />
        <div className="flex justify-between font-semibold">
          <p>Total</p>
          <p>GH₵2100.00</p>
        </div>
      </section>


     {/* PAYMENT METHOD */}
    <h2 className="font-medium text-gray-700 mb-1">PAYMENT METHOD</h2>
    <section className="bg-white px-2 py-4 mb-4 rounded-lg shadow-sm">

    {/* Payment on Delivery */}
    <div className="mb-4">
        <h3 className="font-medium mb-2 text-gray-700">Payment on Delivery</h3>
        <div className="space-y-3">
        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="momo_delivery"
            checked={paymentMethod === 'momo_delivery'}
            onChange={() => setPaymentMethod('momo_delivery')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">Mobile Money on Delivery</span>
        </label>

        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="cash_delivery"
            checked={paymentMethod === 'cash_delivery'}
            onChange={() => setPaymentMethod('cash_delivery')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">Cash on Delivery</span>
        </label>
        </div>
    </div>

    {/* Prepay Now */}
    <div className="mb-4">
        <h3 className="font-medium mb-2 text-gray-700">Prepay Now</h3>
        <div className="space-y-3">
        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="card"
            checked={paymentMethod === 'card'}
            onChange={() => setPaymentMethod('card')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">Pay with Card</span>
        </label>
        </div>
    </div>

    {/* Mobile Money */}
    <div>
        <h3 className="font-medium mb-2 text-gray-700">Mobile Money</h3>
        <div className="space-y-3">
        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="mtn_momo"
            checked={paymentMethod === 'mtn_momo'}
            onChange={() => setPaymentMethod('mtn_momo')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">MTN Mobile Money</span>
        </label>

        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="airteltigo_momo"
            checked={paymentMethod === 'airteltigo_momo'}
            onChange={() => setPaymentMethod('airteltigo_momo')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">AirtelTigo MoMo</span>
        </label>

        <label className="flex items-center gap-3 cursor-pointer">
            <input
            type="radio"
            name="paymentMethod"
            value="telecel_momo"
            checked={paymentMethod === 'telecel_momo'}
            onChange={() => setPaymentMethod('telecel_momo')}
            className="form-radio h-5 w-5 text-primary"
            />
            <span className="text-sm">Telecel Mobile Money</span>
        </label>
        </div>
    </div>
    </section>

    <CustomerAddressSection />

    <DeliveryTypeSelector />

    <ShipmentSection />

      {/* CONFIRM ORDER BUTTON */}
      <Button onClick={()=> router.push("/orders/confirm")} className="bg-primary text-primary-foreground w-full py-3 rounded-lg hover:bg-primary/90 transition-colors duration-200">
        Confirm Order
      </Button>
    </section>
  );
};

export default CheckoutPage;
