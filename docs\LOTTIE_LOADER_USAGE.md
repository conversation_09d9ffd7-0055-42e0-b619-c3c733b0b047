# Lottie Loader Implementation Guide

This guide shows how to use the new Lottie-based loading animations throughout the Everyfash platform.

## Components Overview

### 1. <PERSON><PERSON><PERSON>oa<PERSON> (Base Component)
The core component that renders the Lottie animation.

```tsx
import LottieLoader from '@/components/ui/LottieLoader';

// Basic usage
<LottieLoader />

// With custom size and text
<LottieLoader 
  size="lg" 
  text="Loading..." 
  textSize="md"
  centered={true}
/>
```

**Props:**
- `size`: 'sm' | 'md' | 'lg' | 'xl' | '2xl' (default: 'md')
- `text`: Optional loading text
- `textSize`: 'sm' | 'md' | 'lg' (default: 'md')
- `centered`: Boolean to center the component
- `loop`: Whether to loop animation (default: true)

### 2. FullScreenLoader
For page-level loading states.

```tsx
import FullScreenLoader from '@/components/ui/FullScreenLoader';

// Basic usage
<FullScreenLoader />

// With custom title and text
<FullScreenLoader 
  title="Verifying Your Email"
  text="Please wait while we verify your email address..."
  size="xl"
/>
```

**Props:**
- `title`: Optional title above the loader
- `text`: Loading text (default: 'Loading...')
- `size`: Loader size 'sm' | 'md' | 'lg' | 'xl' | '2xl' (default: 'lg')
- `fullScreen`: Use min-h-screen (default: true)

### 3. ButtonLoader
For inline loading states in buttons and forms.

```tsx
import ButtonLoader from '@/components/ui/ButtonLoader';

// In a button
<Button disabled={isLoading}>
  {isLoading ? (
    <ButtonLoader text="Sending..." />
  ) : (
    'Send Email'
  )}
</Button>
```

**Props:**
- `text`: Loading text (default: 'Loading...')
- `size`: Loader size (default: 'sm')
- `showText`: Whether to show text (default: true)

## Global Loading System

### 4. Next.js App Router Loading Files
Automatic loading states for route transitions.

```tsx
// app/loading.tsx
import FullScreenLoader from '@/components/ui/FullScreenLoader';

export default function Loading() {
  return <FullScreenLoader text="Loading..." size="xl" />;
}
```

### 5. Global Loading Provider
Programmatic control over loading states.

```tsx
import { useLoading, usePageLoading, useManualLoading } from '@/lib/hooks/use-page-loading';

// Automatic loading based on state
function MyComponent() {
  const [isLoading, setIsLoading] = useState(false);
  usePageLoading(isLoading, 'Processing...');

  return <div>Content</div>;
}

// Manual loading control
function AnotherComponent() {
  const { showLoading, hideLoading } = useManualLoading();

  const handleAction = async () => {
    showLoading('Saving...');
    await saveData();
    hideLoading();
  };

  return <button onClick={handleAction}>Save</button>;
}
```

## Migration Examples

### Before (Loader2):
```tsx
<div className="min-h-screen flex items-center justify-center">
  <div className="text-center">
    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
    <p className="text-gray-600">Loading...</p>
  </div>
</div>
```

### After (Lottie):
```tsx
<FullScreenLoader text="Loading..." />
```

### Button Loading Before:
```tsx
{isLoading ? (
  <>
    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
    Sending...
  </>
) : (
  'Send Email'
)}
```

### Button Loading After:
```tsx
{isLoading ? (
  <ButtonLoader text="Sending..." />
) : (
  'Send Email'
)}
```

## Implementation Status

### ✅ Completed
- `app/verify-email/page.tsx` - Full screen and Suspense fallback
- `lib/components/AuthGuard.tsx` - All loading states (now with larger sizes)
- `app/creators/(auth)/forgot-password/page.tsx` - Button loading
- `app/creators/(main)/onboarding/page.tsx` - Full screen loading
- `app/creators/(main)/(routes)/page.tsx` - Verification status loading
- `components/ui/VerificationSkeleton.tsx` - Skeleton loading indicator
- **Global Loading System:**
  - `app/loading.tsx` - Root level loading
  - `app/(buyers)/(main)/(routes)/loading.tsx` - Buyer pages
  - `app/creators/(main)/(routes)/loading.tsx` - Creator dashboard
  - `app/creators/(main)/onboarding/loading.tsx` - Onboarding pages
  - `lib/providers/loading-provider.tsx` - Global loading provider
  - `lib/hooks/use-page-loading.ts` - Loading hooks

### 🔄 Remaining Files to Update
- `app/creators/(main)/onboarding/page.tsx`
- `app/auth/callback/page.tsx`
- `app/creators/(auth)/check-email/page.tsx`
- `app/(buyers)/(auth)/forgot-password/page.tsx`
- `app/reset-password/page.tsx`
- `lib/components/onboarding/ShopInfoForm.tsx`
- All other files using `Loader2` from lucide-react

## Best Practices

1. **Use FullScreenLoader** for page-level loading states
2. **Use ButtonLoader** for form submissions and button actions
3. **Use LottieLoader** for custom loading scenarios
4. **Use loading.tsx files** for automatic route transition loading
5. **Use global loading provider** for programmatic control
6. **Keep text concise** and user-friendly
7. **Match size** to the context:
   - `sm` for buttons and inline elements
   - `md` for cards and components
   - `lg` for page sections
   - `xl` for full-screen loading
   - `2xl` for critical loading states (like "Verifying access")
8. **Use larger sizes** for important loading states that users need to notice
9. **Provide meaningful text** that describes what's happening

## Animation Details

The Lottie animation (`loader.json`) features:
- Colorful circular loading animation
- Shopping-themed icons that cycle through
- Smooth transitions and professional appearance
- Optimized for web performance
- Matches Everyfash brand colors
