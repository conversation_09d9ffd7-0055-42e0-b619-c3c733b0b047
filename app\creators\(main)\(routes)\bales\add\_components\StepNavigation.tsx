"use client";

import { usePathname, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button"; // Adjust path based on your project
import { ChevronLeft, ChevronRight } from "lucide-react"; // Or your preferred icons
import { steps } from "./StepProgress";



// Props for the StepNavigation component
interface StepNavigationProps {
  onSubmit: () => void;
}

const StepNavigation: React.FC<StepNavigationProps> = ({ onSubmit }) => {
  const pathname = usePathname();
  const router = useRouter();

  const currentStepIndex = steps.findIndex((step) => step.route === pathname);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;

  const goBack = () => {
    if (!isFirstStep) {
      router.push(steps[currentStepIndex - 1].route);
    }
  };

  const goNext = () => {
    if (!isLastStep) {
      onSubmit(); // Calls the submit function on the last step
      router.push(steps[currentStepIndex + 1].route)
    } else {
      onSubmit(); // Calls the submit function on the last step
    }
  };

  return (
    <section className="flex gap-4 px-2">
      <Button
        variant="outline"
        onClick={goBack}
        disabled={isFirstStep}
        className="flex items-center"
      >
        <ChevronLeft className="mr-2" />
      </Button>

      <Button
        className="w-full bg-primary hover:bg-primary/90 text-white flex items-center justify-center"
        onClick={goNext}
      >
        {isLastStep ? "Submit" : "Next"}
        {!isLastStep && <ChevronRight className="ml-2" />}
      </Button>
    </section>
  );
};

export default StepNavigation;
