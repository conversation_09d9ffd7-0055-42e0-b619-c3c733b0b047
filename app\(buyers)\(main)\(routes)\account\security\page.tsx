'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export default function SecurityUpdatePage() {
  const { toast } = useToast();

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleChangePassword = () => {
    if (newPassword !== confirmPassword) {
      toast({
        variant: 'destructive',
        title: 'Passwords do not match!',
        description: 'Please make sure both passwords are the same.',
      });
      return;
    }

    console.log({ currentPassword, newPassword });

    toast({
      title: 'Success!',
      description: 'Password changed successfully!',
      className: 'bg-green-100 text-green-800',
    });
  };

  return (
    <section className="w-full mt-20 space-y-6">
      <h2 className="text-sm uppercase font-bold text-gray-600 mb-3">Update Security</h2>

      <div className="bg-white p-4 rounded shadow-sm space-y-4">
        {/* Current Password */}
        <div>
          <label className="block text-sm text-gray-700">Current Password</label>
          <Input
            type="password"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
          />
        </div>

        {/* New Password */}
        <div>
          <label className="block text-sm text-gray-700">New Password</label>
          <Input
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
          />
        </div>

        {/* Confirm Password */}
        <div>
          <label className="block text-sm text-gray-700">Confirm Password</label>
          <Input
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
          />
        </div>

        <Button className="w-full bg-green-500 hover:bg-green-600 text-white" onClick={handleChangePassword}>
          Update Password
        </Button>
      </div>
    </section>
  );
}
