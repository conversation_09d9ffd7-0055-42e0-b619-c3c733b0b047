'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { usePaymentInfo, useUpdatePaymentInfo } from '@/lib/hooks/use-onboarding';
import { usePaymentInfoProfile, useUpdatePaymentInfoProfile } from '@/lib/hooks/useProfile';
import { PaymentInfoUpdateData } from '@/lib/types/auth';
import { toast } from '@/hooks/use-toast';
import { getApiErrorMessage, getApiErrorTitle } from '@/lib/utils/error-utils';
import {
  FormLayout,
  FormSection,
  FormInput,
  FormSelect,
  FormCheckbox,
  FormActions
} from './FormComponents';

// Validation schema with proper conditional validation
const paymentFormSchema = z.object({
  paymentOption: z.enum(['bank', 'mobile_money'], {
    required_error: 'Please select a payment option',
  }),
  // Bank details - conditionally required
  beneficiaryName: z.string().min(1, 'Beneficiary name is required').or(z.literal('')),
  bankAccountNumber: z.string().min(1, 'Bank account number is required').or(z.literal('')),
  bankName: z.string().min(1, 'Bank name is required').or(z.literal('')),
  bankBranch: z.string().optional(),
  swiftCode: z.string().optional(),
  // Mobile money details - conditionally required
  serviceProvider: z.string().min(1, 'Service provider is required').or(z.literal('')),
  momoRegisteredName: z.string().min(1, 'Registered name is required').or(z.literal('')),
  momoRegisteredNumber: z.string().min(1, 'Registered number is required').or(z.literal('')),
  // Payout preferences - always required
  frequency: z.enum(['weekly', 'monthly', 'bi-weekly'], {
    required_error: 'Please select payout frequency',
  }),
  minimumAmount: z.coerce.number().min(1, 'Minimum amount must be at least 1'),
  automaticPayouts: z.boolean(),
}).superRefine((data, ctx) => {
  // Validate bank details when bank is selected
  if (data.paymentOption === 'bank') {
    if (!data.beneficiaryName || data.beneficiaryName.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Beneficiary name is required for bank payments',
        path: ['beneficiaryName'],
      });
    }
    if (!data.bankAccountNumber || data.bankAccountNumber.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Bank account number is required for bank payments',
        path: ['bankAccountNumber'],
      });
    }
    if (!data.bankName || data.bankName.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Bank name is required for bank payments',
        path: ['bankName'],
      });
    }
  }

  // Validate mobile money details when mobile money is selected
  if (data.paymentOption === 'mobile_money') {
    if (!data.serviceProvider || data.serviceProvider.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Service provider is required for mobile money payments',
        path: ['serviceProvider'],
      });
    }
    if (!data.momoRegisteredName || data.momoRegisteredName.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Registered name is required for mobile money payments',
        path: ['momoRegisteredName'],
      });
    }
    if (!data.momoRegisteredNumber || data.momoRegisteredNumber.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Registered number is required for mobile money payments',
        path: ['momoRegisteredNumber'],
      });
    }
  }
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface PaymentInfoFormProps {
  onSuccess?: () => void;
  onBack?: () => void;
  isProfileMode?: boolean;
}

const GHANA_BANKS = [
  'Ghana Commercial Bank',
  'Ecobank Ghana',
  'Standard Chartered Bank Ghana',
  'Barclays Bank Ghana',
  'Zenith Bank Ghana',
  'Fidelity Bank Ghana',
  'Cal Bank',
  'ADB Bank',
  'UMB Bank',
  'Prudential Bank',
  'Other',
];

const MOBILE_MONEY_PROVIDERS = [
  'MTN Mobile Money',
  'Vodafone Cash',
  'AirtelTigo Money',
];

export default function PaymentInfoForm({ onSuccess, onBack, isProfileMode = false }: PaymentInfoFormProps) {
  // Use different hooks based on mode
  const { data: paymentInfoResponse, isLoading: isLoadingPaymentInfo } = isProfileMode
    ? usePaymentInfoProfile()
    : usePaymentInfo();
  const updatePaymentInfoMutation = isProfileMode
    ? useUpdatePaymentInfoProfile()
    : useUpdatePaymentInfo();

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    mode: 'onSubmit', // Validate on submit
    reValidateMode: 'onChange', // Re-validate on change after first submit
    defaultValues: {
      paymentOption: 'bank',
      beneficiaryName: '',
      bankAccountNumber: '',
      bankName: '',
      bankBranch: '',
      swiftCode: '',
      serviceProvider: '',
      momoRegisteredName: '',
      momoRegisteredNumber: '',
      frequency: 'monthly',
      minimumAmount: 50,
      automaticPayouts: false,
    },
  });

  const { control, handleSubmit, watch, formState, formState: { errors }, trigger, reset } = form;
  const paymentOption = watch('paymentOption');

  // Load existing data when available
  useEffect(() => {
    if (paymentInfoResponse?.data) {
      const { paymentInfo, payoutPreferences } = paymentInfoResponse.data;

      // Prepare form data with proper type checking and fallbacks
      const paymentOption = paymentInfo.paymentOption || 'bank';
      const frequency = payoutPreferences.frequency || 'monthly';

      // Ensure the frequency value is valid
      const validFrequency = ['weekly', 'monthly', 'bi-weekly'].includes(frequency)
        ? frequency as 'weekly' | 'monthly' | 'bi-weekly'
        : 'monthly';

      // Ensure the payment option is valid
      const validPaymentOption = ['bank', 'mobile_money'].includes(paymentOption)
        ? paymentOption as 'bank' | 'mobile_money'
        : 'bank';

      const formData = {
        paymentOption: validPaymentOption,
        beneficiaryName: paymentInfo.bankDetails?.accountName || '',
        bankAccountNumber: paymentInfo.bankDetails?.accountNumber || '',
        bankName: paymentInfo.bankDetails?.bankName || '',
        bankBranch: paymentInfo.bankDetails?.branchName || '',
        swiftCode: paymentInfo.bankDetails?.swiftCode || '',
        serviceProvider: paymentInfo.mobileMoneyDetails?.serviceProvider || '',
        momoRegisteredName: paymentInfo.mobileMoneyDetails?.registeredName || '',
        momoRegisteredNumber: paymentInfo.mobileMoneyDetails?.registeredNumber || '',
        frequency: validFrequency,
        minimumAmount: payoutPreferences.minimumAmount || 50,
        automaticPayouts: payoutPreferences.automaticPayouts || false,
      };

      // Reset the entire form with the new data - add small delay to ensure components are mounted
      setTimeout(() => {
        reset(formData);
      }, 50);
    }
  }, [paymentInfoResponse, reset]);

  const onSubmit = async (data: PaymentFormData) => {
    try {
      // Manually trigger validation to ensure all errors are caught
      const isValid = await trigger();

      if (!isValid) {
        // Get current errors after validation
        const currentErrors = Object.keys(formState.errors);
        const errorMessages = currentErrors.map(key => {
          const error = formState.errors[key as keyof PaymentFormData];
          return error?.message;
        }).filter(Boolean);

        toast({
          variant: 'destructive',
          title: 'Please Fix Form Errors',
          description: errorMessages.length > 1
            ? `${errorMessages.length} fields need attention: ${errorMessages[0]} and ${errorMessages.length - 1} more.`
            : errorMessages[0] || 'Please fill in all required fields correctly.',
        });

        return;
      }

      const updateData: PaymentInfoUpdateData = {
        paymentOption: data.paymentOption,
        payoutPreferences: {
          frequency: data.frequency,
          minimumAmount: data.minimumAmount,
          automaticPayouts: data.automaticPayouts,
        },
      };

      if (data.paymentOption === 'bank') {
        updateData.bankDetails = {
          accountName: data.beneficiaryName || '',
          accountNumber: data.bankAccountNumber || '',
          bankName: data.bankName || '',
          branchName: data.bankBranch || '',
          swiftCode: data.swiftCode || '',
        };
      } else {
        updateData.mobileMoneyDetails = {
          serviceProvider: data.serviceProvider || '',
          registeredName: data.momoRegisteredName || '',
          registeredNumber: data.momoRegisteredNumber || '',
        };
      }

      await updatePaymentInfoMutation.mutateAsync(updateData);
      onSuccess?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    }
  };

  if (isLoadingPaymentInfo) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <FormLayout>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-4">
        {/* Payment Method Selection */}
        <FormSection
          title="Preferred Payment Option"
          description="Select your preferred method of receiving payments."
        >
          <FormSelect
            label="Payment Method"
            name="paymentOption"
            control={control}
            errors={errors}
            placeholder="Select Payment Option"
            required
            options={[
              { value: 'bank', label: 'Bank Account' },
              { value: 'mobile_money', label: 'Mobile Money' }
            ]}
          />
          {/* Bank Account Details */}
          {paymentOption === "bank" && (
            <div className="mt-6 space-y-4">
              <h4 className="text-sm font-semibold text-gray-900 border-b border-gray-200 pb-2">Bank Account Information</h4>
              <div className="grid gap-4">
                <FormInput
                  label="Beneficiary Name"
                  name="beneficiaryName"
                  control={control}
                  errors={errors}
                  placeholder="Enter beneficiary name"
                  required
                />
                <FormInput
                  label="Bank Account Number"
                  name="bankAccountNumber"
                  control={control}
                  errors={errors}
                  placeholder="Enter account number"
                  required
                />
                <FormSelect
                  label="Bank Name"
                  name="bankName"
                  control={control}
                  errors={errors}
                  placeholder="Select your bank"
                  required
                  options={GHANA_BANKS.map(bank => ({ value: bank, label: bank }))}
                />
                <FormInput
                  label="Bank Branch"
                  name="bankBranch"
                  control={control}
                  errors={errors}
                  placeholder="Enter branch name"
                  description="Optional - specify if required by your bank"
                />
                <FormInput
                  label="SWIFT Code"
                  name="swiftCode"
                  control={control}
                  errors={errors}
                  placeholder="Enter SWIFT code"
                  description="Optional - for international transfers"
                />
              </div>
            </div>
          )}

          {/* Mobile Money Details */}
          {paymentOption === "mobile_money" && (
            <div className="mt-6 space-y-4">
              <h4 className="text-sm font-semibold text-gray-900 border-b border-gray-200 pb-2">Mobile Money Information</h4>
              <div className="grid gap-4">
                <FormSelect
                  label="Service Provider"
                  name="serviceProvider"
                  control={control}
                  errors={errors}
                  placeholder="Select Service Provider"
                  required
                  options={MOBILE_MONEY_PROVIDERS.map(provider => ({ value: provider, label: provider }))}
                />

                <FormInput
                  label="Registered Mobile Money Name"
                  name="momoRegisteredName"
                  control={control}
                  errors={errors}
                  placeholder="Enter registered name"
                  required
                  description="Name as registered with your mobile money provider"
                />
                <FormInput
                  label="Registered Mobile Money Number"
                  name="momoRegisteredNumber"
                  control={control}
                  errors={errors}
                  placeholder="Enter registered number"
                  required
                  description="Phone number registered with mobile money"
                />
              </div>
            </div>
          )}
        </FormSection>

        {/* Payout Preferences */}
        <FormSection
          title="Payout Preferences"
          description="Configure when and how you receive your payouts."
        >
          <div className="grid gap-4">
            <FormSelect
              label="Payout Frequency"
              name="frequency"
              control={control}
              errors={errors}
              placeholder="Select frequency"
              required
              options={[
                { value: 'weekly', label: 'Weekly' },
                { value: 'bi-weekly', label: 'Bi-weekly' },
                { value: 'monthly', label: 'Monthly' }
              ]}
              description="How often you want to receive payments"
            />

            <FormInput
              label="Minimum Payout Amount (GHS)"
              name="minimumAmount"
              control={control}
              errors={errors}
              placeholder="50"
              type="number"
              required
              description="Minimum amount before payout is processed"
            />

            <FormCheckbox
              label="Enable automatic payouts"
              name="automaticPayouts"
              control={control}
              description="Automatically process payouts when minimum amount is reached"
            />
          </div>
        </FormSection>

        {/* Form Actions */}
        <FormActions>
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
          >
            Back
          </Button>
          <Button
            type="submit"
            disabled={updatePaymentInfoMutation.isPending}
          >
            {updatePaymentInfoMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save & Continue'
            )}
          </Button>
        </FormActions>
      </form>
    </FormLayout>
  );
}
