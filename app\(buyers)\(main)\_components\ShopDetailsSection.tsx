import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ProductCard from '@/components/ui/custom/ProductCard';
import { products } from '@/lib/data';
import { FaShippingFast, FaStar } from 'react-icons/fa';
import { FaBoxOpen } from 'react-icons/fa6';
import Link from 'next/link';

interface ShopDetailsProps {
  id: string,
  shopName: string;
  sellerScore: string;
  numFollowers: number;
  performance: {
    shipping: string;
    quality: string;
    customerRating: string;
  };
}

const ShopDetailsSection: React.FC<ShopDetailsProps> = ({
  id,
  shopName,
  sellerScore,
  numFollowers,
  performance,
}) => {
  return (
    <section className="bg-white my-2 py-3 px-3 rounded shadow-sm">
      {/* Shop Name & Navigation */}
      <div className="flex justify-between items-center border-b pb-2">
        <h1 className="text-sm font-semibold text-gray-600">{shopName}</h1>
        <Link href={`/shops/${id}`}>
          <ChevronRight className="h-4 w-4 text-gray-500" />
        </Link>
      </div>

      {/* Seller Info */}
      <div className="mt-3 flex justify-between items-center text-sm">
        <div className="text-sm text-gray-600">
          <p>
            <span className="font-medium text-gray-600 mr-1">{sellerScore}</span> Seller Score
          </p>
          <p>
           <span className="font-medium text-gray-600 mr-1">{numFollowers}</span>  Followers
          </p>
        </div>
        <Button className="text-xs bg-primary hover:bg-primary/90 text-white px-3 py-1 rounded">
          Follow
        </Button>
      </div>

    {/* Seller Performance */}
    <div className="mt-4 border-t pt-2">
        <h2 className="text-sm font-semibold text-gray-700 mb-1">Seller Performance</h2>
        <div className="space-y-2 text-sm text-gray-600">
            <p className="flex items-center gap-2">
            <FaShippingFast className="text-green-600" />
            Shipping Score: <span className="font-medium text-green-600">{performance.shipping}</span>
            </p>
            <p className="flex items-center gap-2">
            <FaBoxOpen className="text-green-600" />
            Quality Score: <span className="font-medium text-green-600">{performance.quality}</span>
            </p>
            <p className="flex items-center gap-2">
            <FaStar className="text-yellow-500" />
            Customer Rating: <span className="font-medium text-yellow-600">{performance.customerRating}</span>
            </p>
        </div>
    </div>

      {/* Seller's Products */}
      <div className="mt-4">
        <h2 className="text-sm font-semibold text-gray-700 mb-2">More from this Seller</h2>
        <div className="grid grid-cols-2 gap-2">
          {products.slice(0, 4).map((product) => (
            <ProductCard key={product.id} product={product} cardType="normal" />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ShopDetailsSection;
