"use client";

import * as React from "react";
import { Eye, EyeOff } from "lucide-react";
import ButtonLoader from "@/components/ui/ButtonLoader";
import { useForm, Controller } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLogin } from "@/lib/hooks/use-auth";
import { useAuth, useAuthActions } from "@/lib/stores/auth-store";
import { UserRole } from "@/lib/types/auth";

// Zod Validation Schema
const loginSchema = z.object({
  email: z.string().email("Invalid email address").nonempty("Email is required."),
  password: z.string().min(6, "Password should be at least 6 characters long.").nonempty("Password is required."),
});

interface LoginFormProps {
  role?: UserRole;
}

// Login Form Component
const LoginForm = ({ role = 'buyer' }: LoginFormProps) => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const [showPassword, setShowPassword] = React.useState(false);
  const loginMutation = useLogin();
  const { isLoading, error } = useAuth();
  const { clearError } = useAuthActions();

  // Clear any existing errors when component mounts
  React.useEffect(() => {
    clearError();
  }, [clearError]);

  const onSubmit = (data: { email: string; password: string }) => {
    // Clear any existing errors before attempting login
    clearError();

    loginMutation.mutate({
      email: data.email,
      password: data.password,
      role,
    });
  };

  return (
    <div className="md:max-w-lg mx-auto w-11/12 py-4 bg-white rounded-lg space-y-4">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full">
        {/* Error Display */}
        {(error || loginMutation.error) && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error || (loginMutation.error as any)?.message || 'An error occurred during login'}
          </div>
        )}

        {/* Email Field */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Email</label>
          <div className="mt-2">
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="email"
                  placeholder="<EMAIL>"
                  className="w-full"
                  disabled={isLoading || loginMutation.isPending}
                />
              )}
            />
            {errors.email && <span className="text-red-500 text-sm">{errors.email.message}</span>}
          </div>
        </div>

        {/* Password Field with Toggle Visibility */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Password</label>
          <div className="relative mt-3">
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••"
                  className="w-full"
                  disabled={isLoading || loginMutation.isPending}
                />
              )}
            />
            <button
              type="button"
              className="absolute top-1/2 right-2 transform -translate-y-1/2"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading || loginMutation.isPending}
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          {errors.password && <span className="text-red-500 text-sm">{errors.password.message}</span>}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          size="lg"
          className="w-full h-12 text-lg"
          disabled={isLoading || loginMutation.isPending}
        >
          {isLoading || loginMutation.isPending ? (
            <ButtonLoader text="Logging in..." />
          ) : (
            'Login'
          )}
        </Button>

        {/* Forgot Password Link */}
        <div className="text-center mt-4">
          <Link
            href={role === 'creator' ? '/creators/forgot-password' : '/forgot-password'}
            className="text-sm text-primary hover:text-primary/80 transition-colors duration-200"
          >
            Forgot your password?
          </Link>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
