'use client';

import { useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuthActions } from '@/lib/stores/auth-store';
import { toast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

function AuthCallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { login, setError } = useAuthActions();

  useEffect(() => {
    const success = searchParams.get('success');
    const dataParam = searchParams.get('data');
    const error = searchParams.get('error');

    console.log('Auth callback params:', {
      success,
      data: dataParam ? 'Present' : 'Missing',
      error,
    });

    if (error || success !== 'true') {
      console.error('OAuth error:', error);
      setError('Sign-in failed. Please try again.');
      toast({
        variant: 'destructive',
        title: 'Sign-in Failed',
        description: 'Sign-in failed. Please try again.',
      });
      router.push('/login?error=oauth_failed');
      return;
    }

    if (dataParam) {
      try {
        // Decode and parse the auth response data
        const authResponse = JSON.parse(decodeURIComponent(dataParam));

        console.log('Received auth response:', authResponse);

        // Extract token and user from the response (same structure as normal auth)
        const { token, data } = authResponse;
        const user = data.user;

        if (!token || !user) {
          throw new Error('Missing token or user data in response');
        }

        console.log('Extracted user:', user);

        // User data already has the correct role and userType from backend
        // No need for role mapping since backend now sends 'buyer'/'creator' directly
        const mappedUser = {
          ...user,
          id: user.id || user._id, // Ensure id is set
        };

        console.log('Final user for login:', mappedUser);

        // Log the user in
        login(mappedUser, token);

        toast({
          title: 'Welcome!',
          description: `Successfully signed in with Google as ${mappedUser.name}.`,
          className: 'bg-green-100 text-green-800',
        });

        // Redirect based on user role
        const redirectPath = mappedUser.role === 'creator' ? '/creators' : '/';
        console.log('Redirecting to:', redirectPath);
        router.push(redirectPath);
      } catch (parseError) {
        console.error('Error parsing auth response:', parseError);
        setError('Failed to process sign-in data.');
        toast({
          variant: 'destructive',
          title: 'Sign-in Failed',
          description: 'Failed to process sign-in data.',
        });
        router.push('/login?error=parse_failed');
      }
    } else {
      console.error('Missing auth data');
      router.push('/login?error=missing_data');
    }
  }, [searchParams, login, setError, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <h2 className="text-lg font-semibold mb-2">Completing sign-in...</h2>
        <p className="text-gray-600">Please wait while we complete your Google sign-in.</p>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h2 className="text-lg font-semibold mb-2">Loading...</h2>
          <p className="text-gray-600">Please wait...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}
