'use client';

import { Suspense } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { FaStar, FaRegStar } from 'react-icons/fa';
import { Progress } from '@/components/ui/progress';

interface Review {
  id: number;
  user: string;
  title: string;
  comment: string;
  rating: number;
  date: string;
}

interface RatingsBreakdown {
  [key: number]: number;
}

const mockReviews: Review[] = [
  { id: 1, user: 'Alice', title: 'Great Product', comment: 'Loved it!', rating: 5, date: '2024-02-01' },
  { id: 2, user: 'Bob', title: 'Good Value', comment: 'Worth the price.', rating: 4, date: '2024-01-15' },
  { id: 3, user: 'Charlie', title: 'Okay', comment: 'It was okay.', rating: 3, date: '2024-01-10' },
  { id: 4, user: '<PERSON>', title: 'Not great', comment: 'Expected better.', rating: 2, date: '2024-01-05' },
  { id: 5, user: 'Eve', title: 'Terrible', comment: 'Worst product ever.', rating: 1, date: '2024-01-02' },
  { id: 6, user: '<PERSON>', title: 'Fantastic!', comment: 'Highly recommended.', rating: 5, date: '2024-01-25' },
];

const ratingsBreakdown: RatingsBreakdown = {
  5: 1,
  4: 1,
  3: 1,
  2: 1,
  1: 1,
};


const CommentsContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const reviewsPerPage = 5;
  const totalReviews = mockReviews.length;
  const totalPages = Math.ceil(totalReviews / reviewsPerPage);
  const averageRating = 4.2; // Mocked for demonstration

  const paginatedReviews = mockReviews.slice(
    (currentPage - 1) * reviewsPerPage,
    currentPage * reviewsPerPage
  );

  const updatePage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    router.push(`?${params.toString()}`);
  };

  const hasReviews = totalReviews > 0;

  return (
    <section className="w-full mt-20  ">
      {/* Verified Ratings Summary */}
       
        <h1 className="uppercase font-medium text-gray-600 mb-1 ">
            Verified Ratings ({totalReviews})
        </h1>
      <div className=" px-3 py-2 bg-white rounded shadow-sm">
        <div className="flex justify-between items-center">
          {/* Left Section - Average Rating */}
          <div className="flex flex-col items-center">
            <p className="text-3xl font-bold text-gray-600">{averageRating.toFixed(1)} / 5</p>
            <div className="flex gap-1 mt-1">
              {Array.from({ length: 5 }, (_, i) =>
                i < Math.round(averageRating) ? (
                  <FaStar key={i} className="text-yellow-400" />
                ) : (
                  <FaRegStar key={i} className="text-gray-300" />
                )
              )}
            </div>
            <p className="text-sm text-gray-500 mt-1">{totalReviews} Verified Ratings</p>
          </div>

          {/* Right Section - Ratings Breakdown */}
          <div className="w-2/3">
            {Object.entries(ratingsBreakdown)
              .sort((a, b) => Number(b[0]) - Number(a[0]))
              .map(([stars, count]) => (
                <div key={stars} className="flex items-center mb-1">
                  <div className="flex items-center w-12">
                    <FaStar className="text-yellow-400 text-xs" />
                    <span className="text-sm ml-1">{stars}</span>
                  </div>
                  <Progress
                    value={(count / totalReviews) * 100}
                    className="w-full h-2 "
                  />
                  <span className="text-xs text-gray-600 ml-2">({count})</span>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/*  Comments from Verified Purchases Section */}
      <h1 className="uppercase font-medium text-gray-600 mb-1 mt-4 ">
            Comments from Verified Purchases ({totalReviews})
       </h1>
      <div className="mt-4">
        {hasReviews ? (
          <div className="space-y-4">
            {paginatedReviews.map((review) => (
              <div
                key={review.id}
                className="border-b pb-4 last:border-b-0 bg-gray-50 p-3 rounded"
                >
                <div className="flex justify-between items-center mb-1">
                    <div className="flex items-center gap-1">
                    {Array.from({ length: review.rating }, (_, i) => (
                        <FaStar key={i} className="text-yellow-400 text-xs" />
                    ))}
                    {Array.from({ length: 5 - review.rating }, (_, i) => (
                        <FaRegStar key={i} className="text-gray-300 text-xs" />
                    ))}
                    </div>
                    <p className="text-xs text-gray-500">{review.date}</p>
                </div>
                <p className="text-sm font-bold text-gray-800">{review.title}</p>
                <p className="text-sm text-gray-700 mt-1">{review.comment}</p>
                <p className="text-xs text-gray-500 mt-1">By {review.user}</p>
              </div>     
            ))}
          </div>
        ) : (
          <div className="text-center py-10 text-gray-500">
            <p className="text-sm">No reviews yet. Be the first to review this product!</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {hasReviews && (
        <div className="flex justify-between items-center mt-4">
          <button
            onClick={() => updatePage(Math.max(currentPage - 1, 1))}
            className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
            disabled={currentPage === 1}
          >
            Previous
          </button>

          <span className="text-sm font-semibold">
            Page {currentPage} of {totalPages}
          </span>

          <button
            onClick={() => updatePage(Math.min(currentPage + 1, totalPages))}
            className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </section>
  );
};

const CommentsPage = () => {
  return (
    <Suspense fallback={<div>Loading comments...</div>}>
      <CommentsContent  />
    </Suspense>
  );
};

export default CommentsPage;
