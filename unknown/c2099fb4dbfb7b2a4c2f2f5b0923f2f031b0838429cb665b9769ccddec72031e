"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import StepNavigation from "../_components/StepNavigation";
import { toast } from "@/hooks/use-toast";

// Specification form data structure
interface SpecificationsFormData {
  mainMaterial: string;
  dressStyle: string;
  pantType: string;
  skirtType: string;
  menPantSize: string;
  sleeveLength: string;
  fitType: string;
  sizeConversion: string;
  gender: string;
  ageGroup: string;
  additionalNotes: string;
  country: string
}

const initialFormData: SpecificationsFormData = {
  mainMaterial: "",
  dressStyle: "",
  pantType: "",
  skirtType: "",
  menPantSize: "",
  sleeveLength: "",
  fitType: "",
  sizeConversion: "",
  gender: "",
  ageGroup: "",
  additionalNotes: "",
  country: "",
};

const popularCountries = [
  "United Kingdom",
  "United States",
  "Turkey",
  "Canada",
  "Italy",
  "Nigeria",
  "Ghana",
  "Kenya",
  "South Africa",
  "Uganda",
  "Tanzania",
  "Ethiopia",
  "Rwanda",
  "Zambia",
  "Cameroon",
  "Côte d'Ivoire",
  "Senegal",
  "China",
  "UAE",
  "India",
];


export default function SpecificationsPage() {
  const [formData, setFormData] = useState<SpecificationsFormData>(initialFormData);

  const handleChange = (field: keyof SpecificationsFormData, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  

  
  const handleFormSubmit = () => {
    console.log("Form submitted:", formData);

    // Send success toast
    toast({
      title: "Success!",
      description: "Product specifications saved successfully."
    });
  };


  return (
    <div className="w-full ">
      <section className="bg-white mt-2  px-2 py-4 rounded-lg shadow-sm">
        <h2 className="text-sm uppercase font-bold  mb-3">Product Specifications</h2>

        <main  className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Main Material */}
          <div>
            <label className="block text-sm font-medium mb-1">Main Material</label>
            <Input
              value={formData.mainMaterial}
              onChange={(e) => handleChange("mainMaterial", e.target.value)}
              placeholder="e.g., Cotton, Ankara"
            />
          </div>

          {/* Dress Style */}
          <div>
            <label className="block text-sm font-medium mb-1">Dress Style</label>
            <Select onValueChange={(value) => handleChange("dressStyle", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select dress style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="maxi">Maxi Dress</SelectItem>
                <SelectItem value="midi">Midi Dress</SelectItem>
                <SelectItem value="bodycon">Bodycon Dress</SelectItem>
                <SelectItem value="wrap">Wrap Dress</SelectItem>
                <SelectItem value="kaftan">Kaftan</SelectItem>
                <SelectItem value="kitenge">Kitenge Dress</SelectItem>
                <SelectItem value="off-shoulder">Off-Shoulder Dress</SelectItem>
                <SelectItem value="shirt-dress">Shirt Dress</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Pant Type */}
          <div>
            <label className="block text-sm font-medium mb-1">Pant Type</label>
            <Select onValueChange={(value) => handleChange("pantType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select pant type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="jeans">Jeans</SelectItem>
                <SelectItem value="chinos">Chinos</SelectItem>
                <SelectItem value="cargo">Cargo Pants</SelectItem>
                <SelectItem value="joggers">Joggers</SelectItem>
                <SelectItem value="formal">Formal Trousers</SelectItem>
                <SelectItem value="shorts">Shorts</SelectItem>
                <SelectItem value="palazzo">Palazzo Pants</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Skirt Type */}
          <div>
            <label className="block text-sm font-medium mb-1">Skirt Type</label>
            <Select onValueChange={(value) => handleChange("skirtType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select skirt type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pencil">Pencil Skirt</SelectItem>
                <SelectItem value="maxi">Maxi Skirt</SelectItem>
                <SelectItem value="midi">Midi Skirt</SelectItem>
                <SelectItem value="pleated">Pleated Skirt</SelectItem>
                <SelectItem value="wrap">Wrap Skirt</SelectItem>
                <SelectItem value="a-line">A-Line Skirt</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Men's Pant Sizes */}
          <div>
            <label className="block text-sm font-medium mb-1">Men's Pant Sizes</label>
            <Select onValueChange={(value) => handleChange("menPantSize", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select pant size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="28">28</SelectItem>
                <SelectItem value="30">30</SelectItem>
                <SelectItem value="32">32</SelectItem>
                <SelectItem value="34">34</SelectItem>
                <SelectItem value="36">36</SelectItem>
                <SelectItem value="38">38</SelectItem>
                <SelectItem value="40">40</SelectItem>
                <SelectItem value="42">42</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Fit Type */}
          <div>
            <label className="block text-sm font-medium mb-1">Fit Type</label>
            <Select onValueChange={(value) => handleChange("fitType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select fit type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="loose-fit">Loose Fit</SelectItem>
                <SelectItem value="tailored-fit">Tailored Fit</SelectItem>
                <SelectItem value="bodycon">Bodycon</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Gender */}
          <div>
            <label className="block text-sm font-medium mb-1">Gender</label>
            <Select onValueChange={(value) => handleChange("gender", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="men">Men</SelectItem>
                <SelectItem value="women">Women</SelectItem>
                <SelectItem value="unisex">Unisex</SelectItem>
              </SelectContent>
            </Select>
          </div>

        

          {/* Additional Notes */}
          <div className="col-span-1 md:col-span-2">
            <label className="block text-sm font-medium mb-1">Additional Notes</label>
            <Textarea
              value={formData.additionalNotes}
              onChange={(e) => handleChange("additionalNotes", e.target.value)}
              placeholder="Any special care tips or custom options..."
            />
          </div>

         
        </main>
      </section>
     
      <section className="w-full mt-6">
        <StepNavigation onSubmit={handleFormSubmit} />
      </section>
    </div>
  );
}
