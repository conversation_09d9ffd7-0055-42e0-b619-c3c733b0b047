import { useState } from "react";
import DateTimePicker from "react-datetime-picker";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";

interface DateTimePickerProps {
  value?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

export const DateTimePickerComponent = ({ value, onChange, disabled }: DateTimePickerProps) => {
  const [dateTime, setDateTime] = useState<Date | null>(value ? new Date(value) : null);

  const handleChange = (newValue: Date | null) => {
    setDateTime(newValue);
    if (newValue) {
      onChange(newValue.toISOString()); // You can format this however you like
    } else {
      onChange('');
    }
  };

  return (
    <div className="w-full">
      <DateTimePicker
        onChange={handleChange}
        value={dateTime}
        disabled={disabled}
        format="y-MM-dd h:mm a"
        className="w-full border rounded-lg p-2"
      />
    </div>
  );
};
