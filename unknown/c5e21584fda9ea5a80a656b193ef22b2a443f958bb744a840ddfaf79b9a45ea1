import CarouselBanner from '@/app/(buyers)/(main)/_components/CarouselBanner'
import CarouselComponent from '@/app/(buyers)/(main)/_components/CarouselComponent'
import React from 'react'


// Categories header
// Search recommendations header
// Categories with discounts
// Discounts with a timer -red

const SectionHeader = () => {
  return (
    <section className='w-full mt-6'>


        <section className="w-full bg-primary py-4 px-3 text-white">
          <div className="flex justify-between items-center  rounded-sm ">
            <h1 className="md:text-xl text-base font-bold">Top Deals <span className='hidden md:inline'>| Men's Wear</span></h1>
              <button className="text-sm font-bold">View All</button>
          </div>
          <h1 className="text-sm md:hidden">Men's Sneakers</h1>
        </section>
          
        <div className="h-72">

        </div>

        <section className="w-full bg-primary py-4 px-3 text-white">
          <div className="flex justify-between items-center  rounded-sm ">
            <h1 className="md:text-xl text-base font-bold">Top Deals <span className='hidden md:inline'>| Men's Wear</span></h1>
              <button className="text-sm font-bold">View All</button>
          </div>
          <h1 className="text-sm md:hidden">Men's Jewellry</h1>
        </section>
          
        <div className="h-72">

        </div>

        
    </section>
  )
}

export default SectionHeader
