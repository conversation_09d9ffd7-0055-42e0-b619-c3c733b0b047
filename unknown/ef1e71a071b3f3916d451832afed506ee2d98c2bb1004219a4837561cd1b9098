import ProductDetailsCarousel from '@/app/(buyers)/(main)/_components/ProductDetailsCarousel'
import ProductDetailsComponent from '@/app/(buyers)/(main)/_components/ProductDetailsComponent';
import Image from 'next/image';
import React from 'react'


const product = {
    id: 201,
    name: "Men's Slim Fit Casual Blazer - Navy Blue",
    brand: "Zara",
    category: "Men's Fashion",
    price: 350,
    discount: 20, // percentage
    images: [
      "/images/ladies/dress1.jpg",
      "/images/ladies/dress2.jpg",
      "/images/ladies/dress3.jpg",
      "/images/ladies/dress4.jpg",
    ],
    description: `
      Elevate your style with this Men's Slim Fit Casual Blazer from Zara. Designed for versatility, 
      this blazer is perfect for both formal occasions and smart-casual outings. 
      Made from high-quality fabric, it ensures comfort without compromising on style.
    `,
    specifications: {
      "Material": "65% Polyester, 35% Viscose",
      "Fit": "Slim Fit",
      "Color": "Navy Blue",
      "Closure": "Single-button",
      "Lining": "Fully lined",
      "Pockets": "2 front flap pockets, 1 chest pocket, 2 inner pockets",
      "Care Instructions": "Dry clean only",
      "Occasion": "Business, Casual, Formal Events"
    },
    availableSizes: ["XS", "S", "M", "L", "XL", "XXL"],
    availableColors: ["Navy Blue", "Black", "Gray"],
    ratings: 4.3,
    reviews: [
      {
        user: "John D.",
        rating: 5,
        comment: "Fits perfectly and the fabric feels premium. Highly recommend!"
      },
      {
        user: "Michael O.",
        rating: 4,
        comment: "Great blazer but the sleeves were a bit long for me."
      }
    ],
    stock: 15,
    shippingInfo: {
      "Delivery": "Free delivery within Accra in 1-2 business days",
      "Return Policy": "Return within 7 days if unworn and with tags intact"
    },
    sellerInfo: {
      name: "FashionHub GH",
      rating: 4.8,
      location: "Accra, Ghana",
      verified: true
    },
    relatedProducts: [
      { id: 202, name: "Men's Formal Trousers - Black", price: 150 },
      { id: 203, name: "Men's White Dress Shirt - Slim Fit", price: 120 }
    ]
  };

const unavailableSizes = ['S', 'M'];

const Product = () => {
  return (
    <section className="w-full mt-20">
        <ProductDetailsCarousel>
        {product.images.map((image, index) => (
            <section 
            key={index}
            className="h-[50vh] w-full flex justify-center items-center bg-white rounded-xl shadow-md overflow-hidden"
            >
            <Image 
                src={image} 
                alt={`Product image ${index + 1}`} 
                width={500} 
                height={500} 
                className="object-contain h-full w-auto"
            />
            </section>
        ))}
        </ProductDetailsCarousel>

        <ProductDetailsComponent
          product={product}
          unavailableSizes={unavailableSizes}
        />
    </section>
  )
}

export default Product