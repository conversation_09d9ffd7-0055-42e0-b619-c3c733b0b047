"use client"
import React from 'react';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { fashionCategories } from '@/lib/navlinks';
import SubCategoriesCarousel from '@/app/(buyers)/(main)/_components/SubCategoriesCarousel';

export default function CategoryLayout({ children }: { children: React.ReactNode }) {
  const params = useParams();
  const pathname = usePathname();
  const { mainCategory } = params;

  // Find the main category data
  const category = fashionCategories.find(cat => cat.headerLink.split('/').pop() === mainCategory);

  if (!category) {
    return <div>Category not found</div>;
  }

  return (
    <div className="flex flex-col min-h-screen w-full px-2 md:px-10 lg:px-20 pb-6 bg-gray-100">
      {/* Carousel with Subcategories */}
      <section className="w-full bg-gray-100 p-2 mt-20">
          <SubCategoriesCarousel>
            {category.links.map((link) => {
              const linkPath = `${link.route}`;
              const isActive = pathname === linkPath;  
              
              return (
                <Link
                  key={link.route}
                  href={linkPath}
                  className={`w-full mr-2 rounded-lg transition '
                  }`}
                >
                  <div className="mr-2" key={link.route}>
                    <div className="relative w-full h-20 ">
                        {('image' in link) && link.image && (
                          <img
                            src={link.image}
                            alt={link.label}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        )}

                        <div className="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-center rounded-lg ">
                          <h1 className=""></h1>
                        </div>
                        <h2 className="text-sm text-center ">{link.label}</h2>
                    </div>
                  </div>

                </Link>
              );
            })}
          </SubCategoriesCarousel>
      </section>

      {/* Main Content */}
      <main className="w-full">
        {children}
      </main>
    </div>
  );
}
