"use client"
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

// Mock product data
const shipmentProducts = [
  {
    id: '1',
    name: 'Fashion Ladies Dress Women Casual Half Sleeve',
    image: '/images/ladies/dress1.jpg',
    quantity: 1,
  },
  {
    id: '2',
    name: 'Ladies Skirts',
    image: '/images/ladies/skirts.jpg',
    quantity: 2,
  },
  {
    id: '3',
    name: 'Ladies Jeans',
    image: '/images/ladies/jeans.jpg',
    quantity: 1,
  },
];

const ShipmentSection = () => {
  const router = useRouter();
  return (
    <section className=''>
        <h2 className="font-medium text-gray-700 mb-1">SHIPMENT DETAILS</h2>

        <section className="bg-white px-2 py-2 mb-4 rounded-lg shadow-sm">
        {/* Shipment Card */}
        <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg space-y-4">
            {/* Shipment Info */}
            <div className="space-y-1">
            <p className="text-sm font-medium text-gray-700">Shipment 1 of 3</p>
            <p className="text-sm text-gray-800">Door Delivery</p>
            <p className="text-sm text-gray-500">
                Delivery between <span className="font-medium text-gray-700">Feb 20</span> and{' '}
                <span className="font-medium text-gray-700">Feb 22</span>
            </p>
            </div>

            {/* Product List */}
            <div className="space-y-3 border-t pt-3">
            {shipmentProducts.map((product) => (
                <div key={product.id} className="flex items-start gap-3">
                <Image
                    src={product.image}
                    alt={product.name}
                    width={70}
                    height={70}
                    className="rounded object-cover bg-gray-100"
                />
                <div className="flex-1">
                    <p className="text-sm font-medium text-gray-800 leading-tight">{product.name}</p>
                    <p className="text-xs text-gray-500">Qty: {product.quantity}</p>
                </div>
                </div>
            ))}
            </div>

            {/* Modify Cart */}
            <Button
            variant="outline"
            className="w-full text-primary border-primary/20 hover:bg-primary/10 transition-colors duration-200"
            onClick={()=>router.push('/cart')}
            >
            Modify Cart
            </Button>
        </div>
        </section>
    </section>

  );
};

export default ShipmentSection;
