import WishlistContent from "@/components/ui/custom/WishlistContent";

export const revalidate= 0;

interface WishlistPageProps {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

const WishlistPage: React.FC<WishlistPageProps> = async({ searchParams }) => {
  const searchParamsList = await searchParams;
  const currentPage = parseInt(searchParamsList.page || '1', 10);

  return (
    <WishlistContent currentPage={currentPage}  />
  );
};

export default WishlistPage;
