import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import { products } from '@/lib/data';
import ProductCard from '@/components/ui/custom/ProductCard';
import Link from 'next/link';


const WishListComponent = () => {
  return (
    <section className='bg-white my-2 py-3 px-3 rounded shadow-sm'>
      <div className="flex justify-between">
        <h1 className="text-sm font-semibold text-gray-600 pb-2">Wishlist(8)</h1>
        <Link href='/wishlist' className="text-sm text-gray-600 font-bold">See All</Link>
      </div>
      
      <ProductsCarousel>
        {products.map((product) => (
          <div key={product.id} className="p-1">
            <ProductCard product={product} cardType='normal' />
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default WishListComponent;
