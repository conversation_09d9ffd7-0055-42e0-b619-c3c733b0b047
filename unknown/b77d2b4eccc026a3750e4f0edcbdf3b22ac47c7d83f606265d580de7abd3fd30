"use client";
import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { TrashIcon } from "lucide-react";


// Product type
interface Product {
  id: number;
  name: string;
  image: string;
  currentPrice: number;
  discount: number;
  promoPrice: number;
  promoStock: number;
  status: "Pending" | "Approved" | "Rejected";
}

// Dummy data
const submittedProducts: Product[] = [
  {
    id: 1,
    name: "Nice Dress",
    image: "/images/ladies/dress1.jpg",
    currentPrice: 250,
    discount: 0,
    promoPrice: 250,
    promoStock: 50,
    status: "Approved",
  },
  {
    id: 2,
    name: "Lovely Jacket",
    image: "/images/ladies/jacket.jpg",
    currentPrice: 800,
    discount: 10,
    promoPrice: 720,
    promoStock: 30,
    status: "Pending",
  },
  {
    id: 3,
    name: "<PERSON> Shorts",
    image: "/images/ladies/short.jpg",
    currentPrice: 3500,
    discount: 0,
    promoPrice: 3500,
    promoStock: 20,
    status: "Rejected",
  },
  {
    id: 4,
    name: "<PERSON> Dress",
    image: "/images/ladies/dress1.jpg",
    currentPrice: 250,
    discount: 0,
    promoPrice: 250,
    promoStock: 50,
    status: "Approved",
  },
  {
    id: 5,
    name: "Lovely Jacket",
    image: "/images/ladies/jacket.jpg",
    currentPrice: 800,
    discount: 10,
    promoPrice: 720,
    promoStock: 30,
    status: "Pending",
  },
  {
    id: 6,
    name: "Nice Shorts",
    image: "/images/ladies/short.jpg",
    currentPrice: 3500,
    discount: 0,
    promoPrice: 3500,
    promoStock: 20,
    status: "Rejected",
  },
  {
    id: 7,
    name: "Nice Dress",
    image: "/images/ladies/dress1.jpg",
    currentPrice: 250,
    discount: 0,
    promoPrice: 250,
    promoStock: 50,
    status: "Approved",
  },
  {
    id: 8,
    name: "Lovely Jacket",
    image: "/images/ladies/jacket.jpg",
    currentPrice: 800,
    discount: 10,
    promoPrice: 720,
    promoStock: 30,
    status: "Pending",
  },
  {
    id: 9,
    name: "Nice Shorts",
    image: "/images/ladies/short.jpg",
    currentPrice: 3500,
    discount: 0,
    promoPrice: 3500,
    promoStock: 20,
    status: "Rejected",
  },
  {
    id: 10,
    name: "Nice Dress",
    image: "/images/ladies/dress1.jpg",
    currentPrice: 250,
    discount: 0,
    promoPrice: 250,
    promoStock: 50,
    status: "Approved",
  },
  {
    id: 11,
    name: "Lovely Jacket",
    image: "/images/ladies/jacket.jpg",
    currentPrice: 800,
    discount: 10,
    promoPrice: 720,
    promoStock: 30,
    status: "Pending",
  },
  {
    id: 12,
    name: "Nice Shorts",
    image: "/images/ladies/short.jpg",
    currentPrice: 3500,
    discount: 0,
    promoPrice: 3500,
    promoStock: 20,
    status: "Rejected",
  },
];

const SubmittedProductsPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>(submittedProducts);

  // Handle input changes (discount + promo stock)
  const handleInputChange = (id: number, field: keyof Product, value: string) => {
    const updatedProducts = products.map((product) => {
      if (product.id === id) {
        const updatedProduct = { ...product, [field]: value };

        if (field === "discount") {
          const discount = parseFloat(value) || 0;
          const promoPrice = product.currentPrice - (product.currentPrice * discount) / 100;
          updatedProduct.promoPrice = parseFloat(promoPrice.toFixed(2));
        }

        return updatedProduct;
      }
      return product;
    });

    setProducts(updatedProducts);
  };

  // Handle delete (only for "Pending" products)
  const handleDelete = (id: number) => {
    const updatedProducts = products.filter((product) => product.id !== id);
    setProducts(updatedProducts);
  };

  return (
    <section className="w-full mx-auto py-6">
      <h1 className="text-sm font-bold text-gray-700 mb-2">
        Summer Sale Promotion
      </h1>

      <h2 className="text-sm  text-gray-600 mb-2">
        Submitted Products
      </h2>

      <div className="overflow-x-auto bg-white shadow-md rounded-lg border border-gray-200">
        <Table className="w-full table-auto">
          <TableHeader>
            <TableRow>
              <TableHead className="w-auto whitespace-nowrap">Product</TableHead>
              <TableHead className="w-auto whitespace-nowrap  text-center">Current Price</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Discount (%)</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Promo Price (GH₵)</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Promo Stock</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Status</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.length > 0 ? (
              products.map((product) => (
                <TableRow
                  key={product.id}
                  className="space-x-4 transition-colors"
                >
                  <TableCell className="w-auto whitespace-nowrap ">
                    <div className="flex items-center space-x-2 mr-4">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-8 h-8 object-cover rounded-md"
                      />
                      <span className="font-medium text-gray-800 ">
                        {product.name}
                      </span>
                    </div>
                  </TableCell>

                  <TableCell className="text-center">
                    GH₵{product.currentPrice}
                  </TableCell>

                  <TableCell className="text-center">
                    <Input
                      type="number"
                      value={product.discount}
                      disabled={product.status !== "Pending"}
                      onChange={(e) =>
                        handleInputChange(product.id, "discount", e.target.value)
                      }
                      className={`text-center px-0 ${
                        product.status !== "Pending" ? "bg-gray-100" : ""
                      }`}
                    />
                  </TableCell>

                  <TableCell className="text-center">
                    <Input
                      type="text"
                      value={product.promoPrice}
                      disabled={true}
                      className="text-center px-0 bg-gray-100"
                    />
                  </TableCell>

                  <TableCell className="text-center">
                    <Input
                      type="number"
                      value={product.promoStock}
                      disabled={product.status !== "Pending"}
                      onChange={(e) =>
                        handleInputChange(product.id, "promoStock", e.target.value)
                      }
                      className={`text-center px-0 ${
                        product.status !== "Pending" ? "bg-gray-100" : ""
                      }`}
                    />
                  </TableCell>

                  <TableCell className="text-center">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        product.status === "Approved"
                          ? "text-green-600"
                          : product.status === "Pending"
                          ? " text-yellow-600"
                          : " text-red-600"
                      }`}
                    >
                      {product.status}
                    </span>
                  </TableCell>

                  <TableCell className="text-center">
                    <button
                      onClick={() => handleDelete(product.id)}
                      disabled={product.status !== "Pending"}
                      className={`${
                        product.status === "Pending"
                          ? "text-red-500 hover:text-red-700"
                          : "text-gray-400 cursor-not-allowed"
                      }`}
                    >
                      <TrashIcon className="h-5 w-5 inline" />
                    </button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                  No products submitted for this promotion.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </section>
  );
};

export default SubmittedProductsPage;
