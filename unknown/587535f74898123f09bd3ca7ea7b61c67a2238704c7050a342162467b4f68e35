import React from 'react'
import CarouselComponent from '../_components/CarouselComponent'
import SectionHeader from '@/components/ui/custom/SectionHeader'
import FlashSalesComponent from '../_components/FlashSalesComponent'
import CarouselBanner from '../_components/CarouselBanner'
import SponsoredProductsComponent from '../_components/SponsoredProductsComponent'
import BestDealsComponent from '../_components/BestDealsComponent'
import AdsComponent from '../_components/AdsComponent'
import TopDealsComponent from '../_components/TopDealsComponent'
import ShoppingCategoriesComponent from '../_components/ShopCategoriesComponent'
import TopFashionBrands from '../_components/TopFashionBrands'

const HomePage = () => {
  return (
    <section className='w-full mt-20'>
        {/* Hero Carousel, would change it later */}
        <CarouselComponent >
          <CarouselBanner/>
          <CarouselBanner/>
          <CarouselBanner/>
          <CarouselBanner/>
        </CarouselComponent>

        <FlashSalesComponent/>
        <SponsoredProductsComponent/>
        <BestDealsComponent/>
        <AdsComponent />
        <TopDealsComponent category="Fashion Bales" link="/categories/fashion-bales"/>
        <TopDealsComponent category="Women's Dresses" link="/categories/womens-clothing/womens-dresses"/>
        <TopDealsComponent category=" Women's Tops & Blouses" link="/categories/womens-clothing/womens-tops"/>
        <ShoppingCategoriesComponent />
        <TopFashionBrands />
        <TopDealsComponent category="Men's Shirts" link="/categories/mens-clothing/mens-shirts"/>
        <TopDealsComponent category=" Men's Executive Wear" link="/categories/mens-clothing/mens-suits" />
    </section>
  )
}

export default HomePage