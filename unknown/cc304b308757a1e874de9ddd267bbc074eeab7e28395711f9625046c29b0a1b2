import { useState } from "react";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select"; // Adjust import based on your UI library
import { TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";

interface Order {
  id: string;
  status: string;
  deliveryType: string;
}

interface OrderStatusCellProps {
  order: Order;
  onStatusChange: (orderId: string, newStatus: string) => void;
  onCancelOrder: (orderId: string) => void;
  isCanceled: any;
}

const OrderStatusCell: React.FC<OrderStatusCellProps> = ({ order, onStatusChange, onCancelOrder, isCanceled}) => {
  const [isConfirmed, setIsConfirmed] = useState(false);

  return (
    <TableCell className="text-center">
      <section className="flex gap-4 items-center">
        {!isConfirmed && !isCanceled ? (
          <>
            <Button
                variant={"outline"}
              className="bg-primary hover:bg-primary/90 text-primary-foreground transition-colors duration-200"
              onClick={() => setIsConfirmed(true)}
            >
              Confirm
            </Button>
            <Button
            variant={'destructive'}
            onClick={() => onCancelOrder(order.id)}
            >
              Cancel
            </Button>
          </>
        ) : isCanceled ? (
          <Button  className="bg-gray-600 text-white px-3 py-1 rounded-md cursor-not-allowed" disabled>
            Canceled
          </Button>
        ) : (
          <Select onValueChange={(value) => onStatusChange(order.id, value)}>
            <SelectTrigger className="bg-gray-100 rounded px-2 py-1">
              <SelectValue placeholder={order.status} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Processing">Processing</SelectItem>
              <SelectItem value="Shipped">Shipped</SelectItem>
            </SelectContent>
          </Select>
        )}
      </section>
    </TableCell>
  );
};

export default OrderStatusCell;
