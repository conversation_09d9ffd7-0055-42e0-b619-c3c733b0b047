import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';
import { BsMegaphone, BsPeople, BsChatDots, BsPerson, BsGear, BsBox, BsTag } from 'react-icons/bs';
import { FiUsers } from 'react-icons/fi';
import { HiOutlineChartSquareBar } from 'react-icons/hi';
import { MdOutlineInventory2 } from 'react-icons/md';

export const creatorMenuLinks = [
  // {
  //   label: 'Marketing Tools',
  //   icon: <BsMegaphone size={20} className="text-gray-700" />,
  //   link: '/creators/marketing-tools',
  // },
  {
    label: 'Bales',
    icon: <BsBox  size={20} className="text-gray-700 hover:text-white" />,
    link: '/creators/bales',
  },
  {
    label: 'Promotions',
    icon: <BsTag size={20} className="text-gray-700 hover:text-white" />, // Added Promotions
    link: '/creators/promotions',
  },
  {
    label: 'Give us your feedback',
    icon: <BsChatDots size={20} className="text-gray-700 hover:text-white" />,
    link: '/creators/feedback',
  },
  {
    label: 'Profile',
    icon: <BsPerson size={20} className="text-gray-700 hover:text-white" />,
    link: '/creators/profile',
  },
  // {
  //   label: 'Settings',
  //   icon: <BsGear size={20} className="text-gray-700 hover:text-white" />,
  //   link: '/creators/settings',
  // },
];

export const adminMenuLinks = [
    {
      label: "Products",
      icon: <MdOutlineInventory2 size={20} className="text-gray-700" />,
      link: "/admin/products",
    },
    {
      label: "Bales", // If admins manage bulk inventory too
      icon: <BsBox size={20} className="text-gray-700" />,
      link: "/admin/bales",
    },
    {
      label: "Promotions",
      icon: <BsTag size={20} className="text-gray-700" />,
      link: "/admin/promotions",
    },
    {
      label: "Customers", // Admin-specific
      icon: <FiUsers size={20} className="text-gray-700" />,
      link: "/admin/customers",
    },
    // {
    //   label: "Reports", // Analytics & insights
    //   icon: <HiOutlineChartSquareBar size={20} className="text-gray-700" />,
    //   link: "/admin/reports",
    // },
    {
      label: "Feedback",
      icon: <BsChatDots size={20} className="text-gray-700" />,
      link: "/admin/feedback",
    },
    {
      label: "Profile",
      icon: <BsPerson size={20} className="text-gray-700" />,
      link: "/admin/profile",
    },
    // {
    //   label: "Settings",
    //   icon: <BsGear size={20} className="text-gray-700" />,
    //   link: "/admin/settings",
    // },
  ];
  