import { TabsTrigger } from "@/components/ui/tabs";

interface CustomTabsTriggerProps {
  value: string;
  children: React.ReactNode;
}

export default function CustomTabsTrigger({ value, children }: CustomTabsTriggerProps) {
  return (
    <TabsTrigger
      value={value}
      className="rounded-2xl text-sm border border-primary/20 px-3 bg-primary/90 hover:bg-primary text-primary-foreground transition-colors duration-200 data-[state=active]:border-0 data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:shadow-sm font-poppins"
    >
      {children}
    </TabsTrigger>
  );
}
