"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { IoIosNotificationsOutline } from "react-icons/io";

interface NotificationButtonProps {
  unreadCount?: number; // Optional prop for unread notifications
}

const NotificationButton: React.FC<NotificationButtonProps> = ({ unreadCount = 0 }) => {
  const router = useRouter();

  return (
    <button
      onClick={() => router.push("/notifications")}
      className="relative p-2 rounded-full hover:bg-gray-200 focus:outline-none transition"
      aria-label="Notifications"
    >
      <IoIosNotificationsOutline size={26} className="text-gray-700" />

      {/* Unread notification badge */}
      {unreadCount > 0 && (
        <span className="absolute top-1 right-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-[10px] text-white font-bold">
          {unreadCount > 9 ? "9+" : unreadCount}
        </span>
      )}
    </button>
  );
};

export default NotificationButton;
