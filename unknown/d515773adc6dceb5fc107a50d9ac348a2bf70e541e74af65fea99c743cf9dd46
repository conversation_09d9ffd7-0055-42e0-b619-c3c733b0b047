'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, Sheet<PERSON>rigger, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle } from '@/components/ui/sheet';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface FilterSortProps {}

const FilterSort: React.FC<FilterSortProps> = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [sortOption, setSortOption] = useState<string>(searchParams.get('sort') || '');
  const [priceRange, setPriceRange] = useState<number[]>([
    Number(searchParams.get('minPrice')) || 0,
    Number(searchParams.get('maxPrice')) || 500,
  ]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(searchParams.get('brands')?.split(',') || []);
  const [selectedSizes, setSelectedSizes] = useState<string[]>(searchParams.get('sizes')?.split(',') || []);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [isSheetOpen, setIsSheetOpen] = useState<boolean>(false);

  const brands = [
    'Nike', 'Adidas', 'Puma', 'Converse', 'Vans', 'Timberland', 'Clarks', 'New Balance', 'Fila', 'Jordan',
    'Zara', 'H&M', "Levi's", 'Tommy Hilfiger', 'Ralph Lauren', 'Lacoste', 'Guess', 'Diesel', 'GAP', 'Supreme', 'Off-White',
    'Casio', 'G-Shock', 'Fossil', 'Daniel Wellington', 'Michael Kors', 'Rolex',
    'Gucci', 'Louis Vuitton', 'Prada', 'Herschel',
    'Kente Gentlemen', 'Christie Brown', 'Nana Wax', 'AfroMod Trends', 'Ohema Ohene',
    'Reebok', 'Champion'
  ];

  const updateURLParams = (newSortOption?: string) => {
    const params = new URLSearchParams(searchParams.toString());

    if (newSortOption !== undefined) {
      params.set('sort', newSortOption);
    } else if (sortOption) {
      params.set('sort', sortOption);
    }

    if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
    if (priceRange[1] < 500) params.set('maxPrice', priceRange[1].toString());
    if (selectedBrands.length) params.set('brands', selectedBrands.join(','));
    if (selectedSizes.length) params.set('sizes', selectedSizes.join(','));

    router.push(`?${params.toString()}`);
    setIsSheetOpen(false);
  };

  const resetFilters = () => {
    setPriceRange([0, 10000]);
    setSelectedBrands([]);
    setSelectedSizes([]);
    router.push(window.location.pathname);
    setIsSheetOpen(false);
  };

  const handleBrandChange = (brand: string) => {
    setSelectedBrands((prev) =>
      prev.includes(brand) ? prev.filter((b) => b !== brand) : [...prev, brand]
    );
  };

  const handleSizeChange = (size: string) => {
    setSelectedSizes((prev) =>
      prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]
    );
  };

  const handleSortChange = (value: string) => {
    setSortOption(value);
    setIsDialogOpen(false);
    updateURLParams(value);
  };

  return (
    <section className="flex justify-between items-center my-4 px-2">
      {/* Sort Section */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">Sort By</Button>
        </DialogTrigger>
        <DialogContent className="max-w-md w-[90%] rounded-lg p-6 sm:p-8">
          <DialogHeader>
            <DialogTitle>Sort By</DialogTitle>
          </DialogHeader>
          <RadioGroup value={sortOption} onValueChange={handleSortChange}>
            {['Price: Low to High', 'New Arrivals', 'Price: High to Low'].map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={option} />
                <label htmlFor={option} className="cursor-pointer">
                  {option}
                </label>
              </div>
            ))}
          </RadioGroup>
        </DialogContent>
      </Dialog>

      {/* Filter Section */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button variant="outline">Filter</Button>
        </SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Filter</SheetTitle>
          </SheetHeader>

          <div className="overflow-y-auto max-h-[80vh] pr-2">
            {/* Filter by Price */}
            <div className="mb-4">
              <h3 className="font-semibold mb-2">Price Range</h3>
              <Slider
                min={0}
                max={500}
                step={10}
                value={priceRange}
                onValueChange={setPriceRange}
              />
              <p className="mt-2">GH₵ {priceRange[0]} - GH₵ {priceRange[1]}</p>
            </div>

            {/* Filter by Brand */}
            <div className="mb-4">
              <h3 className="font-semibold mb-2">Brand</h3>
              {brands.map((brand) => (
                <div key={brand} className="flex items-center mb-1">
                  <Checkbox
                    id={brand}
                    checked={selectedBrands.includes(brand)}
                    onCheckedChange={() => handleBrandChange(brand)}
                  />
                  <label htmlFor={brand} className="ml-2 text-sm">
                    {brand}
                  </label>
                </div>
              ))}
            </div>

            {/* Filter by Size */}
            <div className="mb-4">
              <h3 className="font-semibold mb-2">Size</h3>
              {['S', 'M', 'L', 'XL', 'XXL'].map((size) => (
                <div key={size} className="flex items-center mb-1">
                  <Checkbox
                    id={size}
                    checked={selectedSizes.includes(size)}
                    onCheckedChange={() => handleSizeChange(size)}
                  />
                  <label htmlFor={size} className="ml-2 text-sm">
                    {size}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={resetFilters}>Reset</Button>
            <Button onClick={() => updateURLParams()}>Show Results</Button>
          </div>
        </SheetContent>
      </Sheet>
    </section>
  );
};

export default FilterSort;
