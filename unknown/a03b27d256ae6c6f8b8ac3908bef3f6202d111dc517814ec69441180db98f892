"use client";

import React, { Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";

interface PaginationComponentProps {
  totalPages: number;
}

const PaginationComponentContent: React.FC<PaginationComponentProps> = ({ totalPages }) => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const currentPage = parseInt(searchParams.get("page") || "1", 10);

  const updatePage = (newPage: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", newPage.toString());
    router.push(`?${params.toString()}`);
  };

  return (
    <div className="flex justify-between items-center mt-6">
      <button
        onClick={() => updatePage(Math.max(currentPage - 1, 1))}
        className="px-4 py-2 bg-primary rounded hover:bg-primary/90 text-primary-foreground disabled:opacity-50 transition-colors duration-200"
        disabled={currentPage === 1}
      >
        Previous
      </button>

      <span className="text-sm font-semibold">
        Page {currentPage} of {totalPages}
      </span>

      <button
        onClick={() => updatePage(Math.min(currentPage + 1, totalPages))}
        className="px-4 py-2 bg-primary rounded hover:bg-primary/90 text-primary-foreground disabled:opacity-50 transition-colors duration-200"
        disabled={currentPage === totalPages}
      >
        Next
      </button>
    </div>
  );
};

const PaginationComponent: React.FC<PaginationComponentProps> = ({totalPages}) => {
  return (
    <Suspense fallback={<div>Loading pagination...</div>}>
        <PaginationComponentContent totalPages={totalPages} />
  </Suspense>
  )
}


export default PaginationComponent;
