"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronsUpDown, Check, Trash } from "lucide-react";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import {  DateTimePickerComponent } from "../_components/DateTimePicker";
import StepNavigation from "../_components/StepNavigation";


// Dummy size options
const sizeOptions = [
  { label: "XS", value: "XS" },
  { label: "S", value: "S" },
  { label: "M", value: "M" },
  { label: "L", value: "L" },
  { label: "XL", value: "XL" },
  { label: "XXL", value: "XXL" },
  { label: "XXXL", value: "XXXL" },
];

// Variation form type
export interface Variation {
  id: number;
  size: string;
  quantity: number;
  globalPrice: string;
  salePrice: string;
  saleStartDate: string;
  saleEndDate: string;
  sizeMenuOpen: boolean; // NEW! To manage popover state safely
}

const VariationsPage = () => {
  const [variations, setVariations] = useState<Variation[]>([
    {
      id: 1,
      size: "",
      quantity: 0,
      globalPrice: "",
      salePrice: "",
      saleStartDate: "",
      saleEndDate: "",
      sizeMenuOpen: false,
    },
  ]);

  // Handles field changes
  const handleChange = (id: number, field: keyof Variation, value: any) => {
    setVariations((prev) =>
      prev.map((variation) => (variation.id === id ? { ...variation, [field]: value } : variation))
    );
  };

  // Adds a new variation form
  const addVariation = () => {
    const newVariation: Variation = {
      id: Date.now(),
      size: "",
      quantity: 0,
      globalPrice: "",
      salePrice: "",
      saleStartDate: "",
      saleEndDate: "",
      sizeMenuOpen: false,
    };
    setVariations([...variations, newVariation]);
  };

  // Deletes a variation form (can’t delete if only one remains)
  const deleteVariation = (id: number) => {
    if (variations.length > 1) {
      setVariations(variations.filter((variation) => variation.id !== id));
    }
  };

  const handleFormSubmit = () =>{

  }


  return (
    <div className="w-full ">
      <section className="bg-white mt-2  px-2 py-4 rounded-lg shadow-sm">
        <h2 className="text-sm uppercase font-bold  mb-3">Product Variations</h2>

        {variations.map((variation) => (
          <div key={variation.id} className="border-b pb-4 mb-4">
            {/* Grid layout */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {/* Size Combobox */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Size</label>
                <Popover
                  open={variation.sizeMenuOpen}
                  onOpenChange={(open) => handleChange(variation.id, "sizeMenuOpen", open)}
                >
                  <PopoverTrigger asChild>
                    <Button variant="outline" role="combobox" className="w-full justify-between">
                      {variation.size
                        ? sizeOptions.find((option) => option.value === variation.size)?.label
                        : "Select size..."}
                      <ChevronsUpDown className="opacity-50 ml-2" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search size..." className="h-9" />
                      <CommandList>
                        <CommandEmpty>No sizes found.</CommandEmpty>
                        <CommandGroup>
                          {sizeOptions.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={() => {
                                handleChange(variation.id, "size", option.value === variation.size ? "" : option.value);
                                handleChange(variation.id, "sizeMenuOpen", false);
                              }}
                            >
                              {option.label}
                              <Check
                                className={`ml-auto ${variation.size === option.value ? "opacity-100" : "opacity-0"}`}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Quantity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                <Input
                  type="number"
                  onChange={(e) => handleChange(variation.id, "quantity", Number(e.target.value))}
                />
              </div>

              {/* Global Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Global Price (GHS)</label>
                <Input
                  type="text"
                  onChange={(e) => handleChange(variation.id, "globalPrice", e.target.value)}
                />
              </div>

              {/* Sale Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sale Price (GHS)</label>
                <Input
                  type="text"
                  value={variation.salePrice}
                  onChange={(e) => handleChange(variation.id, "salePrice", e.target.value)}
                />
              </div>

              {/* Sale Start Date (Always visible, disabled when no salePrice) */}
              <div className="col-span-2"> 
                <label className="block text-sm font-medium text-gray-700 mb-1">Sale Start Date</label>
                <DateTimePickerComponent
                    value={variation.saleStartDate}
                    onChange={(value) => handleChange(variation.id, "saleStartDate", value)}
                    disabled={!variation.salePrice}
                  />

              </div>

              {/* Sale End Date (Always visible, disabled when no salePrice) */}
              <div className="col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Sale End Date</label>
                <DateTimePickerComponent
                    value={variation.saleStartDate}
                    onChange={(value) => handleChange(variation.id, "saleEndDate", value)}
                    disabled={!variation.salePrice}
                  />
              </div>
            </div>

            {/* Delete button (only if more than one form) */}
            {variations.length > 1 && (
              <Button variant="destructive" onClick={() => deleteVariation(variation.id)} className="mt-4">
                <Trash className="w-4 h-4 " />
              </Button>
            )}
          </div>
        ))}

        {/* Add Variation button */}
        <Button onClick={addVariation} variant={"outline"} className="mt-4 w-full  bg-primary hover:bg-blue-700">
          + Add Variation
        </Button>
  
      </section>


      <section className="w-full mt-6">
        <StepNavigation onSubmit={handleFormSubmit} />
      </section>
      
    </div>
  );
};

export default VariationsPage;
