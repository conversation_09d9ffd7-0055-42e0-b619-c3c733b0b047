import { FaStar } from 'react-icons/fa';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';

interface Review {
  id: number;
  user: string;
  title: string;
  comment: string;
  rating: number; // out of 5
  date: string;
}

interface VerifiedCommentsProps {
  averageRating: number;
  totalReviews: number;
  productId: string;
  reviews: Review[];
  viewAllLink: string;
}

const VerifiedComments: React.FC<VerifiedCommentsProps> = ({
  averageRating,
  totalReviews,
  productId,
  reviews,
  viewAllLink
}) => {
    const hasReviews = reviews.length > 0;

  return (
    <section className="bg-white my-2 py-3 px-3 rounded shadow-sm">
      <div className="flex justify-between items-center border-b pb-2">
        <div>
          <h1 className="text-sm font-semibold text-gray-600">Verified Customer Feedback</h1>
          {hasReviews && (<div className="flex items-center gap-1 mt-1">
            <p className="font-bold text-sm text-gray-600">{averageRating.toFixed(1)}</p>
            <FaStar className="text-yellow-400" />
            <span className="text-sm text-gray-600">({totalReviews} reviews)</span>
          </div>)}
        </div>

        <Link href={viewAllLink} className="text-primary hover:text-primary/80 text-sm flex items-center transition-colors duration-200">
          View All
          <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>

      <div className="mt-3 space-y-2">
        {hasReviews ? (reviews.slice(0, 3).map((review) => (
          <div key={review.id} className="border-b pb-3 last:border-b-0">
            {/* Stars */}
            <div className="flex items-center gap-1 mb-1">
              {Array.from({ length: review.rating }, (_, i) => (
                <FaStar key={i} className="text-yellow-400 text-xs" />
              ))}
              {Array.from({ length: 5 - review.rating }, (_, i) => (
                <FaStar key={i} className="text-gray-300 text-xs" />
              ))}
            </div>

            {/* Comment Title */}
            <p className="text-sm font-bold text-gray-600">{review.title}</p>

            {/* Comment Body */}
            <p className="text-sm text-gray-700 mt-1">{review.comment}</p>

            {/* Author */}
            <p className="text-xs text-gray-500 mt-1">By {review.user}</p>
          </div>
        ))
        ) : (
            <div className="py-6 text-center text-gray-500">
            <p className="text-sm">No reviews yet. Be the first to review this product!</p>
            </div>
        )}
      </div>
    </section>
  );
};

export default VerifiedComments;
