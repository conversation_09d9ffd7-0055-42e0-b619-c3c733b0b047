"use client";

import { CheckIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

// Type for step definition
interface Step {
    id: number;
    name: string;
    route: string;
  }

  // Steps array
  export const steps: Step[] = [
    { id: 1, name: "Bale Information", route: "/creators/bales/add/bale-info" },
    { id: 2, name: "Variations", route: "/creators/bales/add/variant" },
    { id: 3, name: "Specifications", route: "/creators/bales/add/specifications" },
  ];

export default function StepProgress() {
  const pathname = usePathname();
  const router = useRouter();
  const currentStep = steps.findIndex((step) => step.route === pathname) + 1;

  const getProgress = () => (currentStep / steps.length) * 100;

  return (
    <div className="flex gap-8 items-start ">
      {/* Left: Single Progress Circle */}
      <div className="relative w-20 h-20">
        <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
          {/* Background circle */}
          <circle
            cx="50"
            cy="50"
            r="45"
            stroke="gray"
            strokeWidth="8"
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx="50"
            cy="50"
            r="45"
            stroke="hsl(var(--primary))"
            strokeWidth="8"
            fill="transparent"
            strokeDasharray="282.6"
            strokeDashoffset={282.6 - (282.6 * getProgress()) / 100}
            className="transition-all duration-300"
          />
        </svg>
        {/* Step count inside the circle */}
        <div className="absolute inset-0 flex items-center justify-center text-xs font-bold">
          {currentStep} of {steps.length}
        </div>
      </div>

      {/* Right: Steps with checkmarks */}
      <div className="flex flex-col space-y-1 w-full">
        {steps.map((step) => (
          <div
            key={step.id}
            onClick={() => router.push(step.route)}
            className={`flex items-center gap-4 p-1 rounded-lg cursor-pointer ${
              step.id <= currentStep ? "text-primary" : "text-gray-400"
            }`}
          >
            {/* Checkmark circle for completed steps */}
            <div
              className={`w-4 h-4 flex items-center justify-center rounded-full border-2 ${
                step.id < currentStep
                  ? "border-primary bg-primary text-primary-foreground"
                  : step.id === currentStep
                  ? "border-primary"
                  : "border-gray-400"
              }`}
            >
              {step.id < currentStep ? <CheckIcon/> : ""}
            </div>

            {/* Step name */}
            <p className={`text-xs ${step.id === currentStep ? "font-bold" : ""}`}>
              {step.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
