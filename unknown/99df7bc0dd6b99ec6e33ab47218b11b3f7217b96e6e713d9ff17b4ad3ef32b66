import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import Link from 'next/link';

interface Category {
  id: number;
  name: string;
  image: string;
  link: string;
}

const categories: Category[] = [
  { id: 1, name: "Men's Clothing", image: "/images/categories/menwear.jpg", link: "/categories/mens-clothing" },
  { id: 2, name: "Women's Clothing", image: "/images/categories/womenwear.jpg", link: "/categories/womens-clothing" },
  { id: 3, name: "Men's Footwear", image: "/images/categories/shoes.jpg", link: "/categories/mens-footwear" },
  { id: 4, name: "Accessories", image: "/images/categories/accessories.jpg", link: "/categories/accessories" },
  { id: 6, name: "Women's Footwear", image: "/images/categories/shoes.jpg", link: "/categories/womens-footwear" },
  { id: 5, name: "Bags", image: "/images/categories/bags.jpg", link: "/categories/accessories/bags" }
];

const ShoppingCategoriesComponent: React.FC = () => {
  return (
    <section className="flex flex-col w-full mt-6">
      {/* Header */}
      <section className="w-full bg-primary py-4 px-3 mb-2 text-primary-foreground">
        <div className="flex justify-between items-center rounded-sm">
          <h1 className="text-base font-bold">Shop with Categories</h1>
        </div>
      </section>

      {/* Carousel with Category Cards */}
      <ProductsCarousel>
        {categories.map((category) => (
            <Link href={category.link}  key={category.id}  className="mr-2 block">
                <div className="relative w-full h-40 ">
                    <img
                    src={category.image}
                    alt={category.name}
                    className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-lg ">
                    <h2 className="text-white text-sm font-bold">{category.name}</h2>
                    </div>
                </div>
            </Link>

        ))}
      </ProductsCarousel>
    </section>
  );
};

export default ShoppingCategoriesComponent;
