'use client';

import { Button } from '@/components/ui/button';
import { IoAdd, IoRemove } from 'react-icons/io5';
import { useState } from 'react';

interface CartItemProps {
  product: {
    id: number;
    name: string;
    price: string;
    discount: number;
    size: string;
    shop: string;
    image: string;
    available: boolean;
    stockLeft: number;
  };
}

const CartItem: React.FC<CartItemProps> = ({ product }) => {
  const [quantity, setQuantity] = useState(1);

  const handleQuantityChange = (increment: boolean) => {
    setQuantity((prevQuantity) =>
      increment ? prevQuantity + 1 : Math.max(prevQuantity - 1, 1)
    );
  };

  const discountedPrice = Number(product.price);
  const originalPrice = (discountedPrice / (1 - product.discount / 100)).toFixed(2);

  return (
    <div className="bg-white rounded shadow-sm p-3 mb-3 flex flex-col">
      <div className="flex">
        <div className="w-24 h-24 bg-gray-200">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover rounded"
          />
        </div>

        <div className="ml-4 flex-1">
          <h2 className="font-medium text-gray-800">{product.name}</h2>
          <div className="flex gap-2 items-center my-1">
            <p className="text-lg font-bold">GH₵{discountedPrice.toFixed(2)}</p>
            <p className="text-sm text-gray-500 line-through">GH₵{originalPrice}</p>
            <div className="bg-primary-50 text-primary text-xs font-bold px-2 py-1 rounded">
              -{product.discount}%
            </div>
          </div>

          <p className="text-sm text-gray-600">Shop: {product.shop}</p>
          <p className="text-sm text-gray-600 mt-1">Size: {product.size}</p>
          {product.stockLeft <= 5 && (
            <p className="text-sm text-red-500 mt-1">Few units left</p>
          )}
        </div>
      </div>

      <div className="flex items-center mt-3 justify-between">
        <Button variant="link" className="text-sm px-3 py-1">
          Remove
        </Button>

        <div className="flex items-center gap-2 ">
          <Button
            onClick={() => handleQuantityChange(false)}
            className="p-1.5 w-8 h-8 font-bold flex items-center justify-center bg-primary hover:bg-primary/90 disabled:opacity-50 transition-colors duration-200"
            disabled={quantity <= 1}
          >
            <IoRemove size={16} color='white' />
          </Button>

          <span className="text-lg min-w-10 text-center font-medium">{quantity}</span>

          <button
            onClick={() => handleQuantityChange(true)}
            className="p-1.5 w-8 h-8 flex items-center justify-center bg-primary hover:bg-primary/90 disabled:opacity-50 transition-colors duration-200"
            disabled={!product.available}
          >
            <IoAdd size={16} color='white' />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CartItem;
