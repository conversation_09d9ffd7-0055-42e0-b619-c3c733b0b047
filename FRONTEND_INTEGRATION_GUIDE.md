# 🚀 EveryFash Frontend Integration Guide

## Table of Contents
1. [Authentication System](#authentication-system)
2. [API Integration Patterns](#api-integration-patterns)
3. [State Management](#state-management)
4. [Form Handling](#form-handling)
5. [Error Handling](#error-handling)
6. [File Upload Patterns](#file-upload-patterns)
7. [Route Protection](#route-protection)
8. [Adding New Features](#adding-new-features)

---

## 🔐 Authentication System

### Overview
EveryFash implements a dual-role authentication system supporting both **Buyers** and **Creators** with separate registration flows, onboarding processes, and dashboard access.

### Architecture

```
Authentication Flow:
├── Buyer Auth
│   ├── Registration (/auth/buyer/register)
│   ├── Login (/auth/buyer/login)
│   ├── Email Verification
│   ├── Google OAuth
│   └── Dashboard Access
└── Creator Auth
    ├── Registration (/auth/creator/register)
    ├── Login (/auth/creator/login)
    ├── Email Verification
    ├── Google OAuth
    ├── Multi-step Onboarding
    └── Dashboard Access
```

### Key Components

#### 1. Authentication Store (Zustand)
**Location**: `lib/stores/auth-store.ts`

```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  setUser: (user: User) => void;
  setToken: (token: string) => void;
}
```

**Key Features:**
- Persistent storage with localStorage
- Automatic token management
- Role-based user data handling
- Error state management

#### 2. API Client Architecture
**Location**: `lib/api/`

```
API Structure:
├── base-client.ts (Core HTTP client)
├── auth.ts (Authentication endpoints)
├── products.ts (Product management)
├── categories.ts (Category hierarchy)
├── config.ts (API configuration)
└── errors.ts (Error handling)
```

#### 3. Authentication Hooks
**Location**: `lib/hooks/use-auth.ts`

```typescript
// Core authentication hook
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    login: store.login,
    logout: store.logout,
    // ... other auth methods
  };
};

// Role-specific hooks
export const useBuyerAuth = () => { /* Buyer-specific logic */ };
export const useCreatorAuth = () => { /* Creator-specific logic */ };
```

### Implementation Details

#### Registration Flow

**Buyer Registration:**
```typescript
// API Call
const registerBuyer = async (data: BuyerRegisterData) => {
  const response = await authApiClient.post('/auth/register/buyer', data);
  return response.data;
};

// Form Component
const BuyerRegisterForm = () => {
  const { register, isLoading } = useAuth();
  
  const onSubmit = async (data: BuyerRegisterFormData) => {
    try {
      await register(data);
      // Handle success (redirect, show message, etc.)
    } catch (error) {
      // Handle error
    }
  };
};
```

**Creator Registration:**
```typescript
// API Call
const registerCreator = async (data: CreatorRegisterData) => {
  const response = await authApiClient.post('/auth/register/creator', data);
  return response.data;
};

// Form Component with validation
const CreatorRegisterForm = () => {
  const form = useForm<CreatorRegisterFormData>({
    resolver: zodResolver(creatorRegisterSchema),
    defaultValues: { /* ... */ }
  });
  
  const onSubmit = async (data: CreatorRegisterFormData) => {
    // Registration logic
  };
};
```

#### Email Verification System

```typescript
// Verification hook
export const useEmailVerification = () => {
  const [isVerifying, setIsVerifying] = useState(false);
  
  const verifyEmail = async (token: string) => {
    setIsVerifying(true);
    try {
      await authApi.verifyEmail(token);
      // Handle success
    } catch (error) {
      // Handle error
    } finally {
      setIsVerifying(false);
    }
  };
  
  const resendVerification = async (email: string) => {
    await authApi.resendVerification(email);
  };
  
  return { verifyEmail, resendVerification, isVerifying };
};
```

#### Google OAuth Integration

```typescript
// OAuth configuration
const GOOGLE_OAUTH_CONFIG = {
  buyer: '/api/v1/auth/google/buyer',
  creator: '/api/v1/auth/google/creator',
  callbackUrl: 'https://www.everyfash.com/auth/callback'
};

// OAuth handler
export const handleGoogleAuth = (userType: 'buyer' | 'creator') => {
  const authUrl = `${API_BASE_URL}${GOOGLE_OAUTH_CONFIG[userType]}`;
  window.location.href = authUrl;
};

// Callback handler
export const handleOAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const success = urlParams.get('success');
  const data = urlParams.get('data');
  
  if (success === 'true' && data) {
    const authResponse = JSON.parse(decodeURIComponent(data));
    // Process auth response
  }
};
```

---

## 🔌 API Integration Patterns

### Base API Client

**Location**: `lib/api/base-client.ts`

```typescript
export class BaseApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  // Authenticated requests
  async authenticatedRequest<T>(
    method: string,
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    const token = getAuthToken();
    const requestHeaders = {
      ...this.defaultHeaders,
      ...(token && { Authorization: `Bearer ${token}` }),
      ...headers,
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method,
      headers: requestHeaders,
      ...(data && { body: JSON.stringify(data) }),
    });

    return this.handleResponse<T>(response);
  }

  // Public requests (no auth required)
  async publicRequest<T>(
    method: string,
    endpoint: string,
    data?: any
  ): Promise<T> {
    // Implementation
  }

  // File upload requests
  async uploadRequest<T>(
    endpoint: string,
    formData: FormData
  ): Promise<T> {
    const token = getAuthToken();
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Note: Don't set Content-Type for FormData
      },
      body: formData,
    });

    return this.handleResponse<T>(response);
  }
}
```

### React Query Integration

**Location**: `lib/hooks/use-*.ts`

```typescript
// Query keys pattern
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
};

// Query hook pattern
export const useCreatorProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: productKeys.list(JSON.stringify(params || {})),
    queryFn: () => productsApi.getCreatorProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Mutation hook pattern
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductData) => productsApi.createProduct(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      toast({
        title: 'Product Created Successfully',
        description: 'Your product has been created and is pending approval.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

---

## 🗂️ State Management

### Zustand Store Pattern

**Store Structure:**
```typescript
// Store interface
interface StoreState {
  // Data
  data: DataType[];
  currentItem: DataType | null;

  // UI State
  isLoading: boolean;
  error: string | null;

  // Pagination
  page: number;
  limit: number;
  total: number;
}

interface StoreActions {
  // Data actions
  setData: (data: DataType[]) => void;
  addItem: (item: DataType) => void;
  updateItem: (id: string, updates: Partial<DataType>) => void;
  removeItem: (id: string) => void;

  // UI actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // Pagination actions
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}
```

**Implementation Example:**
```typescript
// lib/stores/products-store.ts
export const useProductsStore = create<ProductsState & ProductsActions>()(
  persist(
    (set, get) => ({
      // Initial state
      products: [],
      currentProduct: null,
      isLoading: false,
      error: null,
      page: 1,
      limit: 20,
      total: 0,

      // Actions
      setProducts: (products) => set({ products }),

      addProduct: (product) => set((state) => ({
        products: [...state.products, product]
      })),

      updateProduct: (id, updates) => set((state) => ({
        products: state.products.map(p =>
          p._id === id ? { ...p, ...updates } : p
        )
      })),

      removeProduct: (id) => set((state) => ({
        products: state.products.filter(p => p._id !== id)
      })),

      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),
    }),
    {
      name: 'products-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist certain fields
        products: state.products,
        currentProduct: state.currentProduct,
      }),
    }
  )
);
```

---

## 📝 Form Handling

### React Hook Form + Zod Pattern

**Schema Definition:**
```typescript
// lib/schemas/product-schemas.ts
export const productInfoSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().min(1, "Brand is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  category: z.string().min(1, "Category is required"),
  gender: z.enum(['Men', 'Women', 'Unisex'], {
    required_error: "Please select a gender",
  }),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
});

export type ProductInfoFormData = z.infer<typeof productInfoSchema>;
```

**Form Component Pattern:**
```typescript
// components/forms/ProductInfoForm.tsx
export default function ProductInfoForm({ onNext }: ProductInfoFormProps) {
  const form = useForm<ProductInfoFormData>({
    resolver: zodResolver(productInfoSchema),
    defaultValues: {
      name: "",
      brand: "",
      description: "",
      basePrice: 0,
      category: "",
      gender: "Unisex",
      highlights: [],
      tags: [],
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  const onSubmit = async (data: ProductInfoFormData) => {
    try {
      // Process form data
      await submitData(data);
      onNext();
    } catch (error) {
      // Handle error
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Form fields with Controller */}
      <Controller
        name="name"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Product Name <span className="text-red-500">*</span>
            </label>
            <Input
              {...field}
              placeholder="Enter product name"
              className={errors.name ? 'border-red-300' : ''}
            />
            {errors.name && (
              <p className="text-xs text-red-600 mt-1">{errors.name.message}</p>
            )}
          </div>
        )}
      />

      {/* More form fields... */}

      <Button type="submit" className="w-full">
        Continue
      </Button>
    </form>
  );
}
```

### Multi-Step Form Pattern

**Context Provider:**
```typescript
// lib/contexts/ProductFormContext.tsx
interface ProductFormContextType {
  productInfo: ProductInfoFormData;
  specifications: SpecificationsFormData;
  variations: VariationsFormData;
  updateProductInfo: (data: Partial<ProductInfoFormData>) => void;
  updateSpecifications: (data: Partial<SpecificationsFormData>) => void;
  updateVariations: (data: Partial<VariationsFormData>) => void;
  resetForm: () => void;
}

export const ProductFormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [productInfo, setProductInfo] = useState<ProductInfoFormData>(initialProductInfo);
  const [specifications, setSpecifications] = useState<SpecificationsFormData>(initialSpecifications);
  const [variations, setVariations] = useState<VariationsFormData>(initialVariations);

  const updateProductInfo = (data: Partial<ProductInfoFormData>) => {
    setProductInfo(prev => ({ ...prev, ...data }));
  };

  // ... other update functions

  return (
    <ProductFormContext.Provider value={{
      productInfo,
      specifications,
      variations,
      updateProductInfo,
      updateSpecifications,
      updateVariations,
      resetForm,
    }}>
      {children}
    </ProductFormContext.Provider>
  );
};
```

---

## ⚠️ Error Handling

### Centralized Error Handling

**Error Types:**
```typescript
// lib/types/errors.ts
export interface ApiError {
  status: 'fail' | 'error';
  statusCode: number;
  message: string;
  error?: {
    statusCode: number;
    status: string;
    isOperational: boolean;
  };
  stack?: string;
}

export type ErrorType = 'validation' | 'authentication' | 'authorization' | 'network' | 'server' | 'unknown';
```

**Error Utilities:**
```typescript
// lib/utils/error-utils.ts
export const getApiErrorMessage = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'An unexpected error occurred. Please try again.';
};

export const getApiErrorTitle = (error: any): string => {
  const statusCode = error?.response?.status || error?.statusCode;

  switch (statusCode) {
    case 400:
      return 'Invalid Request';
    case 401:
      return 'Authentication Required';
    case 403:
      return 'Access Denied';
    case 404:
      return 'Not Found';
    case 422:
      return 'Validation Error';
    case 500:
      return 'Server Error';
    default:
      return 'Error';
  }
};

export const handleApiError = (error: any) => {
  console.error('API Error:', error);

  // Handle specific error types
  if (error?.response?.status === 401) {
    // Handle authentication error
    useAuthStore.getState().logout();
    window.location.href = '/auth/login';
    return;
  }

  // Show toast notification
  toast({
    variant: 'destructive',
    title: getApiErrorTitle(error),
    description: getApiErrorMessage(error),
  });
};
```

---

## 📁 File Upload Patterns

### FormData Construction

**Single File Upload:**
```typescript
const uploadSingleFile = async (file: File, endpoint: string) => {
  const formData = new FormData();
  formData.append('file', file);

  return await apiClient.uploadRequest(endpoint, formData);
};
```

**Multiple Files with Additional Data:**
```typescript
const createProductWithFiles = async (productData: CreateProductData) => {
  const formData = new FormData();

  // Add basic fields
  formData.append('name', productData.name);
  formData.append('brand', productData.brand);
  formData.append('description', productData.description);
  formData.append('basePrice', productData.basePrice.toString());
  formData.append('category', productData.category);
  formData.append('gender', productData.gender);

  // Add files (field name must match backend expectation)
  productData.productImages.forEach((image) => {
    formData.append('productImages', image);
  });

  // Add arrays using nested notation
  if (productData.variations && productData.variations.length > 0) {
    productData.variations.forEach((variation, index) => {
      formData.append(`variations[${index}][color]`, variation.color);
      formData.append(`variations[${index}][size]`, variation.size);
      formData.append(`variations[${index}][quantity]`, variation.quantity.toString());
      formData.append(`variations[${index}][price]`, variation.price.toString());

      if (variation.salePrice && variation.salePrice > 0) {
        formData.append(`variations[${index}][salePrice]`, variation.salePrice.toString());
      }
    });
  }

  // Add simple arrays
  productData.highlights?.forEach(highlight => {
    formData.append('highlights[]', highlight);
  });

  productData.tags?.forEach(tag => {
    formData.append('tags[]', tag);
  });

  return await productsApi.createProduct(formData);
};
```

---

## 🔒 Route Protection

### Middleware-Based Protection

**Location**: `middleware.ts`

```typescript
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  const userRole = request.cookies.get('user-role')?.value;
  const pathname = request.nextUrl.pathname;

  // Public routes that don't require authentication
  const publicRoutes = ['/auth', '/verify-email', '/reset-password'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // If no token and trying to access protected route
  if (!token && !isPublicRoute) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  // Role-based redirects
  if (token && userRole) {
    // Prevent authenticated users from accessing auth pages
    if (isPublicRoute && pathname !== '/verify-email') {
      const dashboardUrl = userRole === 'creator' ? '/creators' : '/dashboard';
      return NextResponse.redirect(new URL(dashboardUrl, request.url));
    }

    // Creator-specific protection
    if (pathname.startsWith('/creators') && userRole !== 'creator') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }

    // Buyer-specific protection
    if (pathname.startsWith('/dashboard') && userRole !== 'buyer') {
      return NextResponse.redirect(new URL('/creators', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

### Component-Level Protection

```typescript
// components/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'buyer' | 'creator';
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  fallback
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return fallback || <Navigate to="/auth/login" />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return fallback || <AccessDenied />;
  }

  return <>{children}</>;
};
```

---

## 🚀 Adding New Features

### Step-by-Step Integration Guide

#### 1. Define Types and Interfaces

```typescript
// lib/types/new-feature.ts
export interface NewFeatureData {
  id: string;
  name: string;
  description: string;
  // ... other fields
}

export interface CreateNewFeatureData {
  name: string;
  description: string;
  // ... required fields for creation
}

export interface UpdateNewFeatureData {
  name?: string;
  description?: string;
  // ... optional fields for updates
}
```

#### 2. Create API Client

```typescript
// lib/api/new-feature.ts
import { BaseApiClient } from './base-client';

const newFeatureApiClient = new BaseApiClient();

export const newFeatureApi = {
  // Get all items
  getAll: async (params?: QueryParams): Promise<{
    status: string;
    results: number;
    total: number;
    data: { items: NewFeatureData[] };
  }> => {
    const queryString = new URLSearchParams(params).toString();
    return newFeatureApiClient.authenticatedGet(
      `/new-feature${queryString ? `?${queryString}` : ''}`
    );
  },

  // Get single item
  getById: async (id: string): Promise<{
    status: string;
    data: { item: NewFeatureData };
  }> => {
    return newFeatureApiClient.authenticatedGet(`/new-feature/${id}`);
  },

  // Create new item
  create: async (data: CreateNewFeatureData): Promise<{
    status: string;
    data: { item: NewFeatureData };
  }> => {
    return newFeatureApiClient.authenticatedPost('/new-feature', data);
  },

  // Update item
  update: async (id: string, data: UpdateNewFeatureData): Promise<{
    status: string;
    data: { item: NewFeatureData };
  }> => {
    return newFeatureApiClient.authenticatedPatch(`/new-feature/${id}`, data);
  },

  // Delete item
  delete: async (id: string): Promise<{ status: string }> => {
    return newFeatureApiClient.authenticatedDelete(`/new-feature/${id}`);
  },
};
```

#### 3. Create React Query Hooks

```typescript
// lib/hooks/use-new-feature.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { newFeatureApi } from '@/lib/api/new-feature';
import { toast } from '@/hooks/use-toast';

// Query keys
export const newFeatureKeys = {
  all: ['new-feature'] as const,
  lists: () => [...newFeatureKeys.all, 'list'] as const,
  list: (filters: string) => [...newFeatureKeys.lists(), { filters }] as const,
  details: () => [...newFeatureKeys.all, 'detail'] as const,
  detail: (id: string) => [...newFeatureKeys.details(), id] as const,
};

// Get all items
export const useNewFeatureItems = (params?: QueryParams) => {
  return useQuery({
    queryKey: newFeatureKeys.list(JSON.stringify(params || {})),
    queryFn: () => newFeatureApi.getAll(params),
    staleTime: 5 * 60 * 1000,
  });
};

// Get single item
export const useNewFeatureItem = (id: string) => {
  return useQuery({
    queryKey: newFeatureKeys.detail(id),
    queryFn: () => newFeatureApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Create item
export const useCreateNewFeatureItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateNewFeatureData) => newFeatureApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: newFeatureKeys.lists() });
      toast({
        title: 'Success',
        description: 'Item created successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update item
export const useUpdateNewFeatureItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateNewFeatureData }) =>
      newFeatureApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: newFeatureKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: newFeatureKeys.lists() });
      toast({
        title: 'Success',
        description: 'Item updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Delete item
export const useDeleteNewFeatureItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => newFeatureApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: newFeatureKeys.lists() });
      toast({
        title: 'Success',
        description: 'Item deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

#### 4. Create Form Schema and Components

```typescript
// lib/schemas/new-feature-schemas.ts
export const createNewFeatureSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  // ... other validation rules
});

export type CreateNewFeatureFormData = z.infer<typeof createNewFeatureSchema>;
```

```typescript
// components/forms/NewFeatureForm.tsx
export const NewFeatureForm: React.FC<NewFeatureFormProps> = ({ onSuccess }) => {
  const createMutation = useCreateNewFeatureItem();

  const form = useForm<CreateNewFeatureFormData>({
    resolver: zodResolver(createNewFeatureSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const onSubmit = async (data: CreateNewFeatureFormData) => {
    try {
      await createMutation.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      // Error handled by mutation
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <Controller
        name="name"
        control={form.control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Name <span className="text-red-500">*</span>
            </label>
            <Input
              {...field}
              placeholder="Enter name"
              className={form.formState.errors.name ? 'border-red-300' : ''}
            />
            {form.formState.errors.name && (
              <p className="text-xs text-red-600 mt-1">
                {form.formState.errors.name.message}
              </p>
            )}
          </div>
        )}
      />

      {/* More form fields... */}

      <Button
        type="submit"
        disabled={createMutation.isPending}
        className="w-full"
      >
        {createMutation.isPending ? 'Creating...' : 'Create Item'}
      </Button>
    </form>
  );
};
```

#### 5. Create List and Detail Components

```typescript
// components/NewFeatureList.tsx
export const NewFeatureList: React.FC = () => {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");

  const { data, isLoading, error } = useNewFeatureItems({
    page,
    limit: 20,
    search,
  });

  const deleteMutation = useDeleteNewFeatureItem();

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this item?')) {
      await deleteMutation.mutateAsync(id);
    }
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  const items = data?.data?.items || [];

  return (
    <div className="space-y-6">
      {/* Search and filters */}
      <div className="flex gap-4">
        <Input
          placeholder="Search items..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Items list */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item) => (
          <Card key={item.id} className="p-6">
            <h3 className="text-lg font-semibold mb-2">{item.name}</h3>
            <p className="text-gray-600 mb-4">{item.description}</p>

            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Edit
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleDelete(item.id)}
                disabled={deleteMutation.isPending}
              >
                Delete
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={page}
        totalPages={Math.ceil((data?.total || 0) / 20)}
        onPageChange={setPage}
      />
    </div>
  );
};
```

---

## 📋 API Endpoints Reference

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/v1/auth/register/buyer` | Register buyer | No |
| POST | `/api/v1/auth/register/creator` | Register creator | No |
| POST | `/api/v1/auth/login` | Login user | No |
| POST | `/api/v1/auth/logout` | Logout user | Yes |
| POST | `/api/v1/auth/verify-email/:token` | Verify email | No |
| POST | `/api/v1/auth/resend-verification` | Resend verification | No |
| POST | `/api/v1/auth/forgot-password` | Request password reset | No |
| PATCH | `/api/v1/auth/reset-password/:token` | Reset password | No |
| GET | `/api/v1/auth/google/buyer` | Google OAuth (Buyer) | No |
| GET | `/api/v1/auth/google/creator` | Google OAuth (Creator) | No |

### Creator Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/creators/onboarding/status` | Get onboarding status | Yes (Creator) |
| GET | `/api/v1/creators/onboarding/business-info` | Get business info | Yes (Creator) |
| PATCH | `/api/v1/creators/onboarding/business-info` | Update business info | Yes (Creator) |
| GET | `/api/v1/creators/onboarding/payment-info` | Get payment info | Yes (Creator) |
| PATCH | `/api/v1/creators/onboarding/payment-info` | Update payment info | Yes (Creator) |
| GET | `/api/v1/creators/onboarding/shop-info` | Get shop info | Yes (Creator) |
| PATCH | `/api/v1/creators/onboarding/shop-info` | Update shop info | Yes (Creator) |
| GET | `/api/v1/creators/onboarding/shipping-info` | Get shipping info | Yes (Creator) |
| PATCH | `/api/v1/creators/onboarding/shipping-info` | Update shipping info | Yes (Creator) |
| GET | `/api/v1/creators/onboarding/verification-status` | Get verification status | Yes (Creator) |

### Product Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/creators/products` | Get creator products | Yes (Creator) |
| POST | `/api/v1/creators/products` | Create product | Yes (Creator) |
| GET | `/api/v1/creators/products/:id` | Get single product | Yes (Creator) |
| PATCH | `/api/v1/creators/products/:id` | Update product | Yes (Creator) |
| DELETE | `/api/v1/creators/products/:id` | Delete product | Yes (Creator) |
| GET | `/api/v1/creators/products/stats` | Get product statistics | Yes (Creator) |

### Category Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/categories/hierarchy` | Get category hierarchy | No |

---

## 🎯 Best Practices

### 1. API Integration
- Always use TypeScript interfaces for API responses
- Implement proper error handling for all API calls
- Use React Query for caching and state management
- Follow the established naming conventions for query keys

### 2. Form Handling
- Use Zod for schema validation
- Implement proper error display for form fields
- Use React Hook Form Controller for complex components
- Provide loading states during form submission

### 3. State Management
- Use Zustand for global state that needs persistence
- Keep component state local when possible
- Use React Query for server state management
- Implement proper loading and error states

### 4. Error Handling
- Implement centralized error handling
- Provide user-friendly error messages
- Log errors for debugging purposes
- Handle authentication errors globally

### 5. Security
- Never store sensitive data in localStorage
- Implement proper route protection
- Validate user permissions on both client and server
- Use HTTPS for all API communications

---

## 🔧 Development Workflow

### Adding a New Feature

1. **Define Types** - Create TypeScript interfaces
2. **Create API Client** - Implement API functions
3. **Create Hooks** - Implement React Query hooks
4. **Create Forms** - Implement form components with validation
5. **Create Components** - Implement list/detail components
6. **Add Routes** - Configure routing and protection
7. **Test Integration** - Test all functionality
8. **Update Documentation** - Document the new feature

### Testing API Integration

```typescript
// Example test for API hook
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useNewFeatureItems } from '@/lib/hooks/use-new-feature';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

test('should fetch new feature items', async () => {
  const { result } = renderHook(() => useNewFeatureItems(), {
    wrapper: createWrapper(),
  });

  await waitFor(() => {
    expect(result.current.isSuccess).toBe(true);
  });

  expect(result.current.data).toBeDefined();
});
```

This comprehensive guide provides everything you need to understand and extend the EveryFash frontend integration patterns! 🚀
