'use client';

import React from 'react';
import <PERSON><PERSON><PERSON>oa<PERSON> from './LottieLoader';
import { cn } from '@/lib/utils';

interface ButtonLoaderProps {
  /**
   * Loading text to display next to the animation
   */
  text?: string;
  /**
   * Size of the loader animation
   * @default 'sm'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /**
   * Custom className
   */
  className?: string;
  /**
   * Whether to show text
   * @default true
   */
  showText?: boolean;
}

const ButtonLoader: React.FC<ButtonLoaderProps> = ({
  text = 'Loading...',
  size = 'sm',
  className,
  showText = true,
}) => {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <LottieLoader 
        size={size} 
        centered={false}
        className="flex-shrink-0"
      />
      {showText && text && (
        <span className="text-sm">
          {text}
        </span>
      )}
    </div>
  );
};

export default ButtonLoader;
