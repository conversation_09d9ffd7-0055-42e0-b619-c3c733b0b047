import React from 'react'
import RegisterForm from '../_components/RegisterForm'
import Link from 'next/link'
import { SocialAuthButtons } from '../_components/SocialAuthButtons'
import { GuestGuard } from '@/lib/components/AuthGuard'

const RegisterPage = () => {
  return (
    <GuestGuard>
      <section className='flex flex-col items-center w-full max-w-md my-20'>

          {/* Header */}
          <h1 className="text-xl font-bold text-gray-900">Everyfash</h1>

          {/* About */}
          <p className="text-sm pt-2 px-2 mb-6 font-medium text-gray-800">
          Create an account to get started
          </p>

          {/* Social Auth Buttons */}
          <SocialAuthButtons role="buyer" />


          {/* Or */}
          <div className="my-2 flex items-center">
              <p className="text-gray-700">Or</p>
          </div>

          {/* Form */}
          <RegisterForm role="buyer" />

          {/* Link to Login */}
          <div className="">
              <p className="text-left text-gray-600">Already have an account? <Link href="/login" className="text-primary">Login</Link></p>
          </div>

      </section>
    </GuestGuard>
  )
}

export default RegisterPage