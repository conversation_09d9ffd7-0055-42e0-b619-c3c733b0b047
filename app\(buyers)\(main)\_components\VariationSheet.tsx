'use client';

import React, { useState } from 'react';
import { Sheet, <PERSON><PERSON><PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { IoAdd, IoRemove } from 'react-icons/io5';

interface VariationSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  actionType: 'cart' | 'wishlist';
  sizes: { size: string; price: number; stock: string }[];
  unavailableSizes: string[];
}

const VariationSheet: React.FC<VariationSheetProps> = ({
  open,
  onOpenChange,
  actionType,
  sizes,
  unavailableSizes,
}) => {
  const [quantities, setQuantities] = useState<{ [key: string]: number }>({});

  const handleQuantityChange = (size: string, increment: boolean) => {
    setQuantities((prev) => ({
      ...prev,
      [size]: Math.max(0, (prev[size] || 0) + (increment ? 1 : -1)),
    }));
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="bottom" className="p-4 bg-white max-h-[75vh] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>
            {actionType === 'cart' ? 'Add to Cart' : 'Add to Wishlist'}
          </SheetTitle>
        </SheetHeader>

        <div className="mt-4 space-y-4">
          {sizes.map(({ size, price, stock }) => (
            <div key={size} className="flex justify-between items-center">
              <div>
                <span className="font-medium">{size}</span>
                <div className="flex gap-2">
                  <p className="font-medium">GH₵{price}</p>
                  <p className="text-sm text-gray-500 line-through">GH₵{price + 25}</p>
                </div>
                <p className={`text-sm ${stock === 'unavailable' ? 'text-red-500' : 'text-gray-500'}`}>{stock}</p>
              </div>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => handleQuantityChange(size, false)}
                  className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50"
                  disabled={unavailableSizes.includes(size)}
                >
                  <IoRemove size={20} />
                </button>
                <span className="text-lg font-medium">{quantities[size] || 0}</span>
                <button
                  onClick={() => handleQuantityChange(size, true)}
                  className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 disabled:opacity-50"
                  disabled={unavailableSizes.includes(size)}
                >
                  <IoAdd size={20} />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-between gap-2">
          <Button onClick={() => onOpenChange(false)} variant="outline" className="h-14 flex-1">
            Continue Shopping
          </Button>
          <Button onClick={() => onOpenChange(false)} className="bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200 h-14 flex-1">
            {actionType === 'cart' ? 'Add to Cart' : 'Add to Wishlist'}
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default VariationSheet;
