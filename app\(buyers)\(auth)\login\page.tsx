import React from 'react'
import Link from 'next/link'
import LoginForm from '../_components/LoginForm'
import { SocialAuthButtons } from '../_components/SocialAuthButtons'
import { GuestGuard } from '@/lib/components/AuthGuard'

const LoginPage = () => {
  return (
    <GuestGuard>
      <section className='flex flex-col items-center w-full max-w-md my-20'>
          {/* Header */}
          <h1 className="text-3xl font-bold text-gray-900">Everyfash</h1>

          {/* About */}
          <p className="text-base pt-2 mb-6 font-medium text-gray-800">Enter your login details to continue</p>

           {/* Social Auth Buttons */}
           <SocialAuthButtons role="buyer" />

          {/* Or */}
          <div className="my-4 flex items-center">
              <p className="text-gray-700">Or</p>
          </div>


          {/* Form */}
          <LoginForm role="buyer" />

          {/* Link to Login */}
          <div className="">
              <p className="text-left text-gray-600">Don't have an account? <Link href="/register" className="text-primary hover:text-primary/80 transition-colors duration-200">Create one</Link></p>
          </div>



      </section>
    </GuestGuard>
  )
}

export default LoginPage