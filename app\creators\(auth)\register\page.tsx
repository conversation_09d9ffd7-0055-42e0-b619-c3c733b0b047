import React from 'react'
import RegisterForm from '@/app/(buyers)/(auth)/_components/RegisterForm'
import Link from 'next/link'
import { SocialAuthButtons } from '@/app/(buyers)/(auth)/_components/SocialAuthButtons'
import { GuestGuard } from '@/lib/components/AuthGuard'
const CreatorRegisterPage = () => {
    return (
      <GuestGuard>
        <section className='flex flex-col items-center w-full max-w-md my-20'>
          {/* Header */}
          <h1 className="text-xl font-bold text-gray-900">Join Everyfash Vendors</h1>

          {/* About */}
          <p className="text-sm pt-2 px-2 text-center mb-3 font-medium text-gray-800">
          Join Africa’s largest fashion marketplace. Start selling today!
          </p>

          {/* Social Auth Buttons */}
          <SocialAuthButtons role="creator" />

          {/* Or */}
          <div className="my-2 flex items-center">
            <p className="text-gray-700">Or</p>
          </div>

          {/* Form */}
          <RegisterForm role="creator" />

          {/* Link to Login */}
          <div>
            <p className="text-left text-gray-600">
              Already part of Everyfash Vendors?{' '}
              <Link href="/creators/login" className="text-primary hover:text-primary/80 transition-colors duration-200">
                Login
              </Link>
            </p>
          </div>
        </section>
      </GuestGuard>
    );
  };

  export default CreatorRegisterPage;
