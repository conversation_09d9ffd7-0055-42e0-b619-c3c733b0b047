import { useQuery } from '@tanstack/react-query';
import { categoriesApi, handleCategoriesApiError } from '@/lib/api';
import { useCategories, useCategoriesActions } from '@/lib/stores/categories-store';

// Query keys
export const categoriesKeys = {
  all: ['categories'] as const,
  hierarchy: () => [...categoriesKeys.all, 'hierarchy'] as const,
};

// Categories hierarchy query
export const useCategoriesHierarchy = () => {
  const { categories, isLoading: storeLoading, error: storeError, lastFetched } = useCategories();
  const { setCategories, setLoading, setError, clearError } = useCategoriesActions();

  // Check if data is stale (older than 1 hour)
  const isStale = !lastFetched || (Date.now() - lastFetched) > 60 * 60 * 1000;

  const query = useQuery({
    queryKey: categoriesKeys.hierarchy(),
    queryFn: async () => {
      try {
        setLoading(true);
        clearError();
        
        const response = await categoriesApi.getHierarchy();
        
        if (response.status === 'success' && response.data.categories) {
          setCategories(response.data.categories);
          return response.data.categories;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error) {
        const errorMessage = handleCategoriesApiError(error);
        setError(errorMessage);
        throw error;
      } finally {
        setLoading(false);
      }
    },
    enabled: categories.length === 0 || isStale, // Only fetch if no data or data is stale
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors except 408, 429
      if (error instanceof Error) {
        const status = (error as any).status;
        if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
          return false;
        }
      }
      return failureCount < 3;
    },
  });

  return {
    categories: categories.length > 0 ? categories : query.data || [],
    isLoading: storeLoading || query.isLoading,
    error: storeError || (query.error ? handleCategoriesApiError(query.error) : null),
    isSuccess: query.isSuccess,
    refetch: query.refetch,
  };
};

// Hook to get a specific category by ID
export const useCategoryById = (categoryId: string) => {
  const { categories } = useCategories();
  
  const findCategoryById = (cats: any[], id: string): any => {
    for (const cat of cats) {
      if (cat.id === id) return cat;
      if (cat.immediateChildren) {
        const found = findCategoryById(cat.immediateChildren, id);
        if (found) return found;
      }
    }
    return null;
  };

  return findCategoryById(categories, categoryId);
};

// Hook to get a specific category by slug
export const useCategoryBySlug = (slug: string) => {
  const { categories } = useCategories();
  
  const findCategoryBySlug = (cats: any[], targetSlug: string): any => {
    for (const cat of cats) {
      if (cat.slug === targetSlug) return cat;
      if (cat.immediateChildren) {
        const found = findCategoryBySlug(cat.immediateChildren, targetSlug);
        if (found) return found;
      }
    }
    return null;
  };

  return findCategoryBySlug(categories, slug);
};
