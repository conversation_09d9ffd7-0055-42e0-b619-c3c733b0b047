'use client';

import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

export default function AccountManagementPage() {
  const { isAuthenticated, user: authUser } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="account" />;
  }

  // Sample data (you may replace this with user data from your API or context)
  const user = {
    fullName: authUser?.name || '<PERSON>',
    email: authUser?.email || '<EMAIL>',
    phoneNumber: '**********',
  };

  return (
    <section className="w-full mt-20 space-y-6">
      <h2 className="text-sm uppercase font-medium text-gray-600 mb-1">Account Management</h2>

      <div className="bg-white p-4 rounded shadow-sm space-y-3">
        <h3 className="font-semibold text-sm text-gray-600">Account Overview</h3>
        <p className="text-gray-700 text-sm">
          <span className="font-medium">Full Name:</span> {user.fullName}
        </p>
        <p className="text-gray-700 text-sm">
          <span className="font-medium">Email:</span> {user.email}
        </p>
        <p className="text-gray-700 text-sm">
          <span className="font-medium">Phone Number:</span> {user.phoneNumber}
        </p>

        <div className="flex flex-col sm:flex-row gap-3 pt-3">
          <Button variant="outline" asChild>
            <a href="/account/profile">Edit Profile</a>
          </Button>
          <Button variant="outline" asChild>
            <a href="/account/security">Update Security</a>
          </Button>
        </div>
      </div>

      <Separator />

      <Button variant="secondary" className="w-full mt-2">
        Logout
      </Button>
    </section>
  );
}
