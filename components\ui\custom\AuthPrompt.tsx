'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { ShoppingBag, Heart, ShoppingCart, User, Sparkles, Store } from 'lucide-react';
import { motion } from 'framer-motion';

interface AuthPromptProps {
  type: 'orders' | 'cart' | 'wishlist' | 'account' | 'general' | 'creator';
  title?: string;
  description?: string;
}

const AuthPrompt = ({ type, title, description }: AuthPromptProps) => {
  const router = useRouter();

  const getPageConfig = () => {
    switch (type) {
      case 'orders':
        return {
          icon: ShoppingBag,
          title: title || "Track Your Orders",
          description: description || "View your order history, track deliveries, and manage returns. Sign in to access your complete order information.",
          primaryAction: "Create Account",
          secondaryAction: "Sign In",
        };
      case 'cart':
        return {
          icon: ShoppingCart,
          title: title || "Save Your Cart",
          description: description || "Keep your items safe and checkout faster. Sign in to save your cart and never lose your favorite finds.",
          primaryAction: "Create Account",
          secondaryAction: "Sign In",
        };
      case 'wishlist':
        return {
          icon: Heart,
          title: title || "Build Your Wishlist",
          description: description || "Save items you love and get notified when they go on sale. Create an account to start building your wishlist.",
          primaryAction: "Create Account",
          secondaryAction: "Sign In",
        };
      case 'account':
        return {
          icon: User,
          title: title || "Manage Your Account",
          description: description || "Access your profile, addresses, and preferences. Sign in to manage your account settings and personal information.",
          primaryAction: "Create Account",
          secondaryAction: "Sign In",
        };
      case 'creator':
        return {
          icon: Store,
          title: title || "Access Your Creator Dashboard",
          description: description || "Manage your products, track sales, and grow your business. Sign in to access your creator tools and analytics.",
          primaryAction: "Create Creator Account",
          secondaryAction: "Sign In",
        };
      default:
        return {
          icon: Sparkles,
          title: title || "Join Everyfash",
          description: description || "Create an account to access all features and enjoy a personalized shopping experience.",
          primaryAction: "Create Account",
          secondaryAction: "Sign In",
        };
    }
  };

  const config = getPageConfig();
  const IconComponent = config.icon;

  const handlePrimaryAction = () => {
    if (type === 'creator') {
      router.push('/creators/register');
    } else {
      router.push('/register');
    }
  };

  const handleSecondaryAction = () => {
    if (type === 'creator') {
      router.push('/creators/login');
    } else {
      router.push('/login');
    }
  };

  return (
    <section className="w-full mt-16 py-4  flex items-center justify-center">
      <div className="w-full mx-auto px-2">
        {/* Main Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-sm p-6 text-center"
        >
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary flex items-center justify-center"
          >
            <IconComponent className="w-8 h-8 text-primary-foreground" />
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-lg font-semibold text-gray-800 mb-3"
          >
            {config.title}
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-sm text-gray-600 mb-6 leading-relaxed"
          >
            {config.description}
          </motion.p>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="space-y-3"
          >
            <Button
              onClick={handlePrimaryAction}
              className="w-full h-10 bg-primary hover:bg-primary/90 text-primary-foreground transition-colors duration-200"
            >
              {config.primaryAction}
            </Button>

            <Button
              onClick={handleSecondaryAction}
              variant="outline"
              className="w-full h-10 border-gray-300 hover:bg-gray-50 transition-colors duration-200"
            >
              {config.secondaryAction}
            </Button>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="mt-4 text-xs text-gray-500"
          >
            Join thousands of users on Everyfash
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default AuthPrompt;
