/**
 * API Response Interceptors
 * Handles common tasks like error logging, response transformation, and monitoring
 */

import { AuthManager } from './auth-manager';
import { ApiErrorHandler } from './errors';
import { BaseApiResponse } from './types';

export interface InterceptorContext {
  url: string;
  method: string;
  startTime: number;
  requestId: string;
  headers?: Record<string, string>;
}

export interface ResponseInterceptor {
  name: string;
  priority: number; // Lower numbers run first
  onResponse?: (response: Response, context: InterceptorContext) => Promise<Response>;
  onError?: (error: Error, context: InterceptorContext) => Promise<Error>;
  onSuccess?: (data: any, context: InterceptorContext) => Promise<any>;
}

export class InterceptorManager {
  private interceptors: ResponseInterceptor[] = [];
  private isLoggingEnabled: boolean = process.env.NODE_ENV === 'development';

  constructor() {
    this.registerDefaultInterceptors();
  }

  /**
   * Register a new interceptor
   */
  register(interceptor: ResponseInterceptor): void {
    this.interceptors.push(interceptor);
    this.interceptors.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Unregister an interceptor
   */
  unregister(name: string): void {
    this.interceptors = this.interceptors.filter(i => i.name !== name);
  }

  /**
   * Process response through all interceptors
   */
  async processResponse(
    response: Response,
    context: InterceptorContext
  ): Promise<Response> {
    let processedResponse = response;

    for (const interceptor of this.interceptors) {
      if (interceptor.onResponse) {
        try {
          processedResponse = await interceptor.onResponse(processedResponse, context);
        } catch (error) {
          console.error(`Interceptor ${interceptor.name} failed:`, error);
        }
      }
    }

    return processedResponse;
  }

  /**
   * Process error through all interceptors
   */
  async processError(
    error: Error,
    context: InterceptorContext
  ): Promise<Error> {
    let processedError = error;

    for (const interceptor of this.interceptors) {
      if (interceptor.onError) {
        try {
          processedError = await interceptor.onError(processedError, context);
        } catch (interceptorError) {
          console.error(`Interceptor ${interceptor.name} failed:`, interceptorError);
        }
      }
    }

    return processedError;
  }

  /**
   * Process successful response data through all interceptors
   */
  async processSuccess(
    data: any,
    context: InterceptorContext
  ): Promise<any> {
    let processedData = data;

    for (const interceptor of this.interceptors) {
      if (interceptor.onSuccess) {
        try {
          processedData = await interceptor.onSuccess(processedData, context);
        } catch (error) {
          console.error(`Interceptor ${interceptor.name} failed:`, error);
        }
      }
    }

    return processedData;
  }

  /**
   * Create request context
   */
  createContext(url: string, method: string, headers?: Record<string, string>): InterceptorContext {
    return {
      url,
      method,
      startTime: Date.now(),
      requestId: this.generateRequestId(),
      headers,
    };
  }

  /**
   * Register default interceptors
   */
  private registerDefaultInterceptors(): void {
    // Logging interceptor
    this.register({
      name: 'logger',
      priority: 1,
      onResponse: async (response, context) => {
        if (this.isLoggingEnabled) {
          const duration = Date.now() - context.startTime;
          console.log(`[API] ${context.method} ${context.url} - ${response.status} (${duration}ms)`);
        }
        return response;
      },
      onError: async (error, context) => {
        if (this.isLoggingEnabled) {
          const duration = Date.now() - context.startTime;
          console.error(`[API] ${context.method} ${context.url} - ERROR (${duration}ms):`, error);
        }
        return error;
      },
    });

    // Authentication interceptor
    this.register({
      name: 'auth',
      priority: 2,
      onResponse: async (response, context) => {
        // Handle authentication failures, but not on login/register endpoints
        if (response.status === 401) {
          const isAuthEndpoint = context.url.includes('/auth/login') ||
                                 context.url.includes('/auth/register') ||
                                 context.url.includes('/auth/verify-email') ||
                                 context.url.includes('/auth/forgot-password') ||
                                 context.url.includes('/auth/reset-password');

          // Only redirect if this is not an authentication endpoint
          // if (!isAuthEndpoint) {
          //   AuthManager.handleAuthFailure();
          // }
        }
        return response;
      },
    });

    // Response transformation interceptor
    this.register({
      name: 'transformer',
      priority: 3,
      onSuccess: async (data, context) => {
        // Standardize response format
        if (this.isApiResponse(data)) {
          return data;
        }

        // Wrap non-standard responses
        return {
          status: 'success',
          data,
        } as BaseApiResponse;
      },
    });

    // Performance monitoring interceptor
    this.register({
      name: 'performance',
      priority: 4,
      onResponse: async (response, context) => {
        const duration = Date.now() - context.startTime;
        
        // Log slow requests
        if (duration > 5000) { // 5 seconds
          console.warn(`[API] Slow request detected: ${context.method} ${context.url} (${duration}ms)`);
        }

        // Store performance metrics (could be sent to analytics)
        this.recordPerformanceMetric(context, duration, response.status);
        
        return response;
      },
    });

    // Error standardization interceptor
    this.register({
      name: 'error-standardizer',
      priority: 5,
      onError: async (error, context) => {
        // Standardize error format
        const standardizedError = ApiErrorHandler.handleError(error);
        
        // Create new error with standardized message
        const newError = new Error(standardizedError.message);
        (newError as any).code = standardizedError.code;
        (newError as any).status = standardizedError.status;
        
        return newError;
      },
    });

    // Rate limiting interceptor
    this.register({
      name: 'rate-limiter',
      priority: 6,
      onResponse: async (response, context) => {
        // Check for rate limiting headers
        const remaining = response.headers.get('X-RateLimit-Remaining');
        const reset = response.headers.get('X-RateLimit-Reset');
        
        if (remaining && parseInt(remaining) < 10) {
          console.warn(`[API] Rate limit warning: ${remaining} requests remaining`);
        }

        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After');
          console.warn(`[API] Rate limited. Retry after: ${retryAfter} seconds`);
        }

        return response;
      },
    });
  }

  /**
   * Check if data follows API response format
   */
  private isApiResponse(data: any): data is BaseApiResponse {
    return data && typeof data === 'object' && 'status' in data;
  }

  /**
   * Record performance metric
   */
  private recordPerformanceMetric(
    context: InterceptorContext,
    duration: number,
    status: number
  ): void {
    // This could be enhanced to send metrics to an analytics service
    const metric = {
      url: context.url,
      method: context.method,
      duration,
      status,
      timestamp: new Date().toISOString(),
      requestId: context.requestId,
    };

    // Store in session storage for debugging (in development)
    if (this.isLoggingEnabled && typeof window !== 'undefined') {
      try {
        const metrics = JSON.parse(sessionStorage.getItem('api_metrics') || '[]');
        metrics.push(metric);
        
        // Keep only last 100 metrics
        if (metrics.length > 100) {
          metrics.splice(0, metrics.length - 100);
        }
        
        sessionStorage.setItem('api_metrics', JSON.stringify(metrics));
      } catch (error) {
        // Ignore storage errors
      }
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Enable/disable logging
   */
  setLogging(enabled: boolean): void {
    this.isLoggingEnabled = enabled;
  }

  /**
   * Get performance metrics (for debugging)
   */
  getPerformanceMetrics(): any[] {
    if (typeof window === 'undefined') return [];
    
    try {
      return JSON.parse(sessionStorage.getItem('api_metrics') || '[]');
    } catch {
      return [];
    }
  }

  /**
   * Clear performance metrics
   */
  clearPerformanceMetrics(): void {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('api_metrics');
    }
  }
}

// Global interceptor manager instance
export const interceptorManager = new InterceptorManager();
