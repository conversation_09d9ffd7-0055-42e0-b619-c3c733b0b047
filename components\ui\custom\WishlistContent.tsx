'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

const wishlistProducts = Array.from({ length: 15 }, (_, i) => ({
  id: i + 1,
  name: `Product ${i + 1}`,
  price: (Math.random() * 100).toFixed(2),
  discount: Math.floor(Math.random() * 30) + 5,
  size: 'XL',
  image: `/images/ladies/dress1.jpg`,
  available: i %2 == 0,
}));

interface WishlistContentProps {
  currentPage: number;
}

interface Product {
    id: number;
    name: string;
    price: string;
    discount: number;
    size: string;
    image: string;
    available: boolean;
  }

const WishlistContent: React.FC<WishlistContentProps> = ({
  currentPage,
}) => {
  const [page, setPage] = useState(currentPage);
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="wishlist" />;
  }

  const productsPerPage = 5;
  const totalProducts = 15;
  const totalPages = Math.ceil(totalProducts / productsPerPage);
//   const productsPerPage = 5;
//   const totalPages = Math.ceil(wishlistProducts.length / productsPerPage);


  const [wishlistProducts, setWishlistProducts] = useState<Product[]>([]);

  useEffect(() => {
    // Only generate data on the client
    const generatedProducts = Array.from({ length: totalProducts }, (_, i) => ({
      id: i + 1,
      name: `Product ${i + 1}`,
      price: (Math.random() * 100).toFixed(2),
      discount: Math.floor(Math.random() * 30) + 5,
      size: 'XL',
      image: `/images/ladies/dress1.jpg`,
      available: Math.random() > 0.3,
    }));
    setWishlistProducts(generatedProducts);
  }, []);

  const paginatedProducts = wishlistProducts.slice(
    (currentPage - 1) * productsPerPage,
    currentPage * productsPerPage
  );

  useEffect(() => {
    setPage(currentPage);
  }, [currentPage]);



  const updatePage = (page: number) => {
    const params = new URLSearchParams();
    params.set('page', page.toString());
    router.push(`?${params.toString()}`);
  };

  return (
    <section className="w-full mt-20">
      <h1 className="text-lg font-semibold text-gray-600 mb-2 mt-2">
        Wishlist ({wishlistProducts.length})
      </h1>
      {paginatedProducts.map((product) => (
        <div
          key={product.id}
          className="bg-white rounded shadow-sm p-3 mb-3 flex flex-col"
        >
          <div className="flex">
            <div className="w-24 h-24 bg-gray-200">
              <img
                src={product.image}
                alt={product.name}
                className="w-full h-full object-cover rounded"
              />
            </div>

            <div className="ml-4 flex-1">
              <h2 className="font-medium text-gray-800">{product.name}</h2>
              <div className="flex gap-2 items-center justify-between text-gray-700 my-1">
                    <p className=" font-bold">GH₵{product.price}</p>
                    <div className="bg-primary-50 text-primary text-xs font-bold px-2 py-1 rounded">
                    -{product.discount}%
                    </div>
                </div>
                <p className="text-sm text-gray-500 line-through">GH₵{(Number(product.price) / (1 - product.discount / 100)).toFixed(2)}</p>

              <p className="text-sm text-gray-600 mt-1">Size: {product.size}</p>
            </div>
          </div>

          <div className="flex items-center mt-3 justify-between">
            <Button variant="link" className="text-sm px-3 py-1">
              Remove
            </Button>
            <Button
              className={
                product.available
                  ? 'text-sm px-3 py-1'
                  : 'text-sm px-3 py-1 cursor-not-allowed'
              }
              disabled={!product.available}
            >
              {product.available ? 'Buy Now' : 'Out of Stock'}
            </Button>
          </div>
        </div>
      ))}

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={() => updatePage(Math.max(page - 1, 1))}
          className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
          disabled={page === 1}
        >
          Previous
        </button>
        <span className="text-sm font-semibold">
          Page {page} of {totalPages}
        </span>
        <button
          onClick={() => updatePage(Math.min(page + 1, totalPages))}
          className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
          disabled={page === totalPages}
        >
          Next
        </button>
      </div>
    </section>
  );
};

export default WishlistContent;
