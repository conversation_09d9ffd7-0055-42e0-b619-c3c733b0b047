export interface Category {
  _id: string;
  name: string;
  description: string;
  featured: boolean;
  order: number;
  active: boolean;
  slug: string;
  id: string;
  immediateChildren?: Category[];
  childrenCount: number;
}

export interface CategoriesHierarchyResponse {
  status: string;
  results: number;
  data: {
    categories: Category[];
    totalParents: number;
    totalChildren: number;
  };
}

export interface CategoriesState {
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
}

export interface FlattenedCategory {
  id: string;
  name: string;
  description: string;
  slug: string;
  path: string[];
  level: number;
  parentId?: string;
}

export interface NavigationCategory {
  header: string;
  headerLink: string;
  links: NavigationLink[];
}

export interface NavigationLink {
  icon?: React.ReactNode;
  iconName?: string;
  image?: string;
  route: string;
  label: string;
  id: string;
}
