import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';
import { handleApiError as handleError } from './errors';
import { 
  BusinessInfoUpdateData, 
  PaymentInfoUpdateData, 
  ShopInfoUpdateData, 
  ShippingInfoUpdateData,
  BusinessInfoResponse,
  PaymentInfoResponse,
  ShopInfoResponse,
  ShippingInfoResponse
} from '@/lib/types/auth';

// Create API client instance
const apiClient = new BaseApiClient();

export const profileApi = {
  // Business Info Profile Management
  getBusinessInfo: async (): Promise<BusinessInfoResponse> => {
    try {
      return await apiClient.get<BusinessInfoResponse>(API_ENDPOINTS.PROFILE.BUSINESS_INFO);
    } catch (error) {
      throw handleError(error);
    }
  },

  updateBusinessInfo: async (data: BusinessInfoUpdateData): Promise<BusinessInfoResponse> => {
    try {
      const formData = new FormData();

      // Add required fields
      formData.append('businessName', data.businessName);
      formData.append('businessType', data.businessType);
      formData.append('ownerName', data.ownerName);
      formData.append('ownerID', data.ownerID);
      formData.append('phoneNumber', data.phoneNumber);
      formData.append('taxId', data.taxId);

      // Add business address
      formData.append('businessAddress[addressLine1]', data.businessAddress.addressLine1);
      if (data.businessAddress.addressLine2) {
        formData.append('businessAddress[addressLine2]', data.businessAddress.addressLine2);
      }
      formData.append('businessAddress[city]', data.businessAddress.city);
      formData.append('businessAddress[state]', data.businessAddress.state);
      formData.append('businessAddress[country]', data.businessAddress.country);
      if (data.businessAddress.digitalGps) {
        formData.append('businessAddress[digitalGps]', data.businessAddress.digitalGps);
      }

      // Add verification documents if provided
      if (data.verificationDocuments && data.verificationDocuments.length > 0) {
        data.verificationDocuments.forEach((file) => {
          formData.append('verificationDocuments', file);
        });
      }

      return await apiClient.put<BusinessInfoResponse>(API_ENDPOINTS.PROFILE.BUSINESS_INFO, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Payment Info Profile Management
  getPaymentInfo: async (): Promise<PaymentInfoResponse> => {
    try {
      return await apiClient.get<PaymentInfoResponse>(API_ENDPOINTS.PROFILE.PAYMENT_INFO);
    } catch (error) {
      throw handleError(error);
    }
  },

  updatePaymentInfo: async (data: PaymentInfoUpdateData): Promise<PaymentInfoResponse> => {
    try {
      return await apiClient.put<PaymentInfoResponse>(API_ENDPOINTS.PROFILE.PAYMENT_INFO, data);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Shop Info Profile Management
  getShopInfo: async (): Promise<ShopInfoResponse> => {
    try {
      return await apiClient.get<ShopInfoResponse>(API_ENDPOINTS.PROFILE.SHOP_INFO);
    } catch (error) {
      throw handleError(error);
    }
  },

  updateShopInfo: async (data: ShopInfoUpdateData): Promise<ShopInfoResponse> => {
    try {
      const formData = new FormData();

      // Add required fields
      formData.append('name', data.name);
      formData.append('sellerType', data.sellerType);

      // Add required contact information
      formData.append('contact[name]', data.contact.name);
      formData.append('contact[email]', data.contact.email);
      formData.append('contact[phone]', data.contact.phone);

      // Add optional description
      if (data.description) {
        formData.append('description', data.description);
      }

      // Add optional social media
      if (data.socialMedia?.instagram) {
        formData.append('socialMedia[instagram]', data.socialMedia.instagram);
      }
      if (data.socialMedia?.facebook) {
        formData.append('socialMedia[facebook]', data.socialMedia.facebook);
      }
      if (data.socialMedia?.twitter) {
        formData.append('socialMedia[twitter]', data.socialMedia.twitter);
      }
      if (data.socialMedia?.tiktok) {
        formData.append('socialMedia[tiktok]', data.socialMedia.tiktok);
      }
      if (data.socialMedia?.youtube) {
        formData.append('socialMedia[youtube]', data.socialMedia.youtube);
      }
      if (data.socialMedia?.website) {
        formData.append('socialMedia[website]', data.socialMedia.website);
      }

      // Add files if provided
      if (data.logo) {
        formData.append('logo', data.logo);
      }
      if (data.banner) {
        formData.append('banner', data.banner);
      }

      return await apiClient.put<ShopInfoResponse>(API_ENDPOINTS.PROFILE.SHOP_INFO, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Shipping Info Profile Management
  getShippingInfo: async (): Promise<ShippingInfoResponse> => {
    try {
      return await apiClient.get<ShippingInfoResponse>(API_ENDPOINTS.PROFILE.SHIPPING_INFO);
    } catch (error) {
      throw handleError(error);
    }
  },

  updateShippingInfo: async (data: ShippingInfoUpdateData): Promise<ShippingInfoResponse> => {
    try {
      return await apiClient.put<ShippingInfoResponse>(API_ENDPOINTS.PROFILE.SHIPPING_INFO, data);
    } catch (error) {
      throw handleError(error);
    }
  },
};
