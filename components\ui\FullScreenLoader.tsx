'use client';

import React from 'react';
import <PERSON><PERSON><PERSON>oa<PERSON> from './LottieLoader';
import { cn } from '@/lib/utils';

interface FullScreenLoaderProps {
  /**
   * Loading text to display below the animation
   */
  text?: string;
  /**
   * Title text to display above the animation
   */
  title?: string;
  /**
   * Size of the loader animation
   * @default 'lg'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /**
   * Custom className for the container
   */
  className?: string;
  /**
   * Whether to use min-h-screen or full height
   * @default true
   */
  fullScreen?: boolean;
}

const FullScreenLoader: React.FC<FullScreenLoaderProps> = ({
  text = 'Loading...',
  title,
  size = 'lg',
  className,
  fullScreen = true,
}) => {
  if (fullScreen) {
    return (
      <div
        className={cn(
          'fixed inset-0 flex items-center justify-center bg-white z-50',
          className
        )}
      >
        <div className="text-center space-y-6 px-4">
          {title && (
            <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">
              {title}
            </h2>
          )}
          <LottieLoader
            size={size}
            text={text}
            textSize="lg"
            centered={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center bg-white w-full h-full',
        className
      )}
    >
      <div className="text-center space-y-6 px-4">
        {title && (
          <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">
            {title}
          </h2>
        )}
        <LottieLoader
          size={size}
          text={text}
          textSize="lg"
          centered={true}
        />
      </div>
    </div>
  );
};

export default FullScreenLoader;
