"use client";

import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import ProfileLayout from "./_components/ProfileLayout";
import { BusinessInfoForm, PaymentInfoForm, ShopInfoForm, ShippingInfoForm } from "@/lib/components/shared/forms";

const ProfilePageContent = () => {
  const searchParams = useSearchParams();
  const currentStep = searchParams.get("step") || "shop-info";

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "shop-info":
        return <ShopInfoForm isProfileMode={true} />;
      case "business-info":
        return <BusinessInfoForm isProfileMode={true} />;
      case "payment-info":
        return <PaymentInfoForm isProfileMode={true} />;
      case "shipping-info":
        return <ShippingInfoForm isProfileMode={true} />;
      default:
        return <ShopInfoForm isProfileMode={true} />;
    }
  };

  return (
    <ProfileLayout>
      {renderCurrentStep()}
    </ProfileLayout>
  );
};

const ProfilePage = () => {
  return (
    <Suspense fallback={<div>Loading profile...</div>}>
      <ProfilePageContent />
    </Suspense>
  );
};

export default ProfilePage;