"use client";
import React, { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { CheckCircleIcon, MinusCircleIcon, PlusCircleIcon } from "lucide-react";
import { Button } from "@/components/ui/button";


// Product type
interface Product {
  id: number;
  name: string;
  image: string;
  currentPrice: number;
  discount: number;
  promoPrice: number;
  promoStock: number;
  isAdded?: boolean;
}

// Promo criteria
const promoCriteria = {
  minDiscount: 5,
  maxDiscount: 50,
};

// Dummy product data
const availableProducts: Product[] = [
    {
      id: 1,
      name: "<PERSON> Dress",
      image: "/images/ladies/dress1.jpg",
      currentPrice: 250,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 2,
      name: "<PERSON><PERSON> Jacket",
      image: "/images/ladies/jacket.jpg",
      currentPrice: 800,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 3,
      name: "<PERSON> Shorts",
      image: "/images/ladies/short.jpg",
      currentPrice: 3500,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 4,
      name: "Elegant Gown",
      image: "/images/ladies/tops.jpg",
      currentPrice: 1200,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 5,
      name: "Casual Sneakers",
      image: "/images/ladies/dress4.jpg",
      currentPrice: 600,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 6,
      name: "Formal Blazer",
      image: "/images/men/Shirts_TN.png",
      currentPrice: 1500,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 7,
      name: "Stylish Handbag",
      image: "/images/men/Shirts_TN.png",
      currentPrice: 700,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 8,
      name: "Classic Watch",
      image: "/images/men/Shorts_TN.png",
      currentPrice: 2000,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 9,
      name: "Comfortable Sandals",
      image: "/images/men/Suits_TN.png",
      currentPrice: 300,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
    {
      id: 10,
      name: "Trendy Hoodie",
      image: "/images/men/Sweaters_TN.png",
      currentPrice: 950,
      discount: 0,
      promoPrice: 0,
      promoStock: 0,
    },
  ];


const JoinPromoPage: React.FC = () => {
  const [products, setProducts] = useState<Product[]>(availableProducts);

  // Handle input changes (discount + promo stock)
   // Handle discount and promo stock changes
   const handleInputChange = (id: number, field: keyof Product, value: string) => {
    const updatedProducts = products.map((product) => {
      if (product.id === id) {
        const updatedProduct = { ...product, [field]: parseFloat(value) || 0 };

        if (field === "discount") {
          const discount = parseFloat(value) || 0;
          const promoPrice = product.currentPrice - (product.currentPrice * discount) / 100;
          updatedProduct.promoPrice = parseFloat(promoPrice.toFixed(2));
        }

        return updatedProduct;
      }
      return product;
    });

    setProducts(updatedProducts);
  };

  // Toggle product add/remove
  const toggleProduct = (id: number) => {
    const updatedProducts = products.map((product) =>
      product.id === id ? { ...product, isAdded: !product.isAdded } : product
    );
    setProducts(updatedProducts);
  };

  // Check if product is eligible to add based on discount
  const isEligible = (discount: number, stock: number) => discount >= promoCriteria.minDiscount && discount <= promoCriteria.maxDiscount && stock > 1;



  return (
    <section className="w-full py-6">
      {/* Promo Name */}
      <h1 className="text-sm font-semibold text-gray-800 mb-2">🔥 Summer Sale Promotion</h1>

      {/* Promo Criteria */}
      <div className="bg-primary-50 border border-primary/20 rounded-lg p-4 mb-2">
        <h2 className="text-sm text-gray-700 font-medium">
          Discount Criteria: Min 5% | Max 50%
        </h2>
      </div>

      {/* Product Table */}
      <div className="overflow-x-auto bg-white shadow-md rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-auto whitespace-nowrap">Product</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Current Price (GH₵)</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Discount (%)</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Promo Price (GH₵)</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Promo Stock</TableHead>
              <TableHead className="w-auto whitespace-nowrap text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.length > 0 ? (
              products.map((product) => (
                <TableRow key={product.id} className="hover:bg-gray-50 transition-colors">
                  <TableCell className="w-auto whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <img src={product.image} alt={product.name} className="w-10 h-10 object-cover rounded-md" />
                      <span className="font-medium text-gray-800">{product.name}</span>
                    </div>
                  </TableCell>

                  <TableCell className="text-center">GH₵{product.currentPrice}</TableCell>

                  <TableCell className="text-center">
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={product.discount}
                      onChange={(e) => handleInputChange(product.id, "discount", e.target.value)}
                      className="text-center px-1"
                    />
                  </TableCell>

                  <TableCell className="text-center">
                    <Input type="text" value={product.promoPrice} disabled className="text-center bg-gray-100" />
                  </TableCell>

                  <TableCell className="text-center">
                    <Input
                      type="number"
                      min="0"
                      value={product.promoStock}
                      onChange={(e) => handleInputChange(product.id, "promoStock", e.target.value)}
                      className="text-center px-1"
                    />
                  </TableCell>

                  <TableCell className="text-center w-auto whitespace-nowrap">
                  {(product.isAdded  &&  isEligible(product.discount, product.promoStock))? (
                    <Button
                      onClick={() => toggleProduct(product.id)}
                      className=" mr-4"
                      variant={"destructive"}
                    >
                      <MinusCircleIcon className="h-5 w-5 inline" />
                      <span className="ml-1">Remove</span>
                    </Button>
                  ) : (
                    <Button
                      onClick={() => toggleProduct(product.id)}
                      disabled={!isEligible(product.discount, product.promoStock)}
                      className={`${
                        isEligible(product.discount, product.promoStock)
                          ? "bg-green-500 hover:bg-green-700"
                          : "text-gray-400 cursor-not-allowed"
                      }`}
                    >
                      <PlusCircleIcon className="h-5 w-5 inline" />
                      <span className="ml-1">Add</span>
                    </Button>
                  )}
                  {/* {(product.isAdded  &&  isEligible(product.discount, product.promoStock)) && (
                    <CheckCircleIcon className="h-5 w-5 inline text-green-500 ml-1" />
                  )} */}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                  No products available to join this promotion.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </section>
  );
};

export default JoinPromoPage;
