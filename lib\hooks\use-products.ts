import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { productsApi } from '@/lib/api';
import { CreateProductData, Product } from '@/lib/types/products';
import { getApiErrorMessage, getApiErrorTitle } from '@/lib/utils/error-utils';

// Query keys
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
};

// Get creator's products with optional query parameters
export const useCreatorProducts = (params?: {
  page?: number;
  limit?: number;
  sort?: string;
  search?: string;
  status?: string;
  fields?: string;
}) => {
  return useQuery({
    queryKey: productKeys.list(JSON.stringify(params || {})),
    queryFn: () => productsApi.getCreatorProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Get product counts for filter tabs
export const useProductCounts = () => {
  return useQuery({
    queryKey: [...productKeys.all, 'counts'],
    queryFn: () => productsApi.getProductCounts(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Get single product
export const useProduct = (productId: string) => {
  return useQuery({
    queryKey: productKeys.detail(productId),
    queryFn: () => productsApi.getCreatorProduct(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Get single product (for creators with extended data)
export const useCreatorProduct = (productId: string) => {
  return useQuery({
    queryKey: [...productKeys.detail(productId), 'creator'],
    queryFn: () => productsApi.getCreatorProduct(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) or 404 (not found) errors
      if (error?.message?.includes('Session expired') || error?.message?.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Create product mutation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductData) => productsApi.createProduct(data),
    onSuccess: (data) => {
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Product Created Successfully',
        description: 'Your product has been created and is pending approval.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product mutation
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: string; data: Partial<CreateProductData> }) => 
      productsApi.updateProduct(productId, data),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);
      
      // Invalidate and refetch products list
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Product Updated Successfully',
        description: 'Your product has been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Delete product mutation
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => productsApi.deleteProduct(productId),
    onSuccess: (data, productId) => {
      // Remove the product from cache
      queryClient.removeQueries({ queryKey: productKeys.detail(productId) });
      
      // Invalidate and refetch products list and count
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      queryClient.invalidateQueries({ queryKey: ['products', 'count'] });

      // Show success message
      toast({
        title: 'Product Deleted',
        description: 'Your product has been deleted successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product specifications mutation
export const useUpdateProductSpecifications = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, specifications }: {
      productId: string;
      specifications: {
        mainMaterial?: string;
        dressStyle?: string;
        pantType?: string;
        skirtType?: string;
        mensPantSize?: string;
        fitType?: string;
        pattern?: string;
        closure?: string;
        neckline?: string;
        sleeveLength?: string;
        waistline?: string;
        hemline?: string;
      }
    }) => productsApi.updateProductSpecifications(productId, specifications),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Specifications Updated',
        description: 'Product specifications have been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update single product variation mutation
export const useUpdateProductVariation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, variationId, variation }: {
      productId: string;
      variationId: string;
      variation: {
        color?: string;
        size?: string;
        quantity?: number;
        price?: number;
        salePrice?: number;
        saleStartDate?: string;
        saleEndDate?: string;
      }
    }) => productsApi.updateProductVariation(productId, variationId, variation),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Variation Updated',
        description: 'Product variation has been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product basic info mutation
export const useUpdateProductInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, productInfo }: {
      productId: string;
      productInfo: {
        name?: string;
        brand?: string;
        description?: string;
        basePrice?: number;
        gender?: string;
        highlights?: string[];
        tags?: string[];
      }
    }) => productsApi.updateProductBasicInfo(productId, productInfo),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Product Info Updated',
        description: 'Product information has been updated successfully.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Product statistics hook (derived from products data)
export const useProductStats = () => {
  const { data: productsResponse, isLoading, error } = useCreatorProducts();
  
  const products = productsResponse?.data?.products || [];
  
  const stats = {
    total: products.length,
    pending: products.filter(p => p.status === 'pending').length,
    approved: products.filter(p => p.status === 'approved').length,
    rejected: products.filter(p => p.status === 'rejected').length,
    draft: products.filter(p => p.status === 'draft').length,
    totalStock: products.reduce((sum, p) => sum + p.totalStock, 0),
    totalSold: products.reduce((sum, p) => sum + p.sold, 0),
    averageRating: products.length > 0 
      ? products.reduce((sum, p) => sum + p.ratingsAverage, 0) / products.length 
      : 0,
    totalReviews: products.reduce((sum, p) => sum + p.ratingsQuantity, 0),
  };

  return {
    stats,
    products,
    isLoading,
    error,
  };
};

// Update product basic info mutation
export const useUpdateProductBasicInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, basicInfo }: {
      productId: string;
      basicInfo: {
        name?: string;
        brand?: string;
        description?: string;
        gender?: string;
        basePrice?: number;
        highlights?: string[];
        tags?: string[];
      }
    }) => productsApi.updateProductBasicInfo(productId, basicInfo),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      // Show success message
      toast({
        title: 'Product Updated',
        description: 'Product information has been updated successfully.',
        className: 'bg-green-100 text-green-800 border-green-200',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Add product variation mutation
export const useAddProductVariation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, variation }: {
      productId: string;
      variation: {
        color: string;
        size: string;
        quantity: number;
        price: number;
        sku?: string;
      }
    }) => productsApi.addProductVariation(productId, variation),
    onSuccess: (data, variables) => {
      // Update the cached product data
      queryClient.setQueryData(productKeys.detail(variables.productId), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      toast({
        title: 'Variation Added',
        description: 'Product variation has been successfully added.',
        className: "bg-green-100 text-green-800 border-green-200",
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Add Failed',
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Delete product variation mutation
export const useDeleteProductVariation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, variationId }: {
      productId: string;
      variationId: string;
    }) => productsApi.deleteProductVariation(productId, variationId),
    onSuccess: (data, variables) => {
      // Invalidate and refetch product details
      queryClient.invalidateQueries({ queryKey: productKeys.detail(variables.productId) });
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      toast({
        title: 'Variation Deleted',
        description: 'Product variation has been successfully deleted.',
        className: "bg-green-100 text-green-800 border-green-200",
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Delete Failed',
        description: getApiErrorMessage(error),
      });
    },
  });
};

// Update product images mutation
export const useUpdateProductImages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productId, images, productImages }: {
      productId: string;
      images?: string[];
      productImages?: File[];
    }) => productsApi.updateProductImages(productId, { images, productImages }),
    onSuccess: (data, variables) => {
      // Update the specific product in cache with new images
      queryClient.setQueryData(
        [...productKeys.detail(variables.productId), 'creator'],
        (oldData: any) => {
          if (oldData) {
            return {
              ...oldData,
              data: {
                ...oldData.data,
                product: {
                  ...oldData.data.product,
                  images: data.data.product.images
                }
              }
            };
          }
          return oldData;
        }
      );

      // Invalidate products list to reflect changes
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });

      toast({
        title: 'Images Updated',
        description: `Product images updated successfully. ${data.data.summary.addedImages} added, ${data.data.summary.removedImages} removed.`,
        className: 'bg-green-100 text-green-800 border-green-200',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};
