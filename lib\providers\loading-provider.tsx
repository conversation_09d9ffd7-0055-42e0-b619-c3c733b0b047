'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import FullScreenLoader from '@/components/ui/FullScreenLoader';

interface LoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean, text?: string) => void;
  loadingText: string;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: React.ReactNode;
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState('Loading...');
  const router = useRouter();

  const setLoading = (loading: boolean, text: string = 'Loading...') => {
    setIsLoading(loading);
    setLoadingText(text);
  };

  // Listen to route changes for automatic loading states
  useEffect(() => {
    const handleStart = () => setLoading(true, 'Loading page...');
    const handleComplete = () => setLoading(false);

    // For Next.js app router, we'll use a different approach
    // since router events are not available in app router
    let timeoutId: NodeJS.Timeout;

    const handleRouteChange = () => {
      setLoading(true, 'Loading page...');
      
      // Auto-hide after a reasonable time if page loads
      timeoutId = setTimeout(() => {
        setLoading(false);
      }, 3000);
    };

    // Clean up timeout on unmount
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []);

  return (
    <LoadingContext.Provider value={{ isLoading, setLoading, loadingText }}>
      {children}
      {isLoading && (
        <div className="fixed inset-0 z-50 bg-white">
          <FullScreenLoader 
            text={loadingText} 
            size="xl"
            className="bg-white"
          />
        </div>
      )}
    </LoadingContext.Provider>
  );
}

export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
}
