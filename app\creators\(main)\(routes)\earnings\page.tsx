"use client"
import { useState } from "react";
import { FaArrowUp, FaArrowDown } from "react-icons/fa";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerDescription, DrawerClose } from "@/components/ui/drawer";
import TransactionsChart from "./_components/TransactionsChart";
import PaginationComponent from "../../_components/PaginationComponent";
import OrderItemsCarousel from "./_components/OrderItemsCarousel";
import VerificationGuard from "@/lib/components/VerificationGuard";

// Earnings/Transactions Page
export default function EarningsPage() {

  const [open, setOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  const handleViewDetails = (order: any) => {
    setSelectedOrder(order);
    setOpen(true);
  };
// Dummy data
const revenueData = [
  { id: 1, orderId: "#1001", amount: "GHS 2,000", status: "Paid", pending: "GHS 0" },
  { id: 2, orderId: "#1002", amount: "GHS 1,500", status: "Pending", pending: "GHS 1,500" },
];

const payoutsData = [
  { id: 1, payoutId: "#P001", amount: "GHS 5,000", date: "18-02-2024", method: "Bank Transfer" },
  { id: 2, payoutId: "#P002", amount: "GHS 3,000", date: "22-02-2024", method: "Mobile Money" },
];

// Dummy order data
const order = {
  orderNumber: "********",
  placedOn: "18-02-2024",
  total: "GHS 400.00",
  deliveryDate: "21-02-2024",
  status: "Delivered",
  items: [
    {
      id: "1",
      productName: "Elegant Dress",
      size: "M",
      price: "GHS 150.00",
      quantity: 40,
      image: "/images/ladies/dress1.jpg",
    },
    {
      id: "2",
      productName: "Jacket",
      size: "42",
      price: "GHS 250.00",
      quantity: 40,
      image: "/images/ladies/jacket.jpg",
    },
    {
      id: "3",
      productName: "Dress 2",
      size: "42",
      price: "GHS 250.00",
      quantity: 40,
      image: "/images/ladies/dress3.jpg",
    },
    {
      id: "4",
      productName: "Leggings",
      size: "42",
      price: "GHS 250.00",
      quantity: 40,
      image: "/images/ladies/leggings.jpg",
    },
    {
      id: "5",
      productName: "Shorts",
      size: "42",
      price: "GHS 250.00",
      quantity: 40,
      image: "/images/ladies/short.jpg",
    },
  ],
  paymentMethod: "Card",
  paymentDetails: {
    itemsTotal: "GHS 400.00",
    deliveryFees: "GHS 8.06",
    total: "GHS 408.06",
  },
  deliveryMethod: "Everyfash Express",
  shippingAddress: {
    name: "John Doe",
    address: "123 Accra Street, Ghana",
  },
  shippingDetails: "Door Delivery Fulfilled by Everyfash",
};


  return (
    <VerificationGuard feature="earnings">
      <div className="">
      {/* Page Title */}
      <h1 className="text-sm text-gray-600 mb-3 mt-4">Earnings Overview</h1>

      {/* Overview Cards */}
      <section className="grid grid-cols-3 gap-1 mb-4">
        <OverviewCard title="Due & Unpaid (30 Days)" amount="GHS 5,000" />
        <OverviewCard title="Total Revenue" amount="GHS 100,000" />
        <OverviewCard title="Total Payout" amount="GHS 85,000" />
      </section>

      {/* Revenue Graph Placeholder */}
      <section className="bg-white p-2 rounded-lg shadow-md mb-4">
        <h2 className=" text-gray-600 text-sm py-2">Revenue Over Time</h2>

        <TransactionsChart/>
      </section>

      {/* Transactions Section */}
      <section>
        <Tabs defaultValue="revenue">
          <TabsList className="flex mx-0 items-center w-full justify-between">
             <h2 className="text-sm uppercase text-gray-600 font-semibold ">Transactions</h2>
             <section className="flex gap-1">
              <TabsTrigger className="rounded-xl text-white bg-primary data-[state=active]:bg-black data-[state=active]:text-white text-sm font-poppins" value="revenue">Revenue</TabsTrigger>
              <TabsTrigger className="rounded-xl text-white bg-primary data-[state=active]:bg-black data-[state=active]:text-white text-sm font-poppins" value="payouts">Payouts</TabsTrigger>
             </section>
          </TabsList>

          <TabsContent value="revenue">
            <section className="w-full">
              <Table className="bg-white rounded-lg shadow-md">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Pending Amount</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {revenueData.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>{order.orderId}</TableCell>
                        <TableCell>{order.amount}</TableCell>
                        <TableCell className={getStatusColor(order.status)}>{order.status}</TableCell>
                        <TableCell>{order.pending}</TableCell>
                        <TableCell>
                          <button className="text-primary underline" onClick={() => handleViewDetails(order)}>
                            View Details
                          </button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>


              <PaginationComponent
                  totalPages={10}
                />
            </section>

          </TabsContent>

          <TabsContent value="payouts">
            <section className="w-full">
              <Table className="bg-white rounded-lg shadow-md">
                <TableHeader>
                  <TableRow>
                    <TableHead>Payout ID</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Method</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payoutsData.map((payout) => (
                    <TableRow key={payout.id}>
                      <TableCell>{payout.payoutId}</TableCell>
                      <TableCell>{payout.amount}</TableCell>
                      <TableCell>{payout.date}</TableCell>
                      <TableCell>{payout.method}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <PaginationComponent
                    totalPages={10}
                  />
            </section>

          </TabsContent>
        </Tabs>


      {/* Order Details Drawer */}
      <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className=" mx-auto min-h-[50vh] rounded-t-lg">
        <DrawerHeader>
          <DrawerTitle>Order Details - {order.orderNumber}</DrawerTitle>
          <DrawerDescription>Earnings and breakdown for this order</DrawerDescription>
        </DrawerHeader>

        <div className="p-2 space-y-4 mb-10">

          <OrderItemsCarousel items={order.items} />
          <DrawerClose className="mt-4 bg-primary text-white px-4 py-2 rounded-lg w-full text-center">
            Close
          </DrawerClose>
        </div>
      </DrawerContent>
    </Drawer>

      </section>
      </div>
    </VerificationGuard>
  );
}

// Overview Card Component
function OverviewCard({ title, amount }: { title: string; amount: string }) {
  return (
    <div className="bg-white p-2 flex flex-col w-full items-center justify-center rounded-lg shadow-md">
      <h4 className="text-gray-600 text-sm mb-2">{title}</h4>
      <h1 className="mb-3 text-gray-800 font-semibold">
        {amount}
      </h1>
    </div>
  );
}





// Status Color Helper
function getStatusColor(status: string) {
  switch (status) {
    case "Paid":
      return "text-green-500";
    case "Pending":
      return "text-yellow-500";
    case "Unpaid":
      return "text-red-500";
    default:
      return "text-gray-500";
  }
}

