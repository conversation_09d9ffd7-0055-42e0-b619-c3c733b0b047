import React from 'react'

const InputField = ({ label, name, value, onChange, type = "text", required = false, disabled = false }: any) => (
<div>
    <label className="block text-sm font-medium text-gray-700 mb-1">{label}{required && <span className="text-red-500">*</span>}</label>
    <input
    type={type}
    name={name}
    value={value}
    onChange={onChange}
    disabled={disabled}
    placeholder={label}
    className={`w-full border rounded-lg p-2 mt-1 ${disabled ? "bg-gray-100 text-gray-400" : ""}`}
    />
</div>
);

export default InputField