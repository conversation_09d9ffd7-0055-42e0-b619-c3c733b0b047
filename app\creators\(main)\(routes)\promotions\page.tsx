"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import PaginationComponent from "../../_components/PaginationComponent";
import CustomSearch from "@/components/ui/custom/CustomSearchBar";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import CustomTabsTrigger from "@/components/ui/custom/CustomTabsTrigger";

// Sample promotions data
const samplePromotions = [
  {
    id: "PROMO001",
    name: "Valentine’s Day Sale",
    registrationEnd: "Feb 10, 2025 11:59pm",
    period: {
      start: "Feb 12, 2025 12:00am",
      end: "Feb 14, 2025 11:59pm"
    },
    discount: { min: "10%", max: "25%" },
    status: "Ongoing",
  },
  {
    id: "PROMO002",
    name: "Easter Flash Sale",
    registrationEnd: "Mar 25, 2025 11:59pm",
    period: {
      start: "Mar 28, 2025 12:00am",
      end: "Mar 31, 2025 11:59pm"
    },
    discount: { min: "5%", max: "50%" },
    status: "Open",
  },
  {
    id: "PROMO003",
    name: "Back to School Bonanza",
    registrationEnd: "Aug 15, 2025 11:59pm",
    period: {
      start: "Aug 20, 2025 12:00am",
      end: "Aug 25, 2025 11:59pm"
    },
    discount: { min: "10%", max: "40%" },
    status: "Upcoming",
  },
  {
    id: "PROMO004",
    name: "Everyfash Independence Day Promo",
    registrationEnd: "Mar 03, 2025 11:59pm",
    period: {
      start: "Mar 05, 2025 12:00am",
      end: "Mar 06, 2025 11:59pm"
    },
    discount: { min: "15%", max: "50%" },
    status: "Upcoming",
  },
  {
    id: "PROMO005",
    name: "Everyfash Black Friday",
    registrationEnd: "Nov 20, 2025 11:59pm",
    period: {
      start: "Nov 22, 2025 12:00am",
      end: "Nov 29, 2025 11:59pm"
    },
    discount: { min: "10%", max: "60%" },
    status: "Upcoming",
  },
  {
    id: "PROMO006",
    name: "Farmer’s Day Discount",
    registrationEnd: "Dec 02, 2025 11:59pm",
    period: {
      start: "Dec 04, 2025 12:00am",
      end: "Dec 05, 2025 11:59pm"
    },
    discount: { min: "20%", max: "45%" },
    status: "Open",
  },
  {
    id: "PROMO007",
    name: "Labour Day Savings",
    registrationEnd: "Apr 30, 2025 11:59pm",
    period: {
      start: "May 01, 2025 12:00am",
      end: "May 01, 2025 11:59pm"
    },
    discount: { min: "10%", max: "30%" },
    status: "Expired",
  },
  {
    id: "PROMO008",
    name: "Christmas Mega Sale",
    registrationEnd: "Dec 20, 2025 11:59pm",
    period: {
      start: "Dec 23, 2025 12:00am",
      end: "Dec 26, 2025 11:59pm"
    },
    discount: { min: "25%", max: "70%" },
    status: "Upcoming",
  },
  {
    id: "PROMO009",
    name: "Everyfash Republic Day Special",
    registrationEnd: "Jun 30, 2025 11:59pm",
    period: {
      start: "Jul 01, 2025 12:00am",
      end: "Jul 01, 2025 11:59pm"
    },
    discount: { min: "10%", max: "50%" },
    status: "Open",
  },
  {
    id: "PROMO010",
    name: "Everyfash Cultural Festival Deals",
    registrationEnd: "Sep 20, 2025 11:59pm",
    period: {
      start: "Sep 25, 2025 12:00am",
      end: "Sep 27, 2025 11:59pm"
    },
    discount: { min: "15%", max: "45%" },
    status: "Upcoming",
  },
];


const PromotionsPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const totalPages = 2;
  const router = useRouter()

  const filteredPromotions = samplePromotions.filter((promo) => {
    if (activeTab === "all") return true;
    if (activeTab === "open") return promo.status === "Open";
    if (activeTab === "ongoing") return promo.status === "Ongoing";
    if (activeTab === "repleted") return promo.status === "Repleted";
    if (activeTab === "expired") return promo.status === "Expired";
    if (activeTab === "upcoming") return promo.status === "Upcoming";
    return promo;
  });



  return (
    <section className="w-full max-w-4xl mx-auto py-4">
      <h1 className="text-sm font-bold uppercase text-gray-600 mb-4">Promotions Management</h1>

      <section className="w-full mb-8">
        <CustomSearch placeholder="Search for promotion name..." />
      </section>

      <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)} className="mb-6">
        <TabsList className="bg-gray-100 w-[98vw] overflow-x-scroll flex space-x-3 mb-2 p-2 justify-around">
          <CustomTabsTrigger value="all">All</CustomTabsTrigger>
          <CustomTabsTrigger value="open">Open</CustomTabsTrigger>
          <CustomTabsTrigger value="ongoing">Ongoing</CustomTabsTrigger>
          <CustomTabsTrigger value="repleted">Repleted</CustomTabsTrigger>
          <CustomTabsTrigger value="expired">Expired</CustomTabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <section className="space-y-2">
              {filteredPromotions.length > 0 ? (
                filteredPromotions.map((promo) => (
                  <div
                    key={promo.id}
                    className="bg-white shadow-sm rounded-lg p-4 border border-gray-200"
                  >
                    <div className="flex justify-between items-center">
                      <h2 className="text-sm font-bold uppercase text-gray-600">
                        {promo.name}
                      </h2>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          promo.status === 'Open'
                            ? 'bg-green-100 text-green-600'
                            : promo.status === 'Ongoing'
                            ? 'bg-blue-100 text-primary/90'
                            : promo.status === 'Expired'
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {promo.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-700 mt-3">
                      <div>
                        <p className="font-semibold">Registration End:</p>
                        <p>{promo.registrationEnd}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period:</p>
                        <p>
                          {promo.period.start} - {promo.period.end}
                        </p>
                      </div>
                      <div>
                        <p className="font-semibold">Min Discount:</p>
                        <p>{promo.discount.min}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Max Discount:</p>
                        <p>{promo.discount.max}</p>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      {promo.status === 'Open' || promo.status === 'Upcoming' ? (
                        <button className="text-primary hover:text-primary/80 hover:no-underline text-sm transition-colors duration-200" onClick={()=> router.push('/creators/promotions/10')}>
                          Join the Promotion
                        </button>
                      ) : promo.status === 'Repleted' || promo.status === 'Ongoing' ? (
                        <button className="text-primary hover:text-primary/80 text-sm transition-colors duration-200"  onClick={()=> router.push('/creators/promotions/10/products')}>
                          View Products
                        </button>
                      ) : (
                        <button
                          className="text-gray-400 text-sm"
                          disabled
                        >
                          Join the Promotion
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No promotions found.
                </div>
              )}
            </section>

        </TabsContent>
        <TabsContent value="open">
          <section className="space-y-2">
              {filteredPromotions.length > 0 ? (
                filteredPromotions.map((promo) => (
                  <div
                    key={promo.id}
                    className="bg-white shadow-sm rounded-lg p-4 border border-gray-200"
                  >
                    <div className="flex justify-between items-center">
                      <h2 className="text-sm font-bold uppercase text-gray-600">
                        {promo.name}
                      </h2>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          promo.status === 'Open'
                            ? 'bg-green-100 text-green-600'
                            : promo.status === 'Ongoing'
                            ? 'bg-primary-100 text-primary-600'
                            : promo.status === 'Expired'
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {promo.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-700 mt-3">
                      <div>
                        <p className="font-semibold">Registration End:</p>
                        <p>{promo.registrationEnd}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period:</p>
                        <p>
                          {promo.period.start} - {promo.period.end}
                        </p>
                      </div>
                      <div>
                        <p className="font-semibold">Min Discount:</p>
                        <p>{promo.discount.min}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Max Discount:</p>
                        <p>{promo.discount.max}</p>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      {promo.status === 'Open' || promo.status === 'Upcoming' ? (
                        <button className="text-primary hover:text-primary/80 hover:no-underline text-sm transition-colors duration-200">
                          Join the Promotion
                        </button>
                      ) : promo.status === 'Repleted' || promo.status === 'Ongoing' ? (
                        <button className="text-primary hover:text-primary/80 text-sm transition-colors duration-200">
                          View Products
                        </button>
                      ) : (
                        <button
                          className="text-gray-400 text-sm"
                          disabled
                        >
                          Join the Promotion
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No promotions found.
                </div>
              )}
            </section>

        </TabsContent>
        <TabsContent value="ongoing">
          <section className="space-y-2">
              {filteredPromotions.length > 0 ? (
                filteredPromotions.map((promo) => (
                  <div
                    key={promo.id}
                    className="bg-white shadow-sm rounded-lg p-4 border border-gray-200"
                  >
                    <div className="flex justify-between items-center">
                      <h2 className="text-sm font-bold uppercase text-gray-600">
                        {promo.name}
                      </h2>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          promo.status === 'Open'
                            ? 'bg-green-100 text-green-600'
                            : promo.status === 'Ongoing'
                            ? 'bg-blue-100 text-primary/90'
                            : promo.status === 'Expired'
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {promo.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-700 mt-3">
                      <div>
                        <p className="font-semibold">Registration End:</p>
                        <p>{promo.registrationEnd}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period:</p>
                        <p>
                          {promo.period.start} - {promo.period.end}
                        </p>
                      </div>
                      <div>
                        <p className="font-semibold">Min Discount:</p>
                        <p>{promo.discount.min}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Max Discount:</p>
                        <p>{promo.discount.max}</p>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      {promo.status === 'Open' || promo.status === 'Upcoming' ? (
                        <button className="text-primary hover:no-underline text-sm ">
                          Join the Promotion
                        </button>
                      ) : promo.status === 'Repleted' || promo.status === 'Ongoing' ? (
                        <button className="text-primary text-sm ">
                          View Products
                        </button>
                      ) : (
                        <button
                          className="text-gray-400 text-sm"
                          disabled
                        >
                          Join the Promotion
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No promotions found.
                </div>
              )}
            </section>

        </TabsContent>
        <TabsContent value="repleted">
          <section className="space-y-2">
              {filteredPromotions.length > 0 ? (
                filteredPromotions.map((promo) => (
                  <div
                    key={promo.id}
                    className="bg-white shadow-sm rounded-lg p-4 border border-gray-200"
                  >
                    <div className="flex justify-between items-center">
                      <h2 className="text-sm font-bold uppercase text-gray-600">
                        {promo.name}
                      </h2>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          promo.status === 'Open'
                            ? 'bg-green-100 text-green-600'
                            : promo.status === 'Ongoing'
                            ? 'bg-blue-100 text-primary/90'
                            : promo.status === 'Expired'
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {promo.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-700 mt-3">
                      <div>
                        <p className="font-semibold">Registration End:</p>
                        <p>{promo.registrationEnd}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period:</p>
                        <p>
                          {promo.period.start} - {promo.period.end}
                        </p>
                      </div>
                      <div>
                        <p className="font-semibold">Min Discount:</p>
                        <p>{promo.discount.min}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Max Discount:</p>
                        <p>{promo.discount.max}</p>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      {promo.status === 'Open' || promo.status === 'Upcoming' ? (
                        <button className="text-primary hover:no-underline text-sm ">
                          Join the Promotion
                        </button>
                      ) : promo.status === 'Repleted' || promo.status === 'Ongoing' ? (
                        <button className="text-primary text-sm ">
                          View Products
                        </button>
                      ) : (
                        <button
                          className="text-gray-400 text-sm"
                          disabled
                        >
                          Join the Promotion
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No promotions found.
                </div>
              )}
            </section>

        </TabsContent>
        <TabsContent value="expired">
          <section className="space-y-2">
              {filteredPromotions.length > 0 ? (
                filteredPromotions.map((promo) => (
                  <div
                    key={promo.id}
                    className="bg-white shadow-sm rounded-lg p-4 border border-gray-200"
                  >
                    <div className="flex justify-between items-center">
                      <h2 className="text-sm font-bold uppercase text-gray-600">
                        {promo.name}
                      </h2>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          promo.status === 'Open'
                            ? 'bg-green-100 text-green-600'
                            : promo.status === 'Ongoing'
                            ? 'bg-blue-100 text-primary/90'
                            : promo.status === 'Expired'
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {promo.status}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs text-gray-700 mt-3">
                      <div>
                        <p className="font-semibold">Registration End:</p>
                        <p>{promo.registrationEnd}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Period:</p>
                        <p>
                          {promo.period.start} - {promo.period.end}
                        </p>
                      </div>
                      <div>
                        <p className="font-semibold">Min Discount:</p>
                        <p>{promo.discount.min}</p>
                      </div>
                      <div>
                        <p className="font-semibold">Max Discount:</p>
                        <p>{promo.discount.max}</p>
                      </div>
                    </div>

                    <div className="flex justify-center mt-4">
                      {promo.status === 'Open' || promo.status === 'Upcoming' ? (
                        <button className="text-primary hover:no-underline text-sm ">
                          Join the Promotion
                        </button>
                      ) : promo.status === 'Repleted' || promo.status === 'Ongoing' ? (
                        <button className="text-primary text-sm ">
                          View Products
                        </button>
                      ) : (
                        <button
                          className="text-gray-400 text-sm"
                          disabled
                        >
                          Join the Promotion
                        </button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No promotions found.
                </div>
              )}
            </section>

        </TabsContent>
      </Tabs>

      <PaginationComponent totalPages={totalPages} />
    </section>
  );
};

export default PromotionsPage;
