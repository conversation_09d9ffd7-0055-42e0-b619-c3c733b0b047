import { JSX } from 'react';
import { FaUserCircle, FaClipboard, FaShoppingCart, FaStore, FaInfoCircle, FaRegCommentDots, FaTags, FaTshirt, FaSuitcase, FaRunning, FaRedhat, FaShoppingBag, FaChild, FaSocks } from 'react-icons/fa';
import { FaTableTennisPaddleBall } from 'react-icons/fa6';
import { FiWatch } from 'react-icons/fi';
import { GiBelt, GiBootPrints, GiChelseaBoot, GiFlipFlops, GiLargeDress, GiMonclerJacket, GiPearlNecklace, GiRubberBoot, GiSandal, GiSkirt, GiSunglasses, GiTrousers, GiUnderwear, GiUnderwearShorts } from 'react-icons/gi';
import { IoHeartOutline, IoMailOutline } from 'react-icons/io5';
import { PiHighHeel, PiPants } from 'react-icons/pi';
import { TbShoe } from 'react-icons/tb';


export const fashionCategories = [
  {
    header: "My Everyfash Account",
    headerLink: "/everyfash-account",
    links: [
      {
        icon: <FaClipboard size={20} />,
        route: "/orders",
        label: "Orders",
      },
      {
        icon: <IoMailOutline size={20} />,
        route: "/inbox",
        label: "Inbox",
      },
      {
        icon: <FaRegCommentDots size={20} />,
        route: "/pending-reviews",
        label: "Pending Reviews",
      },
      {
        icon: <IoHeartOutline size={20} />,
        route: "/wishlist",
        label: "Wishlist",
      },
    ],
  },
  {
    header: "Men's Clothing",
    headerLink: "/categories/mens-clothing",
    links: [
      {
        icon: <FaTshirt size={20} />,
        image: "/images/men/Shirts_TN.png",
        route: "/categories/mens-clothing/mens-shirts",
        label: "Shirts",
      },
      {
        icon: <GiTrousers size={20} />,
        image: "/images/men/Trousers_TN.png",
        route: "/categories/mens-clothing/mens-trousers",
        label: "Trousers",
      },
      {
        icon: <PiPants size={20} />,
        image: "/images/men/Jeans_TN.png",
        route: "/categories/mens-clothing/mens-jeans",
        label: "Jeans",
      },
      {
        icon: <GiUnderwearShorts size={20} />,
        image: "/images/men/Shorts_TN.png",
        route: "/categories/mens-clothing/mens-shorts",
        label: "Shorts",
      },
      {
        icon: <FaSuitcase size={20} />,
        image: "/images/men/Suits_TN.png",
        route: "/categories/mens-clothing/mens-suits",
        label: "Suits & Blazers",
      },
      {
        image: "/images/men/Sweaters_TN.png",
        route: "/categories/mens-clothing/mens-sweaters",
        label: "Sweaters & Cardigans",
      },
      {
        image: "/images/men/Jackets_Coats_TN.png",
        route: "/categories/mens-clothing/mens-jackets",
        label: "Jackets & Coats",
      },
      {
        image: "/images/men/Underwear_TN.png",
        route: "/categories/mens-clothing/mens-underwear",
        label: "Underwear",
      },
      {
        image: "/images/men/Jackets_Coats_TN.png",
        route: "/categories/mens-clothing/mens-socks",
        label: "Socks",
      }
    ],
  },
  {
    header: "Women's Clothing",
    headerLink: "/categories/womens-clothing",
    links: [
      {
        icon: <FaTshirt size={20} />,
        image: "/images/womens-tops.jpg",
        route: "/categories/womens-clothing/womens-tops",
        label: "Tops",
      },
      {
        icon: <GiSkirt size={20} />,
        image: "/images/womens-skirts.jpg",
        route: "/categories/womens-clothing/womens-skirts",
        label: "Skirts",
      },
      {
        icon: <GiLargeDress size={20} />,
        image: "/images/womens-dresses.jpg",
        route: "/categories/womens-clothing/womens-dresses",
        label: "Dresses",
      },
      {
        icon: <GiMonclerJacket size={20} />,
        image: "/images/womens-jackets.jpg",
        route: "/categories/womens-clothing/womens-jackets",
        label: "Jackets & Coats",
      },
      {
        icon: <GiUnderwear size={20} />,
        image: "/images/womens-underwear.jpg",
        route: "/categories/womens-clothing/womens-undies",
        label: "Underwears",
      },
      {
        image: "/images/womens-bodycon.jpg",
        route: "/categories/womens-clothing/womens-bodycon",
        label: "Bodycon Dresses",
      },
      {
        image: "/images/womens-jumpsuits.jpg",
        route: "/categories/womens-clothing/womens-jumpsuits",
        label: "Jumpsuits & Rompers",
      },
      {
        image: "/images/womens-jeans.jpg",
        route: "/categories/womens-clothing/womens-jeans",
        label: "Jeans & Trousers",
      },
      {
        image: "/images/womens-lingerie.jpg",
        route: "/categories/womens-clothing/womens-lingerie",
        label: "Lingerie & Sleepwear",
      },
    ],
  },
  {
    header: "Men's Footwear",
    headerLink: "/categories/mens-footwear",
    links: [
      {
        icon: <FaRunning size={20} />,
        route: "/mens-sneakers",
        label: "Sneakers",
      },
      {
        icon: <GiChelseaBoot size={20} />,
        route: "/mens-boots",
        label: "Boots",
      },
      {
        icon: <GiFlipFlops size={20} />,
        route: "/mens-sandals",
        label: "Sandals",
      },
      {
        icon: <GiBootPrints size={20} />,
        route: "/mens-loafers",
        label: "Loafers",
      },
      {
        icon: <FaTableTennisPaddleBall size={20} />,
        route: "/mens-sports-shoes",
        label: "Sports Shoes",
      },
    ],
  },
  {
    header: "Women's Footwear",
    headerLink: "/categories/womens-footwear",
    links: [
      {
        icon: <PiHighHeel size={20} />,
        route: "/womens-heels",
        label: "Heels",
      },
      {
        icon: <GiBootPrints size={20} />,
        route: "/womens-flats",
        label: "Flats",
      },
      {
        icon: <GiRubberBoot size={20} />,
        route: "/womens-boots",
        label: "Boots",
      },
      {
        icon: <GiFlipFlops size={20} />,
        route: "/womens-sandals",
        label: "Sandals",
      },
      {
        icon: <FaRunning size={20} />,
        route: "/womens-sneakers",
        label: "Sneakers",
      },
    ],
  },
  {
    header: "Accessories",
    headerLink: "/categories/accessories",
    links: [
      {
        icon : <FaShoppingBag size={20} />,
        route: "/categories/accessories/bags",
        label: "Bags",
      },
      {
        icon: <GiPearlNecklace size={20} />,
        route: "/categories/accessories/jewelry",
        label: "Jewelry",
      },
      {
        icon: <FiWatch size={20} />,
        route: "/categories/accessories/watches",
        label: "Watches",
      },
      {
        icon: <GiSunglasses size={20} />,
        route: "/categories/accessories/sunglasses",
        label: "Sunglasses",
      },
      {
        icon: <FaRedhat size={20} />,
        route: "/categories/accessories/hats-scarves",
        label: "Hats & Scarves",
      },
      {
        icon: <GiBelt size={20} />,
        route: "/categories/accessories/belts",
        label: "Belts",
      },
      {
        icon : <FaShoppingBag size={20} />,
        route: "/bags-wallets",
        label: "Bags & Wallets",
      },
    ],
  },
  {
    header: "Kid's Fashion",
    headerLink: "/categories/kids-fashion",
    links: [
      {
        icon: <FaTshirt size={20} />,
        route: "/kids-clothing",
        label: "Kids' Clothing",
      },
      {
        icon: <TbShoe size={20} />,
        route: "/kids-footwear",
        label: "Kids' Footwear",
      },
    ],
  },
  {
    header: "Fashion Bales",
    headerLink: "/categories/fashion-bales",
    links: [
      {
        icon: <FaTshirt size={20} />,
        route: "/fashion-bales/men",
        label: "Men's Bales",
      },
      {
        icon: <GiLargeDress size={20} />,
        route: "/fashion-bales/women",
        label: "Women's Bales",
      },
      {
        icon: <FaChild size={20} />,
        route: "/fashion-bales/kids",
        label: "Kids' Bales",
      },
      {
        icon: <FaSocks size={20} />,
        route: "/fashion-bales/mixed",
        label: "Mixed Bales",
      },
    ],
  }
  
];



export const fashionCategoriesNavigation = [

  {
    header: "Men's Footwear",
    headerLink: "/mens-footwear",
    links: [
      {
        image: "/images/mens-sneakers.jpg",
        route: "/mens-sneakers",
        label: "Sneakers",
      },
      {
        image: "/images/mens-boots.jpg",
        route: "/mens-boots",
        label: "Boots",
      },
      {
        image: "/images/mens-loafers.jpg",
        route: "/mens-loafers",
        label: "Loafers",
      },
      {
        image: "/images/mens-sandals.jpg",
        route: "/mens-sandals",
        label: "Sandals & Flip-Flops",
      },
      {
        image: "/images/mens-sports-shoes.jpg",
        route: "/mens-sports-shoes",
        label: "Sports Shoes",
      },
      {
        image: "/images/mens-dress-shoes.jpg",
        route: "/mens-dress-shoes",
        label: "Formal Shoes",
      },
      {
        image: "/images/mens-slippers.jpg",
        route: "/mens-slippers",
        label: "Slippers",
      },
    ],
  },
  {
    header: "Women's Footwear",
    headerLink: "/womens-footwear",
    links: [
      {
        image: "/images/womens-heels.jpg",
        route: "/womens-heels",
        label: "Heels",
      },
      {
        image: "/images/womens-flats.jpg",
        route: "/womens-flats",
        label: "Flats",
      },
      {
        image: "/images/womens-boots.jpg",
        route: "/womens-boots",
        label: "Boots",
      },
      {
        image: "/images/womens-sandals.jpg",
        route: "/womens-sandals",
        label: "Sandals",
      },
      {
        image: "/images/womens-sneakers.jpg",
        route: "/womens-sneakers",
        label: "Sneakers",
      },
      {
        image: "/images/womens-slippers.jpg",
        route: "/womens-slippers",
        label: "Slippers",
      },
    ],
  },
  {
    header: "Accessories",
    headerLink: "/accessories",
    links: [
      {
        image: "/images/bags-wallets.jpg",
        route: "/bags-wallets",
        label: "Bags & Wallets",
      },
      {
        image: "/images/jewelry.jpg",
        route: "/jewelry",
        label: "Jewelry",
      },
      {
        image: "/images/watches.jpg",
        route: "/watches",
        label: "Watches",
      },
      {
        image: "/images/sunglasses.jpg",
        route: "/sunglasses",
        label: "Sunglasses",
      },
      {
        image: "/images/hats-scarves.jpg",
        route: "/hats-scarves",
        label: "Hats & Scarves",
      },
      {
        image: "/images/belts.jpg",
        route: "/belts",
        label: "Belts",
      },
      {
        image: "/images/gloves.jpg",
        route: "/gloves",
        label: "Gloves",
      },
    ],
  },
  {
    header: "Kid's Fashion",
    headerLink: "/kids-fashion",
    links: [
      {
        image: "/images/kids-clothing.jpg",
        route: "/kids-clothing",
        label: "Kids' Clothing",
      },
      {
        image: "/images/kids-footwear.jpg",
        route: "/kids-footwear",
        label: "Kids' Footwear",
      },
      {
        image: "/images/kids-accessories.jpg",
        route: "/kids-accessories",
        label: "Accessories",
      },
      {
        image: "/images/kids-toys.jpg",
        route: "/kids-toys",
        label: "Toys & Games",
      },
    ],
  },
];
