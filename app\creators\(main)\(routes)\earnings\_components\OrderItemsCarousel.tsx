"use client";
import React from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

const responsive = {
  desktop: { breakpoint: { max: 3000, min: 1024 }, items: 4 },
  tablet: { breakpoint: { max: 1024, min: 464 }, items: 2 },
  mobile: { breakpoint: { max: 464, min: 0 }, items: 2 },
};

interface Product {
  id: string;
  productName: string;
  size: string;
  price: string;
  quantity: number;
  image: string;
}

interface OrderItemsCarouselProps {
  items: Product[];
}

const OrderItemsCarousel: React.FC<OrderItemsCarouselProps> = ({ items }) => {
  return (
    <>
      {items.length > 2 ? (
        <Carousel responsive={responsive} infinite autoPlay={false} itemClass="px-2">
          {items.map((item) => (
            <ProductCard key={item.id} product={item} />
          ))}
        </Carousel>
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {items.map((item) => (
            <ProductCard key={item.id} product={item} />
          ))}
        </div>
      )}
    </>
  );
};

const ProductCard = ({ product }: { product: Product }) => (
  <div className="bg-white border rounded-lg p-4 shadow-sm">
    <div className="w-full h-32 bg-gray-200 rounded-lg overflow-hidden">
      <img src={product.image} alt={product.productName} className="w-full h-full object-cover" />
    </div>
    <div className="mt-2 space-y-1">
      <h3 className="text-md font-medium text-gray-800 line-clamp-1">{product.productName}</h3>
      <p className="text-sm font-medium text-gray-800">{product.price}</p>
      <p className="text-sm text-gray-600">Size: {product.size}</p>
      <p className="text-sm text-gray-600">Quantity: {product.quantity}</p>

    </div>
  </div>
);

export default OrderItemsCarousel;
