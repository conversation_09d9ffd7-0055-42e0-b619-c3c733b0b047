'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';
import { BsShop } from 'react-icons/bs';
import { products } from '@/lib/data';
import ProductCard from '@/components/ui/custom/ProductCard';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

interface Shop {
  id: string;
  name: string;
  sellerScore: number;
  numFollowers: number;
  products: any;
}



export default function FollowedShopsPage() {
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="account" title="Follow Your Favorite Sellers" description="Build your network by following amazing sellers. Get notified about new arrivals, exclusive deals, and never miss out on your favorite brands!" />;
  }

  const followedShops: Shop[] = [
    // {
    //   id: '1',
    //   name: 'Trendy Outfitters',
    //   sellerScore: 92,
    //   numFollowers: 318,
    //   products: products.slice(0,2)
    // },
    // {
    //   id: '2',
    //   name: 'Jads closet',
    //   sellerScore: 92,
    //   numFollowers: 318,
    //   products: products.slice(0,2)
    // },
  ];
  const suggestedShops: Shop[] = [
    {
      id: '1',
      name: 'Trendy Outfitters',
      sellerScore: 92,
      numFollowers: 318,
      products: products.slice(0,2)
    },
    {
      id: '2',
      name: 'Jads closet',
      sellerScore: 92,
      numFollowers: 318,
      products: products.slice(0,2)
    },
    {
      id: '3',
      name: 'Thrifts Nyame',
      sellerScore: 92,
      numFollowers: 318,
      products: products.slice(0,2)
    },
    {
      id: '4',
      name: 'Shoes Nyame',
      sellerScore: 92,
      numFollowers: 318,
      products: products.slice(0,2)
    },
  ];

  return (
    <section className="w-full mt-16 py-4">
      {/* Empty State */}
      {followedShops.length === 0 ? (
        <div className="flex flex-col items-center text-center px-6 py-8 bg-white rounded shadow-sm">
          <BsShop size={50} className="text-gray-400 mb-3" />
          <h2 className=" font-medium text-gray-700">You don't have any sellers</h2>
          <p className="text-sm text-gray-500 mb-4">All your followed sellers will be displayed here.</p>
          <Link href="/">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 transition-colors duration-200">Start Shopping</Button>
          </Link>
        </div>
      ) : (
        <div className="space-y-2">
          <h2 className="text-sm mt-2 uppercase font-bold text-gray-600 mb-3">Followed Sellers</h2>
          {followedShops.map((shop) => (
            <div key={shop.id} className="bg-white p-4 rounded shadow-sm">
              {/* Shop Header */}
              <div className="flex justify-between items-center border-b pb-2">
                <h1 className="text-sm font-semibold text-gray-700">{shop.name}</h1>
                <Link href={`/shops/${shop.id}`}>
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                </Link>
              </div>

              {/* Seller Info */}
              <div className="mt-3 flex justify-between items-center text-sm">
                <div className="text-gray-600">
                  <p>
                    <span className="font-medium">{shop.sellerScore}</span> Seller Score
                  </p>
                  <p>
                    <span className="font-medium">{shop.numFollowers}</span> Followers
                  </p>
                </div>
                <Button className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1 rounded transition-colors duration-200">
                  Followed
                </Button>
              </div>

              {/* Product Previews */}
              <div className="mt-3 flex gap-3 overflow-x-auto">
                {shop.products.map((product:any) => (
                  <div key={product.id} className="p-1">
                    <ProductCard product={product} cardType="flashy" />
                  </div>
                ))}

              </div>
            </div>
          ))}
        </div>
      )}

      {/* Suggested Sellers */}
      <h2 className="mt-8 mb-3 text-sm font-semibold text-gray-700">Suggested Sellers For You</h2>
      <div className="space-y-2">
        {suggestedShops.map((shop) => (
          <div key={shop.id} className="bg-white p-4 rounded shadow-sm">
            <div className="flex justify-between items-center border-b pb-2">
              <h1 className="text-sm font-semibold text-gray-700">{shop.name}</h1>
              <Link href={`/shops/${shop.id}`}>
                <ChevronRight className="h-4 w-4 text-gray-500" />
              </Link>
            </div>

            <div className="mt-3 flex justify-between items-center text-sm">
              <div className="text-gray-600">
                <p>
                  <span className="font-medium">{shop.sellerScore}</span> Seller Score
                </p>
                <p>
                  <span className="font-medium">{shop.numFollowers}</span> Followers
                </p>
              </div>
              <Button className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1 rounded transition-colors duration-200">
                Follow
              </Button>
            </div>

            {/* Suggested Shop Products */}
            <div className="mt-3 flex gap-3 overflow-x-auto">
              {shop.products.map((product:any) => (
                <div key={product.id} className="p-1">
                  <ProductCard product={product} cardType="flashy" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
