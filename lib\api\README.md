# EveryFash API Integration

This directory contains the complete API integration layer for the EveryFash platform. The architecture is designed to be scalable, maintainable, and type-safe.

## Architecture Overview

### Core Components

1. **BaseApiClient** - Shared HTTP client with common functionality
2. **AuthManager** - Centralized authentication and session management
3. **InterceptorManager** - Request/response interceptors for cross-cutting concerns
4. **ApiErrorHandler** - Standardized error handling and transformation
5. **Configuration** - Centralized API configuration and endpoints

### Directory Structure

```
lib/api/
├── index.ts              # Main export file
├── README.md            # This documentation
├── base-client.ts       # Base HTTP client
├── auth-manager.ts      # Authentication management
├── interceptors.ts      # Request/response interceptors
├── errors.ts           # Error handling utilities
├── config.ts           # API configuration
├── types.ts            # API-specific types
├── auth.ts             # Authentication API
├── onboarding.ts       # Creator onboarding API
└── categories.ts       # Categories API
```

## Key Features

### 🔐 Authentication Management
- Automatic token handling and refresh
- Role-based access control
- Session validation and expiration handling
- Secure token storage and retrieval

### 🔄 Request/Response Interceptors
- Automatic error logging and monitoring
- Performance tracking and metrics
- Response transformation and standardization
- Rate limiting detection and handling

### 🛡️ Error Handling
- Standardized error types and messages
- Network error detection and retry logic
- User-friendly error message transformation
- Comprehensive error categorization

### ⚡ Performance Features
- Request timeout and retry mechanisms
- Exponential backoff for failed requests
- Performance metrics collection
- Request deduplication (future enhancement)

## Usage Examples

### Basic API Usage

```typescript
import { authApi, onboardingApi, categoriesApi } from '@/lib/api';

// Authentication
const loginResponse = await authApi.login({
  email: '<EMAIL>',
  password: 'password',
  role: 'buyer'
});

// Creator onboarding
const onboardingStatus = await onboardingApi.getOnboardingStatus();

// Categories
const categories = await categoriesApi.getHierarchy();
```

### Advanced Usage with Error Handling

```typescript
import { 
  authApi, 
  handleApiError, 
  isNetworkError, 
  isAuthError,
  getUserErrorMessage 
} from '@/lib/api';

try {
  const user = await authApi.getProfile();
  console.log('User profile:', user);
} catch (error) {
  const apiError = handleApiError(error);
  
  if (isNetworkError(apiError)) {
    console.log('Network issue detected');
  } else if (isAuthError(apiError)) {
    console.log('Authentication required');
  }
  
  // Show user-friendly message
  const userMessage = getUserErrorMessage(apiError);
  showToast(userMessage);
}
```

### Session Management

```typescript
import { 
  AuthManager, 
  getSessionInfo, 
  hasRole, 
  clearSession 
} from '@/lib/api';

// Check session status
const session = getSessionInfo();
if (session.isValid) {
  console.log(`Session expires in ${session.expiresIn} seconds`);
}

// Role-based access
if (hasRole('creator')) {
  // Show creator-specific content
}

// Manual logout
clearSession();
```

### Custom API Client

```typescript
import { BaseApiClient, API_CONFIG } from '@/lib/api';

// Create custom client for external API
const externalClient = new BaseApiClient('https://external-api.com/v1');

// Use with custom configuration
const data = await externalClient.get('/endpoint', {
  timeout: 10000,
  retries: 2,
  requiresAuth: false
});
```

### File Upload

```typescript
import { onboardingApi } from '@/lib/api';

const formData = new FormData();
formData.append('businessName', 'My Business');
formData.append('verificationDocuments', file);

const result = await onboardingApi.updateBusinessInfo({
  businessName: 'My Business',
  businessType: 'LLC',
  // ... other fields
  verificationDocuments: [file]
});
```

## Configuration

### Environment Variables

```env
NEXT_PUBLIC_API_URL=https://everyfash-api.onrender.com/api/v1
```

### API Configuration

The API configuration is centralized in `config.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL,
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};
```

## Error Handling

### Error Types

1. **NetworkError** - Connection issues, timeouts
2. **AuthenticationError** - Invalid or expired tokens
3. **ValidationError** - Invalid input data
4. **ApiError** - General API errors

### Error Handling Best Practices

```typescript
import { handleApiError, ApiErrorHandler } from '@/lib/api';

try {
  await apiCall();
} catch (error) {
  const apiError = handleApiError(error);
  
  // Check error type
  if (ApiErrorHandler.isNetworkError(apiError)) {
    // Handle network issues
  } else if (ApiErrorHandler.isAuthenticationError(apiError)) {
    // Redirect to login
  } else if (ApiErrorHandler.isValidationError(apiError)) {
    // Show validation errors
  }
  
  // Get user-friendly message
  const message = ApiErrorHandler.getUserMessage(apiError);
}
```

## Performance Monitoring

### Metrics Collection

```typescript
import { getApiMetrics, clearApiMetrics } from '@/lib/api';

// Get performance metrics
const metrics = getApiMetrics();
console.log('API Performance:', metrics);

// Clear metrics
clearApiMetrics();
```

### Logging Control

```typescript
import { enableApiLogging } from '@/lib/api';

// Enable detailed logging in development
enableApiLogging(process.env.NODE_ENV === 'development');
```

## Migration Guide

### From Old API Structure

The new API structure maintains backward compatibility. Existing code should continue to work without changes:

```typescript
// Old way (still works)
import { authApi } from '@/lib/api/auth';

// New way (recommended)
import { authApi } from '@/lib/api';
```

### Breaking Changes

None. The refactoring maintains full backward compatibility.

## Best Practices

1. **Always handle errors** - Use the provided error handling utilities
2. **Use TypeScript types** - Leverage the comprehensive type definitions
3. **Monitor performance** - Use the built-in metrics collection
4. **Handle authentication** - Use the centralized auth management
5. **Follow patterns** - Use the established patterns for consistency

## Future Enhancements

- Request deduplication
- Offline support and request queuing
- GraphQL integration
- Real-time subscriptions
- Advanced caching strategies
- Request/response transformation pipelines
