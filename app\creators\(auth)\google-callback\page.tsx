'use client';

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

export default function CreatorGoogleCallbackPage() {
  useEffect(() => {
    // Redirect to the unified callback handler
    const currentUrl = window.location.href;
    const newUrl = currentUrl.replace('/creators/google-callback', '/auth/callback');
    window.location.href = newUrl;
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <h2 className="text-lg font-semibold mb-2">Completing sign-in...</h2>
        <p className="text-gray-600">Please wait while we complete your Google sign-in.</p>
      </div>
    </div>
  );
}
