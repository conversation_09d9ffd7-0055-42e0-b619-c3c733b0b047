'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import <PERSON><PERSON><PERSON>oader from '@/components/ui/LottieLoader';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import ButtonLoader from '@/components/ui/ButtonLoader';
import { useManualLoading, usePageLoading } from '@/lib/hooks/use-page-loading';

/**
 * Example component demonstrating all Lottie loader usage patterns
 * This is for documentation and testing purposes
 */
const LoadingExamples: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showFullScreen, setShowFullScreen] = useState(false);
  const { showLoading, hideLoading } = useManualLoading();

  // Example of automatic loading based on state
  usePageLoading(isLoading, 'Processing your request...');

  const handleButtonLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  const handleGlobalLoading = () => {
    showLoading('Global loading example...');
    setTimeout(() => hideLoading(), 3000);
  };

  const handleFullScreenDemo = () => {
    setShowFullScreen(true);
    setTimeout(() => setShowFullScreen(false), 3000);
  };

  if (showFullScreen) {
    return (
      <FullScreenLoader 
        title="Demo Loading"
        text="This is a full-screen loader example"
        size="2xl"
      />
    );
  }

  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-center mb-8">
        Lottie Loader Examples
      </h1>

      {/* Size Examples */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Size Examples</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <LottieLoader size="sm" text="Small" />
          </div>
          <div className="text-center">
            <LottieLoader size="md" text="Medium" />
          </div>
          <div className="text-center">
            <LottieLoader size="lg" text="Large" />
          </div>
          <div className="text-center">
            <LottieLoader size="xl" text="Extra Large" />
          </div>
          <div className="text-center">
            <LottieLoader size="2xl" text="2X Large" />
          </div>
        </div>
      </section>

      {/* Button Loading Examples */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Button Loading Examples</h2>
        <div className="flex flex-wrap gap-4">
          <Button onClick={handleButtonLoading} disabled={isLoading}>
            {isLoading ? (
              <ButtonLoader text="Processing..." />
            ) : (
              'Start Loading'
            )}
          </Button>
          
          <Button onClick={handleGlobalLoading}>
            Show Global Loading
          </Button>
          
          <Button onClick={handleFullScreenDemo}>
            Show Full Screen Demo
          </Button>
        </div>
      </section>

      {/* Card Loading Example */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Card Loading Example</h2>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <LottieLoader 
            size="md" 
            text="Loading card content..." 
            centered={true}
          />
        </div>
      </section>

      {/* Custom Examples */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Custom Examples</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Centered Animation</h3>
            <LottieLoader size="lg" text="Centered" centered={true} />
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">No Loop Animation</h3>
            <LottieLoader size="lg" text="No Loop" loop={false} />
          </div>
        </div>
      </section>

      {/* Usage Notes */}
      <section className="bg-blue-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Usage Notes</h2>
        <ul className="space-y-2 text-sm">
          <li>• Use <code>size="2xl"</code> for critical loading states</li>
          <li>• Use <code>FullScreenLoader</code> for page-level loading</li>
          <li>• Use <code>ButtonLoader</code> for form submissions</li>
          <li>• Use <code>loading.tsx</code> files for automatic route loading</li>
          <li>• Use global loading provider for programmatic control</li>
          <li>• Set <code>loop={`{false}`}</code> to play animation once</li>
          <li>• Use <code>centered={`{true}`}</code> for centered layouts</li>
        </ul>
      </section>
    </div>
  );
};

export default LoadingExamples;
