import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { CategoriesState, Category, FlattenedCategory, NavigationCategory } from '@/lib/types/categories';

interface CategoriesActions {
  setCategories: (categories: Category[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  updateLastFetched: () => void;
}

type CategoriesStore = CategoriesState & CategoriesActions;

const initialState: CategoriesState = {
  categories: [],
  isLoading: false,
  error: null,
  lastFetched: null,
};

export const useCategoriesStore = create<CategoriesStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setCategories: (categories: Category[]) => {
        set({
          categories,
          error: null,
          lastFetched: Date.now(),
        });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      clearError: () => {
        set({ error: null });
      },

      updateLastFetched: () => {
        set({ lastFetched: Date.now() });
      },
    }),
    {
      name: 'categories-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        categories: state.categories,
        lastFetched: state.lastFetched,
      }),
    }
  )
);

// Utility functions for category processing
export const flattenCategories = (categories: Category[]): FlattenedCategory[] => {
  const flattened: FlattenedCategory[] = [];

  const processCategory = (category: Category, path: string[] = [], level: number = 0, parentId?: string) => {
    const currentPath = [...path, category.name];
    
    flattened.push({
      id: category.id,
      name: category.name,
      description: category.description,
      slug: category.slug,
      path: currentPath,
      level,
      parentId,
    });

    if (category.immediateChildren && category.immediateChildren.length > 0) {
      category.immediateChildren.forEach(child => {
        processCategory(child, currentPath, level + 1, category.id);
      });
    }
  };

  categories.forEach(category => processCategory(category));
  return flattened;
};

export const convertToNavigationFormat = (categories: Category[]): NavigationCategory[] => {
  return categories.map(category => ({
    header: category.name,
    headerLink: `/categories/${category.slug}`,
    links: category.immediateChildren?.map(child => ({
      route: `/categories/${category.slug}/${child.slug}`,
      label: child.description || child.name,
      id: child.id,
    })) || [],
  }));
};

// Account section for authenticated users
export const getAccountSection = () => ({
  header: "My Everyfash Account",
  headerLink: "/everyfash-account",
  links: [
    {
      route: "/orders",
      label: "Orders",
      id: "orders",
      iconName: "FaClipboard",
    },
    {
      route: "/inbox",
      label: "Inbox",
      id: "inbox",
      iconName: "IoMailOutline",
    },
    {
      route: "/pending-reviews",
      label: "Pending Reviews",
      id: "pending-reviews",
      iconName: "FaRegCommentDots",
    },
    {
      route: "/wishlist",
      label: "Wishlist",
      id: "wishlist",
      iconName: "IoHeartOutline",
    },
  ],
});

// Combine account section with dynamic categories
export const getCombinedNavigationCategories = (categories: Category[], isAuthenticated: boolean): NavigationCategory[] => {
  const navigationCategories = convertToNavigationFormat(categories);

  if (isAuthenticated) {
    return [getAccountSection(), ...navigationCategories];
  }

  return navigationCategories;
};

// Selectors for easier access to specific parts of the store
export const useCategories = () => {
  const store = useCategoriesStore();
  return {
    categories: store.categories,
    isLoading: store.isLoading,
    error: store.error,
    lastFetched: store.lastFetched,
    flattenedCategories: flattenCategories(store.categories),
    navigationCategories: convertToNavigationFormat(store.categories),
    getCombinedNavigation: (isAuthenticated: boolean) =>
      getCombinedNavigationCategories(store.categories, isAuthenticated),
  };
};

export const useCategoriesActions = () => {
  const store = useCategoriesStore();
  return {
    setCategories: store.setCategories,
    setLoading: store.setLoading,
    setError: store.setError,
    clearError: store.clearError,
    updateLastFetched: store.updateLastFetched,
  };
};
