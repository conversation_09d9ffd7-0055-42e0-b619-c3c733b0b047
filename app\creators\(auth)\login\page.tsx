import React from 'react'
import Link from 'next/link'
import LoginForm from '@/app/(buyers)/(auth)/_components/LoginForm'
import { SocialAuthButtons } from '@/app/(buyers)/(auth)/_components/SocialAuthButtons'
import { GuestGuard } from '@/lib/components/AuthGuard'


const LoginPage = () => {
  return (
    <GuestGuard>
      <section className='flex flex-col items-center w-full max-w-md my-20'>
          {/* Header */}
          <h1 className="text-xl font-bold text-gray-900">Back to Everyfash, Back to Business!</h1>

          {/* About */}
          <p className="text-sm pt-2 px-2 mb-3 text-center font-medium text-gray-800">
          Manage your store and connect with fashion lovers across Africa.
          </p>

           {/* Social Auth Buttons */}
           <SocialAuthButtons role="creator" />

          {/* Or */}
          <div className="my- flex items-center">
              <p className="text-gray-700">Or</p>
          </div>


          {/* Form */}
          <LoginForm role="creator" />

          {/* Link to Login */}
          <div className="">
              <p className="text-left text-gray-600">Don't have an account? <Link href="/creators/register" className="text-primary hover:text-primary/80 transition-colors duration-200">Create one</Link></p>
          </div>



      </section>
    </GuestGuard>
  )
}

export default LoginPage