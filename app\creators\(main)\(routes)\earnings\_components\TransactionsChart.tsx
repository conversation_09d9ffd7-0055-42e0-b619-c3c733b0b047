"use client";

import { useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

// Monthly Data
const monthlyData = [
  { date: "Jan", earnings: 1200 },
  { date: "Feb", earnings: 2100 },
  { date: "Mar", earnings: 800 },
  { date: "Apr", earnings: 1600 },
  { date: "May", earnings: 2400 },
  { date: "Jun", earnings: 2800 },
  { date: "Jul", earnings: 1600 },
  { date: "Aug", earnings: 2400 },
  { date: "Sep", earnings: 2800 },
  { date: "Oct", earnings: 1600 },
  { date: "Nov", earnings: 2400 },
  { date: "Dec", earnings: 2800 },
];

// Yearly Data
const yearlyData = [
  { date: "2019", earnings: 15000 },
  { date: "2020", earnings: 22000 },
  { date: "2021", earnings: 18000 },
  { date: "2022", earnings: 25000 },
  { date: "2023", earnings: 29000 },
  { date: "2024", earnings: 32000 },
];

const TransactionsChart = () => {
  const [view, setView] = useState<"monthly" | "yearly">("monthly");
  const [selectedEarnings, setSelectedEarnings] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  const handlePointClick = (data: any) => {
    setSelectedEarnings(data.earnings);
    setSelectedDate(data.date);
  };

  const chartData = view === "monthly" ? monthlyData : yearlyData;

  const totalEarnings = chartData.reduce((acc, curr) => acc + curr.earnings, 0);

  return (
    <section className="w-full my-1">

        {selectedEarnings ? (
            <>
            <section className="w-full my-2">
                <div className="space-y-1">
                    <h1 className=" text-gray-600 ">You've made <span className="font-bold text-primary"> ₵{selectedEarnings.toLocaleString()}</span> {view === "monthly" ? `in ${selectedDate}` : `in ${selectedDate}`}</h1>
                </div>
            </section>
            </>
        ) : (
            <>
            <section className="w-full my-2">
                <div className="space-y-1">
                    <h1 className=" text-gray-600 ">You've made <span className="font-bold text-primary"> ₵{totalEarnings.toLocaleString()}</span> {view === "monthly" ? "this year" : "over the years"}</h1>
                </div>
            </section>
            </>
        )}

        <div className="w-full overflow-x-auto rounded-xl ">
            {/* Toggle View */}
            <div className="flex justify-end items-center mb-2">
                <div className="flex space-x-2">
                <button
                    className={`px-2 py-1 rounded-lg text-sm font-poppins ${
                    view === "monthly"
                        ? "bg-black text-white"
                        : "bg-gray-100 text-gray-500"
                    }`}
                    onClick={() => {
                    setView("monthly");
                    setSelectedEarnings(null);
                    }}
                >
                    Monthly
                </button>
                <button
                    className={`px-2 py-1 rounded-lg text-sm font-poppins ${
                    view === "yearly"
                        ? "bg-black text-white"
                        : "bg-gray-100 text-gray-500"
                    }`}
                    onClick={() => {
                    setView("yearly");
                    setSelectedEarnings(null);
                    }}
                >
                    Yearly
                </button>
                </div>
            </div>

            {/* Chart */}
            <ResponsiveContainer width="150%" height={300}>
                <LineChart data={chartData} onClick={(e) => handlePointClick(e.activePayload?.[0]?.payload)}>
                <CartesianGrid strokeDasharray="3 3" stroke="#EAEAEA" />
                <XAxis dataKey="date" tick={{ fill: "#888" }} />
                <YAxis tick={{ fill: "#888" }} />
                <Tooltip />
                <Line
                    type="monotone"
                    dataKey="earnings"
                    stroke="#51a2ff"
                    strokeWidth={3}
                    dot={{ r: 5 }}
                    activeDot={{ r: 8 }}
                />
                </LineChart>
            </ResponsiveContainer>

        </div>
    </section>

  );
};

export default TransactionsChart;
