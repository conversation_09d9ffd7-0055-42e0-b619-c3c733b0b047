'use client';

import { useRouter } from 'next/navigation';
import { ChevronRight } from 'lucide-react';

const ProductDescriptionSection = ({ productId }: { productId: string }) => {
  const router = useRouter();

  return (
    <section className="bg-white my-2 py-2 px-2 rounded shadow-sm">
      <div
        className="flex justify-between items-center border-b pb-2 cursor-pointer"
        onClick={() => router.push(`/items/${productId}/description`)}
      >
        <h1 className="text-sm font-semibold text-gray-600">Description</h1>
        <ChevronRight className="text-gray-500" />
      </div>

      <div className="text-sm text-gray-700 mt-2 space-y-1">
        <p>• Stylish and comfortable fabric suitable for all-day wear.</p>
        <p>• Perfect for both casual outings and formal occasions.</p>
        <p>• Breathable and lightweight material for maximum comfort.</p>
        <p>• Available in multiple colors and sizes to suit your style.</p>
        <p>• Easy to wash and maintain with long-lasting durability.</p>
      </div>
    </section>
  );
};

export default ProductDescriptionSection;
