'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Upload, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useBusinessInfo, useUpdateBusinessInfo } from '@/lib/hooks/use-onboarding';
import { useBusinessInfoProfile, useUpdateBusinessInfoProfile } from '@/lib/hooks/useProfile';
import { BusinessInfoUpdateData } from '@/lib/types/auth';
import { toast } from '@/hooks/use-toast';
import { getApiErrorMessage, getApiErrorTitle } from '@/lib/utils/error-utils';
import {
  FormLayout,
  FormSection,
  FormInput,
  FormSelect,
  FormActions
} from './FormComponents';

// Validation schema
const businessInfoSchema = z.object({
  businessName: z.string().min(2, "Business name must be at least 2 characters").nonempty("Business name is required"),
  businessType: z.string().nonempty("Business type is required"),
  ownerName: z.string().min(2, "Owner name must be at least 2 characters").nonempty("Owner name is required"),
  ownerID: z.string().min(5, "Owner ID must be at least 5 characters").nonempty("Owner ID is required"),
  phoneNumber: z.string().min(10, "Phone number must be at least 10 characters").nonempty("Phone number is required"),
  taxId: z.string().min(5, "Tax ID must be at least 5 characters").nonempty("Tax ID is required"),
  addressLine1: z.string().min(5, "Address line 1 must be at least 5 characters").nonempty("Address line 1 is required"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "City must be at least 2 characters").nonempty("City is required"),
  state: z.string().min(2, "State must be at least 2 characters").nonempty("State is required"),
  country: z.string().min(2, "Country must be at least 2 characters").nonempty("Country is required"),
  digitalGps: z.string().optional(),
});

type BusinessInfoFormData = z.infer<typeof businessInfoSchema>;

interface BusinessInfoFormProps {
  onNext?: () => void;
  isProfileMode?: boolean;
}

const BusinessInfoForm = ({ onNext, isProfileMode = false }: BusinessInfoFormProps) => {
  const router = useRouter();
  const [verificationFiles, setVerificationFiles] = useState<File[]>([]);
  const [existingDocuments, setExistingDocuments] = useState<string[]>([]);

  // Use different hooks based on mode
  const { data: businessInfoResponse, isLoading: isLoadingInfo } = isProfileMode
    ? useBusinessInfoProfile()
    : useBusinessInfo();
  const updateBusinessInfo = isProfileMode
    ? useUpdateBusinessInfoProfile()
    : useUpdateBusinessInfo();

  const businessInfo = businessInfoResponse?.data?.businessInfo;

  const { control, handleSubmit, formState: { errors }, setValue, trigger } = useForm<BusinessInfoFormData>({
    resolver: zodResolver(businessInfoSchema),
    mode: 'onSubmit', // Validate on submit
    reValidateMode: 'onChange', // Re-validate on change after first submit
    defaultValues: {
      businessName: businessInfo?.businessName || "",
      businessType: businessInfo?.businessType || "",
      ownerName: businessInfo?.ownerName || "",
      ownerID: businessInfo?.ownerID || "",
      phoneNumber: businessInfo?.phoneNumber || "",
      taxId: businessInfo?.taxId || "",
      addressLine1: businessInfo?.businessAddress?.addressLine1 || "",
      addressLine2: businessInfo?.businessAddress?.addressLine2 || "",
      city: businessInfo?.businessAddress?.city || "",
      state: businessInfo?.businessAddress?.state || "",
      country: businessInfo?.businessAddress?.country || "",
      digitalGps: businessInfo?.businessAddress?.digitalGps || "",
    },
  });

  // Update form when data loads
  React.useEffect(() => {
    if (businessInfo) {
      setValue('businessName', businessInfo.businessName);
      setValue('businessType', businessInfo.businessType);
      setValue('ownerName', businessInfo.ownerName);
      setValue('ownerID', businessInfo.ownerID);
      setValue('phoneNumber', businessInfo.phoneNumber);
      setValue('taxId', businessInfo.taxId);
      setValue('addressLine1', businessInfo.businessAddress.addressLine1);
      setValue('addressLine2', businessInfo.businessAddress.addressLine2 || '');
      setValue('city', businessInfo.businessAddress.city);
      setValue('state', businessInfo.businessAddress.state);
      setValue('country', businessInfo.businessAddress.country);
      setValue('digitalGps', businessInfo.businessAddress.digitalGps || '');

      // Initialize existing documents list (only if not already set to avoid clearing user changes)
      const newExistingDocs = businessInfo.verificationDocuments || [];
      setExistingDocuments(newExistingDocs);
    }
  }, [businessInfo, setValue]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const totalAfterChanges = existingDocuments.length + verificationFiles.length + files.length;

    if (totalAfterChanges > 5) {
      toast({
        variant: 'destructive',
        title: 'Too Many Files',
        description: `You can only upload up to 5 documents total. You currently have ${existingDocuments.length} existing documents and ${verificationFiles.length} new files selected.`,
      });
      return;
    }

    // Add new files to existing ones
    setVerificationFiles(prev => {
      const newArray = [...prev, ...files];
      return newArray;
    });

    // Reset the input value so the same file can be selected again
    event.target.value = '';
  };

  const removeFile = (index: number) => {
    setVerificationFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingDocument = (docUrl: string, _index: number) => {
    // Remove document from existing documents list
    setExistingDocuments(prev => prev.filter(url => url !== docUrl));

    toast({
      title: 'Document Removed',
      description: 'This document will not be included when you save the form.',
      className: 'bg-orange-100 text-orange-800',
    });
  };

  const restoreExistingDocument = (docUrl: string) => {
    // Add document back to existing documents list
    setExistingDocuments(prev => [...prev, docUrl]);

    toast({
      title: 'Document Restored',
      description: 'This document will be kept.',
      className: 'bg-green-100 text-green-800',
    });
  };

  const onSubmit = async (data: BusinessInfoFormData) => {
    // Manually trigger validation to ensure all errors are caught
    const isValid = await trigger();

    if (!isValid) {
      // Get current errors after validation
      const currentErrors = Object.keys(errors);
      const errorMessages = currentErrors.map(key => {
        const error = errors[key as keyof BusinessInfoFormData];
        return error?.message;
      }).filter(Boolean);

      toast({
        variant: 'destructive',
        title: 'Please Fix Form Errors',
        description: errorMessages.length > 1
          ? `${errorMessages.length} fields need attention: ${errorMessages[0]} and ${errorMessages.length - 1} more.`
          : errorMessages[0] || 'Please fill in all required fields correctly.',
      });
      return;
    }

    // Combine existing documents (URLs) and new files
    const allDocuments: (File | string)[] = [...existingDocuments, ...verificationFiles];

    const updateData: BusinessInfoUpdateData = {
      businessName: data.businessName,
      businessType: data.businessType,
      ownerName: data.ownerName,
      ownerID: data.ownerID,
      phoneNumber: data.phoneNumber,
      taxId: data.taxId,
      businessAddress: {
        addressLine1: data.addressLine1,
        addressLine2: data.addressLine2,
        city: data.city,
        state: data.state,
        country: data.country,
        digitalGps: data.digitalGps,
      },
      // Send complete list of documents (existing URLs + new Files)
      verificationDocuments: allDocuments.length > 0 ? allDocuments : undefined,
    };

    updateBusinessInfo.mutate(updateData, {
      onSuccess: (response) => {
        // Clear the new files since they've been uploaded successfully
        setVerificationFiles([]);

        // Update existing documents with the response data if available
        if (response?.data?.businessInfo?.verificationDocuments) {
          setExistingDocuments(response.data.businessInfo.verificationDocuments);
        }

        if (onNext) {
          onNext();
        } else {
          // Navigate to next step (payment info)
          router.push('/creators/onboarding?step=payment-info');
        }
      },
      onError: (error) => {
        toast({
          variant: 'destructive',
          title: getApiErrorTitle(error),
          description: getApiErrorMessage(error),
        });
      },
    });
  };

  if (isLoadingInfo) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <FormLayout>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-4">
        {/* Business Information Section */}
        <FormSection
          title="Business Details"
          description="Basic information about your business."
        >
          <div className="grid gap-4">
            <FormInput
              label="Business Name"
              name="businessName"
              control={control}
              errors={errors}
              placeholder="Enter your business name"
              required
              disabled={updateBusinessInfo.isPending}
            />

            <FormSelect
              label="Business Type"
              name="businessType"
              control={control}
              errors={errors}
              placeholder="Select business type"
              required
              disabled={updateBusinessInfo.isPending}
              options={[
                { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
                { value: 'partnership', label: 'Partnership' },
                { value: 'corporation', label: 'Corporation' },
                { value: 'llc', label: 'Limited Liability Company (LLC)' }
              ]}
            />
          </div>
        </FormSection>

        {/* Legal Representative's Details */}
        <FormSection
          title="Legal Representative's Details"
          description="Please provide the owner/legal representative's information for your business."
        >
          <div className="grid gap-4">
            <FormInput
              label="Full Name"
              name="ownerName"
              control={control}
              errors={errors}
              placeholder="Full name of business owner"
              required
              disabled={updateBusinessInfo.isPending}
            />

            <FormInput
              label="Owner's ID"
              name="ownerID"
              control={control}
              errors={errors}
              placeholder="National ID or passport number"
              required
              disabled={updateBusinessInfo.isPending}
            />
          </div>
        </FormSection>

        {/* Contact Information */}
        <FormSection
          title="Contact Information"
          description="Business contact details and tax information."
        >
          <div className="grid gap-4">
            <FormInput
              label="Phone Number"
              name="phoneNumber"
              control={control}
              errors={errors}
              placeholder="+233123456789"
              required
              disabled={updateBusinessInfo.isPending}
            />

            <FormInput
              label="Contact Number's ID"
              name="taxId"
              control={control}
              errors={errors}
              placeholder="Contact Number's ID"
              required
              disabled={updateBusinessInfo.isPending}
            />
          </div>
        </FormSection>

        {/* Business Address */}
        <FormSection
          title="Business Address"
          description="Physical location of your business operations."
        >
          <div className="grid gap-4">
            <FormInput
              label="Address Line 1"
              name="addressLine1"
              control={control}
              errors={errors}
              placeholder="Street address, building number"
              required
              disabled={updateBusinessInfo.isPending}
            />

            <FormInput
              label="Address Line 2"
              name="addressLine2"
              control={control}
              errors={errors}
              placeholder="Apartment, suite, unit, etc. (optional)"
              disabled={updateBusinessInfo.isPending}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="City"
                name="city"
                control={control}
                errors={errors}
                placeholder="City"
                required
                disabled={updateBusinessInfo.isPending}
              />

              <FormInput
                label="State/Region"
                name="state"
                control={control}
                errors={errors}
                placeholder="State or region"
                required
                disabled={updateBusinessInfo.isPending}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="Country"
                name="country"
                control={control}
                errors={errors}
                placeholder="Country"
                required
                disabled={updateBusinessInfo.isPending}
              />

              <FormInput
                label="Digital GPS Address"
                name="digitalGps"
                control={control}
                errors={errors}
                placeholder="GA-123-4567 (optional)"
                disabled={updateBusinessInfo.isPending}
              />
            </div>
          </div>
        </FormSection>

        {/* Verification Documents */}
        <FormSection
          title="Verification Documents"
          description="Upload up to 5 business registration documents, licenses, or other verification materials. New documents will be added to your existing ones. You can remove existing documents if needed."
        >
          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <input
              type="file"
              multiple
              accept="image/*,.pdf"
              onChange={handleFileChange}
              className="hidden"
              id="verification-files"
              disabled={updateBusinessInfo.isPending}
            />
            <label
              htmlFor="verification-files"
              className="cursor-pointer flex flex-col items-center"
            >
              <Upload className="h-8 w-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-600">
                Click to upload or drag and drop
              </span>
              <span className="text-xs text-gray-500">
                PNG, JPG, PDF up to 10MB each (Maximum 5 files)
              </span>
            </label>
          </div>

          {/* File Count Indicator */}
          {(verificationFiles.length > 0 || existingDocuments.length > 0) && (
            <div className="mt-2 text-sm text-gray-600">
              <div className="space-y-1">
                <div>
                  Total documents: {existingDocuments.length + verificationFiles.length} / 5
                </div>
                {existingDocuments.length > 0 && (
                  <div className="text-green-600 text-xs">
                    {existingDocuments.length} existing document(s) to keep
                  </div>
                )}
                {verificationFiles.length > 0 && (
                  <div className="text-blue-600 text-xs">
                    {verificationFiles.length} new document(s) to upload
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Display selected files */}
          {verificationFiles.length > 0 && (
            <div className="mt-3">
              <p className="text-sm font-medium text-gray-700 mb-2">New Documents to Upload:</p>
              <div className="space-y-2">
                {verificationFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-blue-50 p-3 rounded">
                    <div className="flex items-center">
                      <span className="text-sm text-blue-700 font-medium">{file.name}</span>
                      <span className="text-xs text-blue-600 ml-2">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700 p-1"
                      disabled={updateBusinessInfo.isPending}
                      title="Remove file"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Display existing documents */}
          {businessInfo?.verificationDocuments && businessInfo.verificationDocuments.length > 0 && (
            <div className="mt-3">
              <p className="text-sm font-medium text-gray-700 mb-2">Current Documents:</p>
              <div className="space-y-2">
                {businessInfo.verificationDocuments.map((doc, index) => {
                  const isKept = existingDocuments.includes(doc);
                  return (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 rounded ${
                        isKept ? 'bg-green-50' : 'bg-red-50'
                      }`}
                    >
                      <div className="flex items-center">
                        <span className={`text-sm font-medium ${
                          isKept ? 'text-green-700' : 'text-red-700'
                        }`}>
                          Document {index + 1}
                        </span>
                        <span className={`text-xs ml-2 ${
                          isKept ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {isKept ? '(Will be kept)' : '(Will be removed)'}
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => isKept ? removeExistingDocument(doc, index) : restoreExistingDocument(doc)}
                        className={`p-1 ${
                          isKept ? 'text-red-500 hover:text-red-700' : 'text-green-500 hover:text-green-700'
                        }`}
                        disabled={updateBusinessInfo.isPending}
                        title={isKept ? 'Remove document' : 'Restore document'}
                      >
                        {isKept ? <X className="h-4 w-4" /> : <span className="text-sm">Restore</span>}
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </FormSection>

        {/* Form Actions */}
        <FormActions>
          <Button
            type="submit"
            disabled={updateBusinessInfo.isPending}
            className="w-full sm:w-auto"
          >
            {updateBusinessInfo.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save & Continue'
            )}
          </Button>
        </FormActions>
      </form>
    </FormLayout>
  );
};

export default BusinessInfoForm;
