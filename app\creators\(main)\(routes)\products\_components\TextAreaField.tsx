import { Textarea } from "@/components/ui/textarea";

interface TextAreaFieldProps<T extends Record<string, any>> {
  label: string;
  name: keyof T;
  value: string;
  onChange: (field: keyof T, value: string) => void;
  placeholder?: string;
}

function TextAreaField<T extends Record<string, any>>({
  label,
  name,
  value,
  onChange,
  placeholder,
}: TextAreaFieldProps<T>) {
  return (
    <div>
      <label className="block text-sm font-medium mb-1">{label}</label>
      <Textarea
        value={value}
        onChange={(e) => onChange(name, e.target.value)}
        placeholder={placeholder}
        className="w-full"
      />
    </div>
  );
}

export default TextAreaField;
