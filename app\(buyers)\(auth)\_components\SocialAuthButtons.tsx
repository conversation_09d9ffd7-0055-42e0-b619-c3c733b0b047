'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useGoogleAuth } from '@/lib/hooks/use-auth';
import { useAuth } from '@/lib/stores/auth-store';
import { UserRole } from '@/lib/types/auth';
import { Loader2 } from 'lucide-react';
import { FcGoogle } from 'react-icons/fc';

interface SocialAuthButtonsProps {
  role: UserRole;
}

export const SocialAuthButtons = ({ role }: SocialAuthButtonsProps) => {
  const { isLoading } = useAuth();
  const { initiateAuth, isLoading: googleLoading } = useGoogleAuth();

  const handleGoogleAuth = () => {
    initiateAuth(role);
  };

  const isAnyLoading = isLoading || googleLoading;

  return (
    <div className="flex flex-col w-full p-4">
      <Button
        className="w-full py-2 rounded-md h-12 border-gray-300 hover:bg-gray-50"
        variant="outline"
        onClick={handleGoogleAuth}
        disabled={isAnyLoading}
      >
        {googleLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Connecting with Google...
          </>
        ) : (
          <>
            <FcGoogle className="mr-2 h-5 w-5" />
            Continue with Google
          </>
        )}
      </Button>
    </div>
  );
};
