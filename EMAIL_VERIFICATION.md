# Email Verification System

This document explains the email verification system implemented for user registration.

## Overview

The email verification system ensures that users have access to the email address they register with. This improves security and reduces spam accounts.

## Flow

### 1. User Registration
- User submits registration form (buyer or creator)
- Backend validates input and creates user with `emailVerified: false`
- Backend generates verification token and sends email
- Backend responds with success message (no JWT token yet)

### 2. Email Verification
- User clicks verification link in email
- Frontend extracts token from URL parameter
- Frontend calls verification endpoint
- Backend verifies token and marks email as verified
- Backend responds with JWT token and user data (same as login)

### 3. Login with Auto-Verification Email
- Users with unverified emails cannot login
- System automatically sends new verification email on login attempt
- User gets friendly message to check their email
- Social auth users are automatically verified

## API Endpoints

### Register Buyer
```
POST /api/v1/auth/register/buyer
```

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "passwordConfirm": "password123"
}
```

**Response (Success):**
```json
{
  "status": "success",
  "message": "Registration successful! Please check your email to verify your account.",
  "data": {
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "buyer",
      "emailVerified": false
    }
  }
}
```

### Register Creator
```
POST /api/v1/auth/register/creator
```

**Request Body:**
```json
{
  "name": "Jane Creator",
  "email": "<EMAIL>",
  "password": "password123",
  "passwordConfirm": "password123",
  "bio": "Fashion creator",
  "location": "Accra, Ghana"
}
```

**Response:** Same structure as buyer registration

### Verify Email
```
GET /api/v1/auth/verify-email/:token
```

**Response (Success):**
```json
{
  "status": "success",
  "token": "jwt_token_here",
  "data": {
    "user": {
      // Complete user object
    }
  }
}
```

### Login (Unverified Email)
```
POST /api/v1/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (Unverified Email):**
```json
{
  "status": "fail",
  "message": "Email verification required. We've sent a new verification link to your email address. Please check your inbox and verify your email before logging in.",
  "emailSent": true,
  "userEmail": "<EMAIL>"
}
```

### Resend Verification Email
```
POST /api/v1/auth/resend-verification
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Verification email sent! Please check your inbox."
}
```

## Frontend Implementation

### Registration Flow
```javascript
// 1. Register user
const response = await fetch('/api/v1/auth/register/buyer', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(formData)
});

const data = await response.json();

if (data.status === 'success') {
  // Show success message
  // Redirect to "check your email" page
  // Don't store token yet (user not verified)
}
```

### Email Verification
```javascript
// 2. Handle verification link click
// URL: /verify-email?token=abc123...

const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');

if (token) {
  const response = await fetch(`/api/v1/auth/verify-email/${token}`);
  const data = await response.json();
  
  if (data.status === 'success') {
    // Store JWT token
    localStorage.setItem('token', data.token);
    // Store user data
    localStorage.setItem('user', JSON.stringify(data.data.user));
    // Redirect to dashboard
    window.location.href = '/dashboard';
  }
}
```

### Login with Auto-Verification
```javascript
// 3. Handle login attempt
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const data = await response.json();

if (response.status === 401 && data.emailSent) {
  // User exists but email not verified
  // System automatically sent verification email
  showMessage(data.message); // Show friendly message
  // Optionally redirect to "check your email" page
} else if (data.status === 'success') {
  // Login successful
  localStorage.setItem('token', data.token);
  localStorage.setItem('user', JSON.stringify(data.data.user));
  window.location.href = '/dashboard';
} else {
  // Other login errors (wrong password, etc.)
  showError(data.message);
}
```

### Resend Verification
```javascript
// 4. Resend verification email (if needed)
const response = await fetch('/api/v1/auth/resend-verification', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: userEmail })
});
```

## Email Templates

### Buyer Verification Email
- Subject: "Verify Your Email Address - Everyfash"
- Link: `{FRONTEND_BUYER_URL}/verify-email?token={TOKEN}`

### Creator Verification Email
- Subject: "Verify Your Email Address - Everyfash Creator"
- Link: `{FRONTEND_CREATOR_URL}/verify-email?token={TOKEN}`

## Database Changes

### BaseUser Model
```javascript
{
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpires: Date
}
```

### Methods Added
- `createEmailVerificationToken()` - Generates verification token
- `verifyEmail()` - Marks email as verified and clears tokens

## Environment Variables

```env
# Frontend URLs for email verification links
FRONTEND_BUYER_URL=http://localhost:3000
FRONTEND_CREATOR_URL=http://localhost:3001
FRONTEND_ADMIN_URL=http://localhost:3002
```

## Security Features

1. **Token Expiration:** Verification tokens expire in 24 hours
2. **Hashed Tokens:** Tokens are hashed before storage in database
3. **Login Protection:** Unverified users cannot login
4. **Social Auth Bypass:** Social auth users are automatically verified
5. **One-time Use:** Tokens are cleared after successful verification

## Error Handling

- Invalid/expired token: "Token is invalid or has expired"
- Already verified: "Email is already verified"
- User not found: "There is no user with that email address"
- Unverified login attempt: "Please verify your email address before logging in"
