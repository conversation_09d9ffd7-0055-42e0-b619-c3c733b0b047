/**
 * Date utility functions for the application
 */

/**
 * Formats a date to YYYY-MM-DD format for HTML date inputs
 * @param date - Date object, ISO string, or null/undefined
 * @returns Formatted date string or empty string
 */
export function formatDateForInput(date: Date | string | null | undefined): string {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) return '';
    
    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Gets today's date in YYYY-MM-DD format
 * @returns Today's date string
 */
export function getTodayDate(): string {
  return new Date().toISOString().split('T')[0];
}

/**
 * Checks if a date is in the past (before today)
 * @param date - Date to check
 * @returns True if date is in the past
 */
export function isDateInPast(date: Date | string): boolean {
  if (!date) return false;
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return dateObj < today;
  } catch (error) {
    return false;
  }
}

/**
 * Checks if end date is after start date
 * @param startDate - Start date
 * @param endDate - End date
 * @returns True if end date is after start date
 */
export function isEndDateAfterStartDate(startDate: Date | string, endDate: Date | string): boolean {
  if (!startDate || !endDate) return false;
  
  try {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    return end > start;
  } catch (error) {
    return false;
  }
}

/**
 * Formats a date for display (e.g., "Jan 15, 2024")
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDateForDisplay(date: Date | string | null | undefined): string {
  if (!date) return 'Not set';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) return 'Invalid date';
    
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return 'Invalid date';
  }
}
