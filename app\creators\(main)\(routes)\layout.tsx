import type { Metadata } from "next";
import { Toaster } from "@/components/ui/toaster"
import CreatorsBottomNavigation from "@/components/ui/custom/CreatorsBottomBar";
import { CreatorGuardWithOnboarding } from "@/lib/components/AuthGuard";


export const metadata: Metadata = {
  title: "Everyfash",
  description: "Africa's largest fashion marketplace",
};

export default function CreatorsHomeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CreatorGuardWithOnboarding>
      <main className="w-full">
          <section className="flex min-h-screen w-full px-2 md:px-10 lg:px-20 pb-6 bg-gray-100 mb-24">
              {children}
          </section>
          <Toaster />
          <CreatorsBottomNavigation />
      </main>
    </CreatorGuardWithOnboarding>
  );
}
