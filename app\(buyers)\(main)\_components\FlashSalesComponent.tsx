import React from 'react'
import ProductCard from '@/components/ui/custom/ProductCard';
import ProductsCarousel from './ProductsCarousel';
import { products } from '@/lib/data';
import Link from 'next/link';



const FlashSalesComponent = () => {
  return (
    <section className="flex flex-col w-full mt-6">
     {/* Flash Sales Header */}
      <section className="w-full bg-primary py-4 px-3 mb-2 text-primary-foreground">
        <div className="flex justify-between items-center rounded-sm">
          <h1 className="text-base font-bold">
            Flash Sales{' '}
            <span className="hidden md:inline">| Time Left: 00h : 47m : 58s</span>
          </h1>
          <Link href="/flashSales" className="text-sm font-bold">View All</Link>
        </div>
        <h1 className="text-sm md:hidden">
          Time Left:
          <span className="font-bold ml-1">00h : 47m : 58s</span>
        </h1>
      </section>

       {/* Carousel with Product Cards */}

        <ProductsCarousel>
            {products.map((product) => (
            <div key={product.id} className="p-1">
                <ProductCard product={product} cardType='flashy' />
            </div>
            ))}
        </ProductsCarousel>

    </section>
  )
}

export default FlashSalesComponent