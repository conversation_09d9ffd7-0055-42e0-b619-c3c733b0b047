import React from 'react';
import { <PERSON>aFacebookF, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaInstagram, FaTiktok } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-8 px-6 mt-6">
      <div className="max-w-screen-lg mx-auto grid grid-cols-1 md:grid-cols-3 gap-6 text-center md:text-left">
        {/* Brand & Contact */}
        <div>
          <h1 className="text-lg font-extrabold tracking-wide">Everyfash</h1>
          <p className="text-sm text-gray-400 mt-2">Your go-to fashion marketplace.</p>
          <p className="text-sm text-gray-400 mt-2"><EMAIL></p>
        </div>

        {/* Quick Links */}
        <div>
          <h2 className=" font-semibold">Quick Links</h2>
          <ul className="text-sm text-gray-400 space-y-1 mt-2">
            <li><a href="#" className="hover:text-white">FAQs</a></li>
            <li><a href="#" className="hover:text-white">Returns & Refunds</a></li>
            <li><a href="#" className="hover:text-white">Shipping Info</a></li>
            <li><a href="#" className="hover:text-white">Terms & Conditions</a></li>
          </ul>
        </div>

        {/* Social Media */}
        <div>
          <h2 className=" font-semibold">Follow Us</h2>
          <div className="flex justify-center md:justify-start space-x-4 mt-2">
            <a href="#" className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition">
              <FaFacebookF className="w-5 h-5" />
            </a>
            <a href="#" className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition">
              <FaTwitter className="w-5 h-5" />
            </a>
            <a href="#" className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition">
              <FaInstagram className="w-5 h-5" />
            </a>
            <a href="#" className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition">
              <FaTiktok className="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="mt-6 text-center text-sm text-gray-400 border-t border-gray-700 pt-4">
        &copy; {new Date().getFullYear()} Everyfash. All rights reserved.
      </div>
    </footer>
  );
};

export default Footer;
