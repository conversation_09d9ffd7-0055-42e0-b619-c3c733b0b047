'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Loader2, Mail, RefreshCw, Store } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useResendVerification } from '@/lib/hooks/use-auth';
import { useAuth } from '@/lib/stores/auth-store';

const CreatorCheckEmailForm = () => {
  const [email, setEmail] = useState<string>('');
  const [canResend, setCanResend] = useState(true);
  const [countdown, setCountdown] = useState(0);

  const searchParams = useSearchParams();
  const resendMutation = useResendVerification();
  const { isLoading } = useAuth();

  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, canResend]);

  const handleResendEmail = () => {
    if (!email || !canResend) return;

    resendMutation.mutate(
      { email },
      {
        onSuccess: () => {
          setCanResend(false);
          setCountdown(60); // 60 second cooldown
        },
      }
    );
  };

  return (
    <section className='flex flex-col items-center w-full max-w-md my-20'>
      {/* Icon */}
      <div className="mb-6">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
          <Store className="w-8 h-8 text-primary" />
        </div>
      </div>

      {/* Header */}
      <h1 className="text-xl font-bold text-gray-900 mb-2 text-center">Verify Your Business Email</h1>

      {/* About */}
      <p className="text-sm text-center mb-6 text-gray-600 px-4">
        We've sent a verification link to{' '}
        <span className="font-medium text-gray-900">{email}</span>
        <br />
        Click the link to activate your creator account and start selling.
      </p>

      {/* Instructions */}
      <div className="w-full bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="font-medium text-gray-900 mb-2">Get Started with Your Store</h3>
        <ol className="text-sm text-gray-600 space-y-1">
          <li>1. Check your email inbox</li>
          <li>2. Look for an email from Everyfash Creator</li>
          <li>3. Click the verification link</li>
          <li>4. Access your creator dashboard</li>
          <li>5. Start uploading your products</li>
        </ol>
      </div>

      {/* Resend Email */}
      <div className="w-full mb-6">
        <p className="text-sm text-gray-600 text-center mb-3">
          Didn't receive the email?
        </p>
        <Button
          onClick={handleResendEmail}
          disabled={!canResend || isLoading || resendMutation.isPending}
          variant="outline"
          className="w-full"
        >
          {isLoading || resendMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : !canResend ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Resend in {countdown}s
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Resend Email
            </>
          )}
        </Button>
      </div>

      {/* Help */}
      <div className="text-center">
        <p className="text-sm text-gray-600 mb-2">
          Need help getting started?
        </p>
        <div className="space-y-2">
          <p className="text-xs text-gray-500">
            Check your spam folder or{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              contact creator support
            </a>
          </p>
          <Link 
            href="/creators/login" 
            className="text-sm text-primary hover:text-primary/80 transition-colors duration-200"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </section>
  );
};

const CreatorCheckEmailPage = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <CreatorCheckEmailForm />
    </Suspense>
  );
};

export default CreatorCheckEmailPage;
