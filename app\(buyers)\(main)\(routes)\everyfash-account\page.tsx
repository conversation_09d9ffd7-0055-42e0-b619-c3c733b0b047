'use client';

import { FaChevronRight, FaRegCommentDots } from 'react-icons/fa6';
import { BsBoxSeam, BsChatDots, BsEnvelope, BsHeart, BsPerson, BsPinMap, BsShop } from 'react-icons/bs';
import { FiUsers } from 'react-icons/fi';
import { MdLogout } from 'react-icons/md';
import Link from 'next/link';
import { FaClipboard } from 'react-icons/fa';
import { IoHeartOutline, IoMailOutline } from 'react-icons/io5';
import { useAuth } from '@/lib/stores/auth-store';
import { useLogout } from '@/lib/hooks/use-auth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

export default function EveryfashAccountPage() {
  const { user, isAuthenticated } = useAuth();
  const logoutMutation = useLogout();
  const router = useRouter();

  // Redirect if not authenticated
  // useEffect(() => {
  //   if (!isAuthenticated) {
  //     router.push('/login');
  //   }
  // }, [isAuthenticated, router]);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // Show auth prompt if not authenticated
  if (!isAuthenticated || !user) {
    return <AuthPrompt type="account" />;
  }

  const accountSections = [
    {
      header: 'Orders',
      icon: <FaClipboard size={20} />,
      headerLink: '/orders',
    },
    {
      header: 'Inbox',
      icon: <IoMailOutline size={20} />,
      headerLink: '/inbox',
    },
    {
      header: 'Pending Reviews',
      icon: <FaRegCommentDots size={20} />,
      headerLink: '/pending-reviews',
    },
    {
      header: 'Wishlist',
      icon: <IoHeartOutline size={20} />,
      headerLink: '/wishlist',
    },
    {
      header: 'Followed Sellers',
      icon: <BsShop size={20}  />,
      headerLink: '/followed-sellers',
    },
    {
      header: 'Feedback',
      icon: <BsChatDots size={20}  />,
      headerLink: '/feedback',
    }
  ];

  const settingsSections = [
    {
      header: 'Account Management',
      icon: <BsPerson size={20} className="text-gray-700" />,
      headerLink: '/account',
    },
    {
      header: 'Address Book',
      icon: <BsPinMap size={20} className="text-gray-700" />,
      headerLink: '/address-book',
    },
  ];

  return (
    <section className="w-full max-w-lg mx-auto mt-20 py-4">
      {/* User Welcome Section */}
      <div className="bg-white px-4 py-6 rounded shadow-sm mb-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary/70 text-primary-foreground rounded-full flex items-center justify-center text-xl font-semibold mx-auto mb-3">
            {user.name
              .split(' ')
              .map(word => word.charAt(0))
              .join('')
              .toUpperCase()
              .slice(0, 2)}
          </div>
          <h1 className="text-lg font-semibold text-gray-800">{user.name}</h1>
          <p className="text-sm text-gray-600">{user.email}</p>
          <p className="text-xs text-gray-500 capitalize mt-1">{user.role} Account</p>
        </div>
      </div>

      {/* My Everyfash Account */}
      <h2 className="text-xs uppercase font-bold text-gray-600 mb-3">My Everyfash Account</h2>

      <div className="bg-white px-2 py-4 rounded shadow-sm mb-6">
        {accountSections.map((section) => (
          <div key={section.header} className="flex justify-between items-center mb-6 last:mb-0">
            <Link href={section.headerLink} className="flex items-center gap-3 text-sm text-gray-800">
              {section.icon}
              <p>{section.header}</p>
            </Link>

            <Link href={section.headerLink}>
              <FaChevronRight size={18} className="text-gray-500" />
            </Link>
          </div>
        ))}
      </div>

      {/* Account Settings */}
      <h2 className="text-xs uppercase font-bold text-gray-600 mb-3">Account Settings</h2>

      <div className="bg-white px-2 py-4 rounded shadow-sm mb-6">
        {settingsSections.map((section) => (
          <div key={section.header} className="flex justify-between items-center mb-6 last:mb-0">
            <Link href={section.headerLink} className="flex items-center gap-3 text-sm text-gray-800">
              {section.icon}
              <p>{section.header}</p>
            </Link>

            <Link href={section.headerLink}>
              <FaChevronRight size={18} className="text-gray-500" />
            </Link>
          </div>
        ))}
      </div>

      {/* Logout */}
      <div className="text-center flex justify-center w-full mt-6">
        <button
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
          className="text-red-600 hover:text-red-700 uppercase font-semibold flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <MdLogout size={18} />
          {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
        </button>
      </div>
    </section>
  );
}
