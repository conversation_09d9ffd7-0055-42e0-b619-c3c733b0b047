import React from 'react';
import ProductsCarousel from './ProductsCarousel';
import { products } from '@/lib/data';
import ProductCard from '@/components/ui/custom/ProductCard';
import Link from 'next/link';

interface TopDealsComponentProps {
  category: string;
  link: string;
}

const TopDealsComponent: React.FC<TopDealsComponentProps> = ({ category, link }) => {
  return (
    <section className="flex flex-col w-full mt-6">
     {/* Top Deals Header */}
      <section className="w-full bg-primary py-4 px-3 mb-2 text-primary-foreground">
        <div className="flex justify-between items-center rounded-sm">
          <h1 className="text-base font-bold">
            Top Deals{' '}
            <span className="hidden md:inline">| {category}</span>
          </h1>
          <Link href={link} className="text-sm font-bold">View All</Link>
        </div>
        <h1 className="text-sm font-bold md:hidden">
            {category}
        </h1>
      </section>

       {/* Carousel with Product Cards */}
        <ProductsCarousel>
            {products.map((product) => (
            <div key={product.id} className="p-1">
                <ProductCard  product={product} cardType='flashy' />
            </div>
            ))}
        </ProductsCarousel>
    </section>
  );
}

export default TopDealsComponent;
