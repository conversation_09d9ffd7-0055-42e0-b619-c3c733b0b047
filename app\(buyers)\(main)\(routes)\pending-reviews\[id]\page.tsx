'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useState } from 'react';
import { IoStar, IoStarOutline } from 'react-icons/io5';

interface ReviewProduct {
  id: string;
  productName: string;
  image: string;
}

const mockProduct: ReviewProduct = {
  id: '1',
  productName: 'Elegant Dress',
  image: '/images/ladies/short.jpg',
};

const ReviewPage = () => {
  const [rating, setRating] = useState<number>(0);
  const [reviewTitle, setReviewTitle] = useState('');
  const [reviewDetail, setReviewDetail] = useState('');
  const [name, setName] = useState('');

  const handleRating = (value: number) => {
    setRating(value);
  };

  const handleSubmit = () => {
    console.log({
      rating,
      reviewTitle,
      reviewDetail,
      name,
      productId: mockProduct.id,
    });
  };

  const notification = mockProduct; // Replace with actual product data from API later

  return (
    <section className="w-full mt-16 py-4">
      {/* Product Display */}
      <div className="flex items-center gap-3 border rounded-md p-3 bg-white">
        <div className="w-16 h-16 bg-gray-200 rounded">
          <img
            src={notification.image}
            alt={notification.productName}
            className="w-full h-full object-cover rounded"
          />
        </div>
        <p className="text-sm font-medium text-gray-800">
          {notification.productName}
        </p>
      </div>

      {/* Rate this Product */}
      <h2 className="text-sm font-medium uppercase mt-4 text-gray-600">Rate This Product</h2>
      <div className="bg-white p-4 rounded shadow-sm mt-2">
        <p className="text-sm text-gray-600 mb-2">Tap the stars to rate</p>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRating(star)}
              className="focus:outline-none"
            >
              {star <= rating ? (
                <IoStar className="text-yellow-400" size={24} />
              ) : (
                <IoStarOutline className="text-gray-400" size={24} />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Review Form */}
      <h3 className="text-sm font-medium uppercase mt-4 text-gray-600">Leave A Review</h3>

      <div className="bg-white p-2 rounded shadow-sm mt-2">
        {/* Review Title */}
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Review Title
        </label>
        <Input
          type="text"
          value={reviewTitle}
          onChange={(e) => setReviewTitle(e.target.value)}
          className="border rounded w-full p-2 mb-4"
          placeholder="e.g. Great Product!"
        />

        {/* Detailed Review */}
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Detailed Review
        </label>
        <Textarea
          value={reviewDetail}
          onChange={(e) => setReviewDetail(e.target.value)}
          className="border rounded w-full p-2 mb-4"
          rows={4}
          placeholder="Share your experience..."
        />

        {/* Your Name */}
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Your Name
        </label>
        <Input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="border rounded w-full p-2 mb-6"
          placeholder="e.g. John Doe"
        />

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          className="w-full bg-primary/90 text-white py-2 rounded"
        >
          Submit
        </Button>
      </div>
    </section>
  );
};

export default ReviewPage;
