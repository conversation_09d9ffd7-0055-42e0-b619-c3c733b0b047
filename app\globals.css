@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-inter), 'Inter', sans-serif;
}

/* Font Hierarchy */
.font-heading {
  font-family: var(--font-montserrat), 'Montserrat', sans-serif;
}

.font-subheading {
  font-family: var(--font-poppins), 'Poppins', sans-serif;
}

.font-body {
  font-family: var(--font-inter), 'Inter', sans-serif;
}

/* Apply font styles to specific elements */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-montserrat), 'Montserrat', sans-serif;
}

.text-title {
  font-family: var(--font-montserrat), 'Montserrat', sans-serif;
  font-weight: 700;
}

.text-subtitle {
  font-family: var(--font-poppins), 'Poppins', sans-serif;
  font-weight: 500;
}

.text-body {
  font-family: var(--font-inter), 'Inter', sans-serif;
  font-weight: 400;
}

.text-button {
  font-family: var(--font-poppins), 'Poppins', sans-serif;
  font-weight: 600;
}

/* Apply Poppins font to all buttons */
button, .button, [type='button'], [type='submit'], [type='reset'] {
  font-family: var(--font-poppins), 'Poppins', sans-serif;
  font-weight: 500;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;  /* Base primary color (neutral-900) */
    --primary-foreground: 0 0% 98%;

    /* Primary color variants - Neutral black scale */
    --primary-50: 0 0% 98%;    /* Lightest */
    --primary-100: 0 0% 95%;
    --primary-200: 0 0% 89%;
    --primary-300: 0 0% 83%;
    --primary-400: 0 0% 64%;
    --primary-500: 0 0% 45%;
    --primary-600: 0 0% 32%;
    --primary-700: 0 0% 25%;
    --primary-800: 0 0% 15%;
    --primary-900: 0 0% 9%;    /* Base color */
    --primary-950: 0 0% 4%;    /* Darkest */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;      /* Inverted for dark mode */
    --primary-foreground: 0 0% 9%;

    /* Dark mode primary color variants */
    --primary-50: 0 0% 4%;
    --primary-100: 0 0% 9%;
    --primary-200: 0 0% 15%;
    --primary-300: 0 0% 25%;
    --primary-400: 0 0% 32%;
    --primary-500: 0 0% 45%;
    --primary-600: 0 0% 64%;
    --primary-700: 0 0% 83%;
    --primary-800: 0 0% 89%;
    --primary-900: 0 0% 95%;
    --primary-950: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* main */
  .main-container {
    @apply flex min-h-screen flex-1 flex-col items-center ;
  }

  .bottombar {
    @apply fixed bottom-0 z-10 w-full border-t p-4 backdrop-blur-lg px-4 max-w-lg;
  }

  .bottombar_container {
    @apply flex items-center justify-between gap-1;
  }

  .bottombar_link {
    @apply relative flex flex-col items-center gap-2 rounded-lg p-2 flex-1 sm:px-1 sm:py-2.5;
  }

}

/* Custom Scrollbar Styling */
.scrollbar-thin {
  scrollbar-width: thin; /* Firefox */
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px; /* Thin width */
  height: 6px; /* For horizontal scrollbars */
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent; /* Transparent track */
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db; /* Gray-300 */
  border-radius: 10px;
}

/* styles.css or Editor.module.css */
.no-padding .bn-editor {
  padding-left: 0 !important;
  padding-right: 0 !important;
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}



