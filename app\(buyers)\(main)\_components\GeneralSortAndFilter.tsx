"use client";

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetTrigger, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

// Example categories and brands for the filter
const categories = [
  "Shirts", "Trousers", "Jeans", "Shorts", "Suits & Blazers", "Sweaters & Cardigans", "Jackets & Coats", "Underwear", "Socks",
  "Tops", "Skirts", "Dresses", "Bodycon Dresses", "Jumpsuits & Rompers", "Lingerie & Sleepwear",
  "Sneakers", "Boots", "Sandals", "Loafers", "Sports Shoes", "Heels", "Flats",
  "Bags", "Jewelry", "Watches", "Sunglasses", "Hats & Scarves", "Belts", "Bags & Wallets",
  "Kids' Clothing", "Kids' Footwear",
  "Men's Bales", "Women's Bales", "Kids' Bales", "Mixed Bales"
];

const brands = [
  'Nike', 'Adidas', 'Puma', 'Zara', 'H&M', 'Reebok', 'Champion', 'Rolex', 'Casio', 'Michael Kors',
];

const sizes = ['S', 'M', 'L', 'XL', 'XXL'];

const genders = ['Male', 'Female', 'Unisex'];

const GeneralSortAndFilter = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [sortOption, setSortOption] = useState<string>(searchParams.get('sort') || '');
  const [priceRange, setPriceRange] = useState<number[]>([
    Number(searchParams.get('minPrice')) || 0,
    Number(searchParams.get('maxPrice')) || 500,
  ]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>(searchParams.get('category')?.split(',') || []);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(searchParams.get('brands')?.split(',') || []);
  const [selectedSizes, setSelectedSizes] = useState<string[]>(searchParams.get('sizes')?.split(',') || []);
  const [selectedGender, setSelectedGender] = useState<string>(searchParams.get('gender') || '');

  const [isSheetOpen, setIsSheetOpen] = useState<boolean>(false);
  const [isSortDialogOpen, setIsSortDialogOpen] = useState<boolean>(false);
  const [showAllCategories, setShowAllCategories] = useState<boolean>(false);
  const [showAllBrands, setShowAllBrands] = useState<boolean>(false);

  const updateURLParams = (newSortOption?: string) => {
    const params = new URLSearchParams(searchParams.toString());

    if (newSortOption !== undefined) {
      params.set('sort', newSortOption);
    } else if (sortOption) {
      params.set('sort', sortOption);
    }

    if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
    if (priceRange[1] < 500) params.set('maxPrice', priceRange[1].toString());


    if (selectedCategories.length) {
        params.set('category', selectedCategories.join(','));
      } else {
        params.delete('category');
      }

    if (selectedBrands.length) {
      params.set('brands', selectedBrands.join(','));
    } else {
      params.delete('brands');
    }

    if (selectedSizes.length) {
      params.set('sizes', selectedSizes.join(','));
    } else {
      params.delete('sizes');
    }

    if (selectedGender) {
      params.set('gender', selectedGender);
    } else {
      params.delete('gender');
    }

    router.push(`?${params.toString()}`);
    setIsSheetOpen(false);
  };

  const resetFilters = () => {
    setPriceRange([0, 500]);
    setSelectedCategories([]);
    setSelectedBrands([]);
    setSelectedSizes([]);
    setSelectedGender('');
    setSortOption('');
    router.push(window.location.pathname);
    setIsSheetOpen(false);
  };

  const handleBrandChange = (brand: string) => {
    setSelectedBrands((prev) =>
      prev.includes(brand) ? prev.filter((b) => b !== brand) : [...prev, brand]
    );
  };

  const handleSizeChange = (size: string) => {
    setSelectedSizes((prev) =>
      prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]
    );
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category]
    );
  };

  const handleSortChange = (value: string) => {
    setSortOption(value);
    setIsSortDialogOpen(false);
    updateURLParams(value);
  };

  return (
    <section className="flex justify-between items-center my-4 px-2">
      <Dialog open={isSortDialogOpen} onOpenChange={setIsSortDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">Sort By</Button>
        </DialogTrigger>
        <DialogContent className="max-w-md w-[90%] rounded-lg p-6 sm:p-8">
          <DialogHeader>
            <DialogTitle>Sort By</DialogTitle>
          </DialogHeader>
          <RadioGroup value={sortOption} onValueChange={handleSortChange}>
            {['Price: Low to High', 'Price: High to Low', 'New Arrivals'].map((option) => (
              <div key={option} className="flex items-center">
                <RadioGroupItem value={option} />
                <label className="ml-2">{option}</label>
              </div>
            ))}
          </RadioGroup>
        </DialogContent>
      </Dialog>

      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button variant="outline">Filter</Button>
        </SheetTrigger>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Filter</SheetTitle>
          </SheetHeader>

          <div className="overflow-y-auto max-h-[70vh] pr-2 space-y-6">
            {/* Gender Filter */}
            <div>
              <h3 className="font-semibold">Gender</h3>
              <RadioGroup value={selectedGender} onValueChange={setSelectedGender}>
                {genders.map((gender) => (
                  <div key={gender} className="flex items-center">
                    <RadioGroupItem value={gender} />
                    <label className="ml-2">{gender}</label>
                  </div>
                ))}
              </RadioGroup>
            </div>

              {/* Category Filter */}
            <div>
              <h3 className="font-semibold">Category</h3>
              {(showAllCategories ? categories : categories.slice(0, 10)).map((category) => (
                <div key={category} className="flex items-center">
                  <Checkbox
                    checked={selectedCategories.includes(category)}
                    onCheckedChange={() => handleCategoryChange(category)}
                  />
                  <label className="ml-2">{category}</label>
                </div>
              ))}
              {!showAllCategories && <Button variant="link" onClick={() => setShowAllCategories(true)}>View More</Button>}
            </div>

            {/* Price Range */}
            <div>
              <h3 className="font-semibold">Price Range</h3>
              <Slider min={0} max={500} value={priceRange} onValueChange={setPriceRange} />
              <p>GH₵ {priceRange[0]} - GH₵ {priceRange[1]}</p>
            </div>

            {/* Brand Filter */}
            <div>
              <h3 className="font-semibold">Brand</h3>
              {(showAllBrands ? brands : brands.slice(0, 5)).map((brand) => (
                <div key={brand} className="flex items-center">
                  <Checkbox
                    checked={selectedBrands.includes(brand)}
                    onCheckedChange={() => handleBrandChange(brand)}
                  />
                  <label className="ml-2">{brand}</label>
                </div>
              ))}
              {!showAllBrands && <Button variant="link" onClick={() => setShowAllBrands(true)}>View More</Button>}
            </div>
          </div>

            {/* Actions */}
            <div className="flex justify-between mt-6">
            <Button variant="outline" onClick={resetFilters}>Reset</Button>
            <Button onClick={() => updateURLParams()}>Show Results</Button>
          </div>
        </SheetContent>
      </Sheet>
    </section>
  );
};

export default GeneralSortAndFilter;
