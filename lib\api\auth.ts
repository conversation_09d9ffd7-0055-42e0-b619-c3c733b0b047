import {
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  ForgotPasswordCredentials,
  ForgotPasswordResponse,
  ResetPasswordCredentials,
  ResetPasswordResponse,
  ChangePasswordCredentials,
  EmailVerificationResponse,
  ResendVerificationCredentials,
  ResendVerificationResponse,
  RegistrationResponse,
  ApiError,
  User,
} from '@/lib/types/auth';
import { BaseApiClient } from './base-client';
import { API_ENDPOINTS, API_CONFIG } from './config';

// Create API client instance
const apiClient = new BaseApiClient();

// Authentication API functions
export const authApi = {
  // Login user (public endpoint)
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    return apiClient.publicPost<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials);
  },

  // Register user (public endpoint) - now returns registration response without token
  register: async (credentials: RegisterCredentials): Promise<RegistrationResponse> => {
    const { role, ...registerData } = credentials;
    const endpoint = role === 'buyer' ? API_ENDPOINTS.AUTH.REGISTER_BUYER : API_ENDPOINTS.AUTH.REGISTER_CREATOR;
    console.log('Registration API Call:', {
      endpoint,
      data: registerData,
      fullUrl: `${API_CONFIG.BASE_URL}${endpoint}`
    });
    try {
      const result = await apiClient.publicPost<RegistrationResponse>(endpoint, registerData);
      console.log('Registration API Success:', result);
      return result;
    } catch (error) {
      console.log('Registration API Error:', error);
      throw error;
    }
  },


  // Resend verification code (public endpoint)
  resendVerificationCode: async (phone: string, countryCode: string): Promise<{ success: boolean }> => {
    return apiClient.publicPost<{ success: boolean }>('/auth/resend-code', { phone, countryCode });
  },

  // Forgot password (public endpoint)
  forgotPassword: async (credentials: ForgotPasswordCredentials): Promise<ForgotPasswordResponse> => {
    return apiClient.publicPost<ForgotPasswordResponse>(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, credentials);
  },

  // Reset password with token (public endpoint)
  resetPassword: async (token: string, credentials: ResetPasswordCredentials): Promise<ResetPasswordResponse> => {
    return apiClient.publicPatch<ResetPasswordResponse>(`${API_ENDPOINTS.AUTH.RESET_PASSWORD}/${token}`, credentials);
  },

  // Verify email with token (public endpoint)
  verifyEmail: async (token: string): Promise<EmailVerificationResponse> => {
    return apiClient.publicGet<EmailVerificationResponse>(`${API_ENDPOINTS.AUTH.VERIFY_EMAIL}/${token}`);
  },

  // Resend verification email (public endpoint)
  resendVerification: async (credentials: ResendVerificationCredentials): Promise<ResendVerificationResponse> => {
    console.log('Resend Verification API Call:', {
      endpoint: API_ENDPOINTS.AUTH.RESEND_VERIFICATION,
      data: credentials,
      fullUrl: `${API_CONFIG.BASE_URL}${API_ENDPOINTS.AUTH.RESEND_VERIFICATION}`
    });
    try {
      const result = await apiClient.publicPost<ResendVerificationResponse>(API_ENDPOINTS.AUTH.RESEND_VERIFICATION, credentials);
      console.log('Resend Verification API Success:', result);
      return result;
    } catch (error) {
      console.log('Resend Verification API Error:', error);
      throw error;
    }
  },

  // Change password
  changePassword: async (credentials: ChangePasswordCredentials): Promise<{ success: boolean }> => {
    return apiClient.put<{ success: boolean }>(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, credentials);
  },

  // Refresh token removed - using single token approach

  // Get current user profile
  getProfile: async (): Promise<User> => {
    return apiClient.get<User>(API_ENDPOINTS.AUTH.PROFILE);
  },

  // Update user profile
  updateProfile: async (updates: Partial<User>): Promise<User> => {
    return apiClient.put<User>(API_ENDPOINTS.AUTH.PROFILE, updates);
  },

  // Logout (server-side cleanup)
  logout: async (): Promise<{ success: boolean }> => {
    return apiClient.post<{ success: boolean }>(API_ENDPOINTS.AUTH.LOGOUT);
  },

  // Google OAuth authentication
  initiateGoogleAuth: (role: 'buyer' | 'creator'): string => {
    // Return the Google OAuth URL for the specific role
    const endpoint = role === 'buyer' ? API_ENDPOINTS.AUTH.GOOGLE_OAUTH_BUYER : API_ENDPOINTS.AUTH.GOOGLE_OAUTH_CREATOR;
    return `${API_CONFIG.BASE_URL}${endpoint}`;
  },
};

// Error handling utility (for backward compatibility)
export const handleApiError = (error: unknown): ApiError => {
  console.log('handleApiError - Raw error:', error);

  // Handle axios-style errors with response.data
  if (error && typeof error === 'object' && 'response' in error) {
    const axiosError = error as any;
    if (axiosError.response?.data) {
      const errorData = axiosError.response.data;
      console.log('handleApiError - Response data:', errorData);

      // Handle the API error structure: { status: "fail", message: "...", error: {...} }
      if (errorData.message) {
        return {
          message: errorData.message,
          status: errorData.error?.statusCode || axiosError.response.status,
        };
      }
    }
  }

  // Handle fetch-style errors or direct error objects
  if (error && typeof error === 'object' && 'message' in error) {
    const errorObj = error as any;

    // Try to parse JSON if the message contains JSON
    if (typeof errorObj.message === 'string') {
      try {
        const parsedError = JSON.parse(errorObj.message);
        if (parsedError.message) {
          return {
            message: parsedError.message,
            status: parsedError.error?.statusCode,
          };
        }
      } catch {
        // Not JSON, use the message as-is
        return {
          message: errorObj.message,
        };
      }
    }
  }

  // Handle Error instances
  if (error instanceof Error) {
    return {
      message: error.message,
    };
  }

  return {
    message: 'An unexpected error occurred',
  };
};
