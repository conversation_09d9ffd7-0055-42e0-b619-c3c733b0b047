'use client'; // Ensures this runs only on the client side

import "@blocknote/core/fonts/inter.css";
import { useCreateBlockNote } from "@blocknote/react";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { PartialBlock } from "@blocknote/core";

// Props for our editor
interface EditorProps {
  initialContent?: string; 
  onChange: (val: string) => void;
  editable?: boolean;
}

// Our <Editor> component we can reuse
export default function Editor({ initialContent,onChange,editable }: EditorProps) {
  // Creates a new editor instance
  const editor = useCreateBlockNote({
    initialContent: initialContent ? JSON.parse(initialContent) as PartialBlock[] : undefined,
    defaultStyles: false,
  });

  // Renders the editor instance
  return <div className="">
    <BlockNoteView
  editable={editable}
  editor={editor}
  theme="light"
  style={
    {
      fontSize: "14px",
      padding:  "0px"
    }
  }
  onChange={() => {
    onChange(JSON.stringify(editor.topLevelBlocks, null, 2));
  }}
  sideMenu={editable}
  className={!editable ? "no-padding" : ""} // Adjust px here
/>


  </div>
  
}
