'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useLogin } from '@/lib/hooks/use-auth';
import { useAuth } from '@/lib/stores/auth-store';

/**
 * Debug component to test login error handling
 * Add this to any page to test: <LoginErrorTest />
 */
const LoginErrorTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('wrongpassword');
  const loginMutation = useLogin();
  const { error, isLoading } = useAuth();

  const testLogin = () => {
    loginMutation.mutate({
      email,
      password,
      role: 'buyer',
    });
  };

  const testCreatorLogin = () => {
    loginMutation.mutate({
      email,
      password,
      role: 'creator',
    });
  };

  return (
    <div className="p-8 max-w-md mx-auto space-y-6 border border-gray-200 rounded-lg">
      <h2 className="text-xl font-bold text-center">Login Error Test</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">Email</label>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter test email"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Password</label>
          <Input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter wrong password"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Button 
          onClick={testLogin} 
          disabled={isLoading || loginMutation.isPending}
          className="w-full"
        >
          Test Buyer Login Error
        </Button>
        
        <Button 
          onClick={testCreatorLogin} 
          disabled={isLoading || loginMutation.isPending}
          className="w-full"
          variant="outline"
        >
          Test Creator Login Error
        </Button>
      </div>

      {/* Error Display */}
      {(error || loginMutation.error) && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          <strong>Error from auth store:</strong> {error}
          <br />
          <strong>Error from mutation:</strong> {(loginMutation.error as any)?.message}
          <br />
          <strong>Raw mutation error:</strong> 
          <pre className="text-xs mt-2 overflow-auto">
            {JSON.stringify(loginMutation.error, null, 2)}
          </pre>
        </div>
      )}

      {/* Success Display */}
      {loginMutation.isSuccess && (
        <div className="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
          Login successful! (This shouldn't happen with wrong credentials)
        </div>
      )}

      {/* Loading Display */}
      {(isLoading || loginMutation.isPending) && (
        <div className="p-3 text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-md">
          Testing login... Please wait.
        </div>
      )}

      <div className="text-xs text-gray-500 space-y-1">
        <p><strong>Instructions:</strong></p>
        <p>1. Enter an invalid email/password combination</p>
        <p>2. Click one of the test buttons</p>
        <p>3. Check that the error message shows: "Incorrect email or password"</p>
        <p>4. Verify that both auth store error and mutation error are displayed</p>
      </div>
    </div>
  );
};

export default LoginErrorTest;
