import { CategoriesHierarchyResponse } from '@/lib/types/categories';
import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';
import { handleCategoriesApiError as handleError } from './errors';

// Create API client instance
const categoriesApiClient = new BaseApiClient();

// Categories API functions
export const categoriesApi = {
  // Get categories hierarchy (public endpoint)
  getHierarchy: async (): Promise<CategoriesHierarchyResponse> => {
    return categoriesApiClient.publicGet<CategoriesHierarchyResponse>(API_ENDPOINTS.CATEGORIES.HIERARCHY);
  },
};

// Error handling utility (for backward compatibility)
export const handleCategoriesApiError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unexpected error occurred while fetching categories';
};
