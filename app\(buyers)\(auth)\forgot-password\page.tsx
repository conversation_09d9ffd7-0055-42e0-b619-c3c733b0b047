'use client';

import React from 'react';
import Link from 'next/link';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForgotPassword } from '@/lib/hooks/use-auth';
import { useAuth, useAuthActions } from '@/lib/stores/auth-store';
import { GuestGuard } from '@/lib/components/AuthGuard';

// Zod Validation Schema
const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address").nonempty("Email is required."),
});

const ForgotPasswordPage = () => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const forgotPasswordMutation = useForgotPassword();
  const { isLoading, error } = useAuth();
  const { clearError } = useAuthActions();

  // Clear any existing errors when component mounts
  React.useEffect(() => {
    clearError();
  }, [clearError]);

  const onSubmit = (data: { email: string }) => {
    forgotPasswordMutation.mutate({
      email: data.email,
    });
  };

  return (
    <GuestGuard>
      <section className='flex flex-col items-center w-full max-w-md my-20'>
        {/* Back Link */}
        <div className="w-full mb-4">
          <Link 
            href="/login" 
            className="flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Login
          </Link>
        </div>

        {/* Header */}
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Forgot Password</h1>

        {/* About */}
        <p className="text-sm text-center mb-6 text-gray-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>

        {/* Form */}
        <div className="md:max-w-lg mx-auto w-11/12 py-4 bg-white rounded-lg space-y-4">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full">
            {/* Error Display */}
            {error && (
              <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                {error}
              </div>
            )}

            {/* Email Field */}
            <div className="w-full">
              <label className="text-sm font-medium text-gray-800">Email Address</label>
              <div className="mt-2">
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full"
                      disabled={isLoading || forgotPasswordMutation.isPending}
                    />
                  )}
                />
                {errors.email && <span className="text-red-500 text-sm">{errors.email.message}</span>}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              size="lg"
              className="w-full h-12 text-lg"
              disabled={isLoading || forgotPasswordMutation.isPending}
            >
              {isLoading || forgotPasswordMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                'Send Reset Link'
              )}
            </Button>
          </form>
        </div>

        {/* Link to Login */}
        <div className="mt-6">
          <p className="text-center text-gray-600">
            Remember your password?{' '}
            <Link href="/login" className="text-primary hover:text-primary/80 transition-colors duration-200">
              Sign in
            </Link>
          </p>
        </div>
      </section>
    </GuestGuard>
  );
};

export default ForgotPasswordPage;
