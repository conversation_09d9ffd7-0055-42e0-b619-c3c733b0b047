/**
 * API-specific types and interfaces
 * These types are specifically for API requests/responses and are separate from domain types
 */

// Base API response structure
export interface BaseApiResponse<T = any> {
  status: string;
  message?: string;
  data?: T;
  error?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends BaseApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Request configuration types
export interface ApiRequestConfig {
  timeout?: number;
  retries?: number;
  requiresAuth?: boolean;
  isPublic?: boolean;
  headers?: Record<string, string>;
}

// File upload types
export interface FileUploadConfig extends ApiRequestConfig {
  maxFileSize?: number;
  allowedTypes?: string[];
  multiple?: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// Authentication API types
export interface LoginRequest {
  email: string;
  password: string;
  role: 'buyer' | 'creator';
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  passwordConfirm: string;
  role: 'buyer' | 'creator';
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  password: string;
  passwordConfirm: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  password: string;
  passwordConfirm: string;
}

export interface ResendVerificationRequest {
  email: string;
}

// Onboarding API types
export interface BusinessInfoRequest {
  businessName: string;
  businessType: string;
  ownerName: string;
  ownerID: string;
  phoneNumber: string;
  taxId: string;
  businessAddress: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    country: string;
    digitalGps?: string;
  };
  verificationDocuments?: File[];
}

export interface PaymentInfoRequest {
  bankName: string;
  accountNumber: string;
  accountName: string;
  routingNumber?: string;
  swiftCode?: string;
  paymentMethod: 'bank_transfer' | 'mobile_money' | 'paypal';
}

export interface ShopInfoRequest {
  shopName: string;
  shopDescription: string;
  shopLogo?: File;
  shopBanner?: File;
  customerCare: {
    email: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    country: string;
  };
}

export interface ShippingInfoRequest {
  shippingAddress: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  returnAddress: {
    useSameAddress: boolean;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  shippingMethods: string[];
  estimatedDeliveryDays: number;
}

// Categories API types
export interface CategoryRequest {
  name: string;
  description?: string;
  parentId?: string;
  featured?: boolean;
  active?: boolean;
}

// Search and filter types
export interface SearchParams {
  query?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  brand?: string;
  size?: string;
  color?: string;
  condition?: string;
  location?: string;
  sortBy?: 'price' | 'date' | 'popularity' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

// Bulk operation types
export interface BulkOperationRequest<T> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
}

export interface BulkOperationResponse<T> {
  success: T[];
  failed: Array<{
    item: T;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// Webhook types
export interface WebhookPayload<T = any> {
  event: string;
  timestamp: string;
  data: T;
  signature?: string;
}

// Cache types
export interface CacheConfig {
  ttl?: number; // Time to live in seconds
  key?: string;
  tags?: string[];
  invalidateOn?: string[];
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// API versioning types
export interface ApiVersion {
  version: string;
  deprecated?: boolean;
  deprecationDate?: string;
  supportedUntil?: string;
}

// Health check types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: Record<string, {
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }>;
}

// Metrics types
export interface ApiMetrics {
  requestCount: number;
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  lastUpdated: string;
}
