type Payout = {
    id: number;
    payoutId: string;
    vendor: string;
    amount: string;
    date: string;
    method: string;
    status: string;
    accountDetails:
      | {
          bankName: string;
          accountName: string;
          accountNumber: string;
          branch: string;
          swiftCode: string;
        }
      | {
          serviceProvider: string;
          momoName: string;
          momoNumber: string;
        };
  };
  
  const payoutsData: Payout[] = [
    {
      id: 1,
      payoutId: "P-1001",
      vendor: "Thrift Shop",
      amount: "GHS 2,500",
      date: "2024-03-10",
      method: "Bank Transfer",
      status: "Pending",
      accountDetails: {
        bankName: "Bank of Ghana",
        accountName: "Thrift Shop Ltd",
        accountNumber: "*********",
        branch: "Accra",
        swiftCode: "BOG123",
      },
    },
    {
      id: 2,
      payoutId: "P-1002",
      vendor: "Urban Trends",
      amount: "GHS 1,800",
      date: "2024-03-11",
      method: "Mobile Money",
      status: "Completed",
      accountDetails: {
        serviceProvider: "MTN",
        momoName: "Urban Trends Ltd",
        momoNumber: "**********",
      },
    },
    {
      id: 3,
      payoutId: "P-1003",
      vendor: "Luxury Fits",
      amount: "GHS 3,200",
      date: "2024-03-12",
      method: "Bank Transfer",
      status: "Pending",
      accountDetails: {
        bankName: "GCB Bank",
        accountName: "Luxury Fits Ltd",
        accountNumber: "*********",
        branch: "Kumasi",
        swiftCode: "GCB987",
      },
    },
    {
      id: 4,
      payoutId: "P-1004",
      vendor: "Chic Styles",
      amount: "GHS 5,000",
      date: "2024-03-13",
      method: "Mobile Money",
      status: "Failed",
      accountDetails: {
        serviceProvider: "Vodafone Cash",
        momoName: "Chic Styles Ltd",
        momoNumber: "**********",
      },
    },
    {
      id: 5,
      payoutId: "P-1005",
      vendor: "StreetWear Co.",
      amount: "GHS 4,700",
      date: "2024-03-14",
      method: "Bank Transfer",
      status: "Completed",
      accountDetails: {
        bankName: "Stanbic Bank",
        accountName: "StreetWear Co. Ltd",
        accountNumber: "*********",
        branch: "Tema",
        swiftCode: "STB456",
      },
    },
    {
      id: 6,
      payoutId: "P-1006",
      vendor: "Trendy Closet",
      amount: "GHS 2,300",
      date: "2024-03-15",
      method: "Mobile Money",
      status: "Pending",
      accountDetails: {
        serviceProvider: "AirtelTigo Money",
        momoName: "Trendy Closet Ltd",
        momoNumber: "**********",
      },
    },
    {
      id: 7,
      payoutId: "P-1007",
      vendor: "Elite Fashion",
      amount: "GHS 3,500",
      date: "2024-03-16",
      method: "Bank Transfer",
      status: "Completed",
      accountDetails: {
        bankName: "Ecobank",
        accountName: "Elite Fashion Ltd",
        accountNumber: "*********",
        branch: "Takoradi",
        swiftCode: "ECO789",
      },
    },
    {
      id: 8,
      payoutId: "P-1008",
      vendor: "Vintage Vogue",
      amount: "GHS 1,900",
      date: "2024-03-17",
      method: "Mobile Money",
      status: "Pending",
      accountDetails: {
        serviceProvider: "MTN",
        momoName: "Vintage Vogue Ltd",
        momoNumber: "**********",
      },
    },
    {
      id: 9,
      payoutId: "P-1009",
      vendor: "Sneaker Kings",
      amount: "GHS 6,200",
      date: "2024-03-18",
      method: "Bank Transfer",
      status: "Success",
      accountDetails: {
        bankName: "Fidelity Bank",
        accountName: "Sneaker Kings Ltd",
        accountNumber: "*********",
        branch: "Sunyani",
        swiftCode: "FID741",
      },
    },
    {
      id: 10,
      payoutId: "P-1010",
      vendor: "Fashion Hub",
      amount: "GHS 2,750",
      date: "2024-03-19",
      method: "Mobile Money",
      status: "Completed",
      accountDetails: {
        serviceProvider: "Vodafone Cash",
        momoName: "Fashion Hub Ltd",
        momoNumber: "**********",
      },
    },
  ];
  
  
  export default payoutsData;
  