'use client';

import React from 'react';
import { Controller } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Professional form section component
export const FormSection = ({ 
  title, 
  description, 
  children, 
  className = "" 
}: { 
  title: string; 
  description?: string; 
  children: React.ReactNode; 
  className?: string; 
}) => (
  <Card className={`bg-white border border-gray-200 shadow-sm ${className}`}>
    <CardHeader className="pb-4">
      <CardTitle className="text-lg font-semibold text-gray-900">{title}</CardTitle>
      {description && (
        <p className="text-sm text-gray-600 mt-1">{description}</p>
      )}
    </CardHeader>
    <CardContent className="space-y-4">
      {children}
    </CardContent>
  </Card>
);

// Professional input field component
export const FormInput = ({
  label,
  name,
  control,
  errors,
  placeholder,
  required = false,
  disabled = false,
  type = "text",
  description,
  ...props
}: {
  label: string;
  name: string;
  control: any;
  errors: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  type?: string;
  description?: string;
  [key: string]: any;
}) => (
  <div className="space-y-1" {...props}>
    <label className="text-sm font-medium text-gray-900">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    {description && (
      <p className="text-xs text-gray-500">{description}</p>
    )}
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Input
          {...field}
          type={type}
          placeholder={placeholder}
          disabled={disabled}
          className={`transition-colors ${
            errors[name] 
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
              : 'border-gray-300 focus:border-primary focus:ring-primary'
          }`}
          onChange={(e) => {
            const value = type === 'number' ? Number(e.target.value) || 0 : e.target.value;
            field.onChange(value);
          }}
        />
      )}
    />
    {errors[name] && (
      <p className="text-xs text-red-600 flex items-center mt-1">
        <span className="mr-1">⚠</span>
        {errors[name]?.message}
      </p>
    )}
  </div>
);

// Professional textarea component
export const FormTextarea = ({
  label,
  name,
  control,
  errors,
  placeholder,
  required = false,
  disabled = false,
  rows = 3,
  description
}: {
  label: string;
  name: string;
  control: any;
  errors: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  rows?: number;
  description?: string;
}) => (
  <div className="space-y-1">
    <label className="text-sm font-medium text-gray-900">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    {description && (
      <p className="text-xs text-gray-500">{description}</p>
    )}
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Textarea
          {...field}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows}
          className={`transition-colors resize-none ${
            errors[name] 
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
              : 'border-gray-300 focus:border-primary focus:ring-primary'
          }`}
        />
      )}
    />
    {errors[name] && (
      <p className="text-xs text-red-600 flex items-center mt-1">
        <span className="mr-1">⚠</span>
        {errors[name]?.message}
      </p>
    )}
  </div>
);

// Professional select component
export const FormSelect = ({
  label,
  name,
  control,
  errors,
  placeholder,
  required = false,
  disabled = false,
  options,
  description
}: {
  label: string;
  name: string;
  control: any;
  errors: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options: Array<{ value: string; label: string }>;
  description?: string;
}) => (
  <div className="space-y-1">
    <label className="text-sm font-medium text-gray-900">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    {description && (
      <p className="text-xs text-gray-500">{description}</p>
    )}
    <Controller
      name={name}
      control={control}
      render={({ field }) => {
        const currentValue = field.value ? String(field.value) : '';

        return (
          <Select
            value={currentValue}
            onValueChange={field.onChange}
            disabled={disabled}
            key={`${name}-${currentValue || 'empty'}`} // Force re-render when value changes
          >
            <SelectTrigger className={`transition-colors ${
              errors[name]
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-primary focus:ring-primary'
            }`}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }}
    />
    {errors[name] && (
      <p className="text-xs text-red-600 flex items-center mt-1">
        <span className="mr-1">⚠</span>
        {errors[name]?.message}
      </p>
    )}
  </div>
);

// Professional checkbox component
export const FormCheckbox = ({
  label,
  name,
  control,
  disabled = false,
  description
}: {
  label: string;
  name: string;
  control: any;
  disabled?: boolean;
  description?: string;
}) => (
  <div className="space-y-1">
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <div className="flex items-start space-x-3">
          <Checkbox
            id={name}
            checked={field.value}
            onCheckedChange={field.onChange}
            disabled={disabled}
            className="mt-0.5"
          />
          <div className="space-y-1">
            <label htmlFor={name} className="text-sm font-medium text-gray-900 cursor-pointer">
              {label}
            </label>
            {description && (
              <p className="text-xs text-gray-500">{description}</p>
            )}
          </div>
        </div>
      )}
    />
  </div>
);

// Professional form layout wrapper
export const FormLayout = ({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) => (
  <div className={`max-w-4xl mx-auto space-y-6 ${className}`}>
    {children}
  </div>
);

// Professional form header
export const FormHeader = ({ 
  title, 
  description 
}: { 
  title: string; 
  description?: string; 
}) => (
  <div className="text-center space-y-2 mb-8">
    <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
    {description && (
      <p className="text-gray-600 max-w-2xl mx-auto">{description}</p>
    )}
  </div>
);

// Professional form actions
export const FormActions = ({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) => (
  <Card className={`bg-white border border-gray-200 shadow-sm ${className}`}>
    <CardContent className="py-4">
      <div className="flex flex-col sm:flex-row gap-3 justify-between">
        {children}
      </div>
    </CardContent>
  </Card>
);
