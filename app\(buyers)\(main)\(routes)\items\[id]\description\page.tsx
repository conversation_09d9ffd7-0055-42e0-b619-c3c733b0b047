import React from 'react';

const ProductDescription = () => {
  return (
    <section className='w-full mt-20'>
      {/* Description Section */}
      <section className="bg-white my-2 py-3 px-3 rounded shadow-sm">
        <h1 className="text-sm font-semibold text-gray-600 border-b pb-2">Description</h1>
        <div className="text-sm text-gray-700 mt-2 space-y-2">
          <p>This elegant ladies' dress is designed to bring out your confidence and beauty.</p>
          <p>Crafted from high-quality fabric that offers comfort and durability.</p>
          <p>Perfect for evening parties, casual outings, or formal gatherings.</p>
          <p>Its unique half-sleeve design provides a blend of style and convenience.</p>
          <p>Available in various sizes to suit your preference.</p>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="bg-white my-2 py-3 px-3 rounded shadow-sm">
        <h1 className="text-sm font-semibold text-gray-600 border-b pb-2">Key Features</h1>
        <div className="text-sm text-gray-700 mt-2 space-y-1">
          <ul className="list-disc list-inside">
            <li>Material: Premium Cotton Blend</li>
            <li>Design: Elegant Half Sleeve</li>
            <li>Occasion: Casual, Party, Formal</li>
            <li>Care Instructions: Machine Washable</li>
            <li>Comfortable Fit with Modern Design</li>
          </ul>
        </div>
      </section>

      {/* Specification Section */}
      <section className="bg-white my-2 py-3 px-3 rounded shadow-sm">
        <h1 className="text-sm font-semibold text-gray-600 border-b pb-2">Specification</h1>
        <div className="text-sm text-gray-700 mt-2 space-y-2">
          <p><span className="font-semibold">Material:</span> Cotton Blend</p>
          <p><span className="font-semibold">Sleeve Type:</span> Half Sleeve</p>
          <p><span className="font-semibold">Neckline:</span> Round Neck</p>
          <p><span className="font-semibold">Fit:</span> Regular Fit</p>
          <p><span className="font-semibold">Color Options:</span> Black, Red, Blue</p>
        </div>
      </section>
    </section>
  );
};

export default ProductDescription;
