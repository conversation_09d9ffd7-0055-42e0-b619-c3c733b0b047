/**
 * API Error Handling Module
 * Provides standardized error handling across all API modules
 */

import { HTTP_STATUS } from './config';

// Error types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

export interface ValidationError extends ApiError {
  field?: string;
  value?: any;
}

export interface NetworkError extends ApiError {
  isNetworkError: true;
  originalError?: Error;
}

export interface AuthenticationError extends ApiError {
  isAuthError: true;
  requiresLogin?: boolean;
}

// Error classes
export class ApiErrorHandler {
  /**
   * Handle and transform API errors into standardized format
   */
  static handleError(error: unknown): ApiError {
    // Handle fetch/network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return this.createNetworkError(error);
    }

    // Handle timeout errors
    if (error instanceof Error && error.name === 'AbortError') {
      return this.createTimeoutError();
    }

    // Handle API response errors
    if (error instanceof Error) {
      return this.parseApiError(error);
    }

    // Handle unknown errors
    return this.createUnknownError();
  }

  /**
   * Create network error
   */
  private static createNetworkError(originalError: Error): NetworkError {
    return {
      message: 'Network connection failed. Please check your internet connection.',
      code: 'NETWORK_ERROR',
      isNetworkError: true,
      originalError,
    };
  }

  /**
   * Create timeout error
   */
  private static createTimeoutError(): ApiError {
    return {
      message: 'Request timed out. Please try again.',
      code: 'TIMEOUT_ERROR',
      status: 408,
    };
  }

  /**
   * Parse API error from response
   */
  private static parseApiError(error: Error): ApiError {
    const message = error.message;

    // Check for specific HTTP status codes
    if (message.includes('401')) {
      return this.createAuthenticationError();
    }

    if (message.includes('403')) {
      return this.createForbiddenError();
    }

    if (message.includes('404')) {
      return this.createNotFoundError();
    }

    if (message.includes('422')) {
      return this.createValidationError(message);
    }

    if (message.includes('500')) {
      return this.createServerError();
    }

    // Default API error
    return {
      message: message || 'An error occurred while processing your request.',
      code: 'API_ERROR',
    };
  }

  /**
   * Create authentication error
   */
  private static createAuthenticationError(): AuthenticationError {
    return {
      message: 'Your session has expired. Please log in again.',
      code: 'AUTHENTICATION_ERROR',
      status: HTTP_STATUS.UNAUTHORIZED,
      isAuthError: true,
      requiresLogin: true,
    };
  }

  /**
   * Create forbidden error
   */
  private static createForbiddenError(): ApiError {
    return {
      message: 'You do not have permission to perform this action.',
      code: 'FORBIDDEN_ERROR',
      status: HTTP_STATUS.FORBIDDEN,
    };
  }

  /**
   * Create not found error
   */
  private static createNotFoundError(): ApiError {
    return {
      message: 'The requested resource was not found.',
      code: 'NOT_FOUND_ERROR',
      status: HTTP_STATUS.NOT_FOUND,
    };
  }

  /**
   * Create validation error
   */
  private static createValidationError(message: string): ValidationError {
    return {
      message: message || 'Please check your input and try again.',
      code: 'VALIDATION_ERROR',
      status: 422,
    };
  }

  /**
   * Create server error
   */
  private static createServerError(): ApiError {
    return {
      message: 'A server error occurred. Please try again later.',
      code: 'SERVER_ERROR',
      status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
    };
  }

  /**
   * Create unknown error
   */
  private static createUnknownError(): ApiError {
    return {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNKNOWN_ERROR',
    };
  }

  /**
   * Check if error is a network error
   */
  static isNetworkError(error: ApiError): error is NetworkError {
    return 'isNetworkError' in error && error.isNetworkError === true;
  }

  /**
   * Check if error is an authentication error
   */
  static isAuthenticationError(error: ApiError): error is AuthenticationError {
    return 'isAuthError' in error && error.isAuthError === true;
  }

  /**
   * Check if error is a validation error
   */
  static isValidationError(error: ApiError): error is ValidationError {
    return error.code === 'VALIDATION_ERROR';
  }

  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: ApiError): string {
    // Return specific messages for different error types
    if (this.isNetworkError(error)) {
      return 'Please check your internet connection and try again.';
    }

    if (this.isAuthenticationError(error)) {
      return 'Please log in to continue.';
    }

    if (error.code === 'SERVER_ERROR') {
      return 'Our servers are experiencing issues. Please try again in a few minutes.';
    }

    // Return the original message for other errors
    return error.message;
  }
}

// Utility functions for backward compatibility
export const handleApiError = (error: unknown): ApiError => {
  return ApiErrorHandler.handleError(error);
};

export const handleAuthApiError = (error: unknown): ApiError => {
  return ApiErrorHandler.handleError(error);
};

export const handleOnboardingApiError = (error: unknown): { message: string } => {
  const apiError = ApiErrorHandler.handleError(error);
  return { message: apiError.message };
};

export const handleCategoriesApiError = (error: unknown): string => {
  const apiError = ApiErrorHandler.handleError(error);
  return apiError.message;
};
