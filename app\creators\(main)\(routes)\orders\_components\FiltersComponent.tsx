"use client";

import { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { FilterIcon } from "lucide-react";

export default function FilterOrdersDrawer() {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isPrepaid, setIsPrepaid] = useState(false);
  const [isPostpaid, setIsPostpaid] = useState(false);

  const handleApplyFilters = () => {
    console.log({
      startDate,
      endDate,
      paymentType: isPrepaid ? "Prepaid" : isPostpaid ? "Postpaid" : "All",
    });
  };

  const clearFilters = () => {
    setStartDate(null);
    setEndDate(null);
    setIsPrepaid(false);
    setIsPostpaid(false);
  };

  return (
    <section className="w-full">
    <Drawer>

      <DrawerTrigger asChild>
        <Button variant="outline" className="h-12 text-primary hover:text-primary/80 transition-colors duration-200"><FilterIcon/></Button>
      </DrawerTrigger>
      <DrawerContent className="p-6 w-full">

        <DrawerHeader>
            <DrawerTitle>Filter Orders</DrawerTitle>
        </DrawerHeader>


        {/* Date Range Filter */}
        <div className="mb-4 w-full">
          <label className="block text-sm font-medium mb-1">Start Date</label>
          <DatePicker
            selected={startDate}
            onChange={(date) => setStartDate(date)}
            dateFormat="yyyy-MM-dd"
            className="border rounded w-full px-3 py-2"
            placeholderText="Select start date"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">End Date</label>
          <DatePicker
            selected={endDate}
            onChange={(date) => setEndDate(date)}
            dateFormat="yyyy-MM-dd"
            className="border rounded w-full px-3 py-2"
            placeholderText="Select end date"
          />
        </div>

        {/* Payment Type Filter */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Payment Type</label>
          <div className="flex gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={isPrepaid}
                onCheckedChange={() => {
                  setIsPrepaid(!isPrepaid);
                //   setIsPostpaid(false);
                }}
              />
              <span>Prepaid</span>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={isPostpaid}
                onCheckedChange={() => {
                  setIsPostpaid(!isPostpaid);
                //   setIsPrepaid(false);
                }}
              />
              <span>Postpaid</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={clearFilters}>
            Clear
          </Button>
          <Button onClick={handleApplyFilters} className="bg-primary hover:bg-blue-700">Apply Filters</Button>
        </div>
      </DrawerContent>
    </Drawer>
    </section>
  );
}
