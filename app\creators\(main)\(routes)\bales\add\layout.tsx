import type { Metada<PERSON> } from "next";
import StepProgress from "./_components/StepProgress";
import StepNavigation from "./_components/StepNavigation";
import { Toaster } from "@/components/ui/toaster";


export const metadata: Metadata = {
  title: "Everyfash Vendors",
  description: "Largest Fashion marketplace",
};

export default function AddProductLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="w-full">
        <h2 className="text-sm uppercase font-bold mt-4 mb-3">Add New Product</h2>
        <aside className=" p-2 my-2 bg-white rounded-lg">
            <StepProgress />
        </aside>
        <section className="w-full">
            {children}
        </section>
        <Toaster />

    </main>
  );
}
