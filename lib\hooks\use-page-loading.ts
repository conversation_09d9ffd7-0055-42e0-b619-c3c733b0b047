'use client';

import { useLoading } from '@/lib/providers/loading-provider';
import { useEffect } from 'react';

/**
 * Hook to show loading state for async operations
 * @param isLoading - Whether the operation is loading
 * @param text - Loading text to display
 */
export function usePageLoading(isLoading: boolean, text: string = 'Loading...') {
  const { setLoading } = useLoading();

  useEffect(() => {
    setLoading(isLoading, text);
  }, [isLoading, text, setLoading]);
}

/**
 * Hook to manually control loading state
 */
export function useManualLoading() {
  const { setLoading } = useLoading();

  const showLoading = (text: string = 'Loading...') => {
    setLoading(true, text);
  };

  const hideLoading = () => {
    setLoading(false);
  };

  return { showLoading, hideLoading };
}
