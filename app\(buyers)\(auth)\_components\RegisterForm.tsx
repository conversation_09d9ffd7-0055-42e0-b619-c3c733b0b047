"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRegister } from "@/lib/hooks/use-auth";
import { useAuth, useAuthActions } from "@/lib/stores/auth-store";
import { UserRole } from "@/lib/types/auth";

// Zod Validation Schema
const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters long.").nonempty("Name is required."),
  email: z.string().email("Invalid email address").nonempty("Email is required."),
  password: z.string().min(6, "Password should be at least 6 characters long.").nonempty("Password is required."),
  passwordConfirm: z.string().min(6, "Password confirmation is required.").nonempty("Password confirmation is required."),
}).refine((data) => data.password === data.passwordConfirm, {
  message: "Passwords don't match",
  path: ["passwordConfirm"],
});

// Country Code Selector removed - not needed for new API

interface RegisterFormProps {
  role?: UserRole;
}

// Register Form Component
const RegisterForm = ({ role = 'buyer' }: RegisterFormProps) => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      passwordConfirm: "",
    },
  });

  const [showPassword, setShowPassword] = React.useState(false);
  const registerMutation = useRegister();
  const { isLoading, error } = useAuth();
  const { clearError } = useAuthActions();

  // Clear any existing errors when component mounts
  React.useEffect(() => {
    clearError();
  }, [clearError]);

  const onSubmit = (data: any) => {
    const registrationData: any = {
      name: data.name,
      email: data.email,
      password: data.password,
      passwordConfirm: data.passwordConfirm,
      role,
    };

    // Add creator-specific fields if role is creator
    if (role === 'creator') {
      registrationData.bio = data.bio;
      registrationData.location = data.location;
    }

    registerMutation.mutate(registrationData);
  };

  return (
    <div className="md:max-w-lg mx-auto w-11/12  py-4 bg-white rounded-lg space-y-4">

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full">
        {/* Error Display */}
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}

        {/* Name Field */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Full Name</label>
          <div className="mt-2">
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="text"
                  placeholder="John Doe"
                  className="w-full"
                  disabled={isLoading || registerMutation.isPending}
                />
              )}
            />
            {errors.name && <span className="text-red-500 text-sm">{errors.name.message}</span>}
          </div>
        </div>

        {/* Email Field */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Email</label>
          <div className="mt-2">
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="email"
                  placeholder="<EMAIL>"
                  className="w-full"
                  disabled={isLoading || registerMutation.isPending}
                />
              )}
            />
            {errors.email && <span className="text-red-500 text-sm">{errors.email.message}</span>}
          </div>
        </div>

        {/* Password Field with Toggle Visibility */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Password</label>
          <div className="relative mt-3">
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••"
                  className="w-full"
                  disabled={isLoading || registerMutation.isPending}
                />
              )}
            />
            <button
              type="button"
              className="absolute top-1/2 right-2 transform -translate-y-1/2"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading || registerMutation.isPending}
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          {errors.password && <span className="text-red-500 text-sm">{errors.password.message}</span>}
        </div>

        {/* Password Confirmation Field */}
        <div className="w-full">
          <label className="text-sm font-medium text-gray-800">Confirm Password</label>
          <div className="relative mt-3">
            <Controller
              name="passwordConfirm"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••"
                  className="w-full"
                  disabled={isLoading || registerMutation.isPending}
                />
              )}
            />
          </div>
          {errors.passwordConfirm && <span className="text-red-500 text-sm">{errors.passwordConfirm.message}</span>}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          size="lg"
          className="w-full h-12 text-lg"
          disabled={isLoading || registerMutation.isPending}
        >
          {isLoading || registerMutation.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating account...
            </>
          ) : (
            'Register'
          )}
        </Button>
      </form>
    </div>
  );
};

export default RegisterForm;
