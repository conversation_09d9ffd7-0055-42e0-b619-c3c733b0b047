"use client"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { creatorbottombarLinks } from "@/lib/helpers/bottomlinks"

const CreatorsBottomNavigation = () => {
  const pathname = usePathname()
  return (
    <section className="bottombar bg-black">
        <div className="bottombar_container">
        {creatorbottombarLinks.map((link:any)=> {
                const isActive = (pathname.includes(link.route) && link.route.length > 9) || pathname === link.route
                return (<Link href={link.route}
                    key={link.label}
                    className={`bottombar_link ${isActive ? 'text-red-400' : 'text-white'}`}>
                        {typeof link.imgURL === 'function' ? link.imgURL({ isActive }) : link.imgURL}
                        <p className="text-xs">
                            {link.label.split(/\s+/)[0]}
                        </p>
                    </Link>
                )
            })}
        </div>
    </section>
  )
}

export default CreatorsBottomNavigation
