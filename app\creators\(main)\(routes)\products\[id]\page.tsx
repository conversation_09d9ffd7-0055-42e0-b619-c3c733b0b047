"use client";

import React from "react";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Trash2, Package } from "lucide-react";
import { useCreatorProduct, useDeleteProduct } from "@/lib/hooks/use-products";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";
import ProductInfoSection from "./_components/ProductInfoSection";
import ProductImagesSection from "./_components/ProductImagesSection";
import SpecificationsSection from "./_components/SpecificationsSection";
import VariationsSection from "./_components/VariationsSection";
import MetricsCard from "./_components/MetricsCard";

const ProductDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;

  const { data: productResponse, isLoading, error } = useCreatorProduct(productId);
  const deleteProductMutation = useDeleteProduct();

  const product = productResponse?.data?.product;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeleteProduct = async () => {
    if (!product) return;

    if (window.confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
      try {
        await deleteProductMutation.mutateAsync(productId);
        toast({
          title: "Product deleted",
          description: "The product has been successfully deleted.",
        });
        router.push('/creators/products');
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  if (isLoading) {
    return (
      <div className="w-full p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="w-full p-6">
        <div className="text-center py-12">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">Product not found</h3>
          <p className="text-gray-500 mb-6">The product you're looking for doesn't exist or has been removed.</p>
          <Link href="/creators/products">
            <Button>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Products
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6 w-full">
      {/* Product Title and Action Buttons */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-3">
        <div className="flex items-center gap-4">
          <Link href="/creators/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="font-semibold text-gray-700">{product.name}</h1>
            <Badge className={getStatusColor(product.status)}>
              {product.status}
            </Badge>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDeleteProduct}
            disabled={deleteProductMutation.isPending}
            className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-1 text-sm font-poppins transition-colors duration-200"
          >
            <Trash2 className="w-4 h-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Product Metrics */}
      <div className="mb-6">
        <MetricsCard product={product} />
      </div>

      {/* Product Details Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Images and Basic Info */}
        <div className="space-y-6">
          <ProductImagesSection product={product} />
          <ProductInfoSection product={product} />
        </div>

        {/* Right Column - Specifications and Variations */}
        <div className="space-y-6">
          <SpecificationsSection product={product} />
          <VariationsSection product={product} />
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsPage;