'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

interface Order {
  id: string;
  productName: string;
  image: string;
  size: string;
  status: 'DELIVERED' | 'ORDER CONFIRMED' | 'CANCELLED';
  date: string;
  orderNumber: string;
  cancelReason?: string;
}

const orders: Order[] = [
  {
    id: '1',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    size: 'XL',
    status: 'DELIVERED',
    date: '22-Jan-2025',
    orderNumber: '35554455',
  },
  {
    id: '2',
    productName: 'Casual Shirt',
    image: '/images/ladies/skirts.jpg',
    size: 'L',
    status: 'CANCELLED',
    date: '15-Jan-2025',
    orderNumber: '35554456',
    cancelReason: 'PAYMENT UNSUCCESSFUL',
  },
  {
    id: '3',
    productName: 'Smart Shoes',
    image: '/images/men/Shirts_TN.png',
    size: '42',
    status: 'ORDER CONFIRMED',
    date: 'Delivery between Friday, 21st Ferbuary and 22nd February',
    orderNumber: '35554457',
  },
  {
    id: '4',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    size: 'XL',
    status: 'DELIVERED',
    date: '22-Jan-2025',
    orderNumber: '35554455',
  },
  {
    id: '5',
    productName: 'Casual Shirt',
    image: '/images/ladies/skirts.jpg',
    size: 'L',
    status: 'CANCELLED',
    date: '15-Jan-2025',
    orderNumber: '35554456',
    cancelReason: 'PAYMENT UNSUCCESSFUL',
  },
  {
    id: '6',
    productName: 'Smart Shoes',
    image: '/images/men/Shirts_TN.png',
    size: '42',
    status: 'DELIVERED',
    date: '10-Jan-2025',
    orderNumber: '35554457',
  },
  {
    id: '7',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    size: 'XL',
    status: 'DELIVERED',
    date: '22-Jan-2025',
    orderNumber: '35554455',
  },
  {
    id: '8',
    productName: 'Casual Shirt',
    image: '/images/ladies/skirts.jpg',
    size: 'L',
    status: 'CANCELLED',
    date: '15-Jan-2025',
    orderNumber: '35554456',
    cancelReason: 'PAYMENT UNSUCCESSFUL',
  },
  {
    id: '9',
    productName: 'Smart Shoes',
    image: '/images/men/Shirts_TN.png',
    size: '42',
    status: 'DELIVERED',
    date: '10-Jan-2025',
    orderNumber: '35554457',
  },
];

const OrdersPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'OngoingDelivered' | 'Cancelled'>(
    'OngoingDelivered'
  );
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="orders" />;
  }

  const filteredOrders = orders.filter((order) =>
    activeTab === 'OngoingDelivered'
      ? order.status === 'DELIVERED' ||  order.status === 'ORDER CONFIRMED'
      : order.status === 'CANCELLED'
  );

  const handleOrderClick = (orderId: string) => {
    router.push(`/orders/${orderId}`);
  };

  return (
    <section className="w-full mt-20 py-4">
      <h1 className=" font-semibold text-gray-700 mb-2">My Orders</h1>

      {/* Tabs */}
      <div className="flex mb-4 ">
        <button
          className={`py-2 text-sm font-medium flex-1 uppercase ${
            activeTab === 'OngoingDelivered'
              ? 'border-b-2 border-primary text-primary'
              : 'text-gray-600'
          }`}
          onClick={() => setActiveTab('OngoingDelivered')}
        >
          Ongoing / Delivered
        </button>
        <button
          className={`py-2  text-sm px-4 flex-1 font-medium uppercase ${
            activeTab === 'Cancelled'
              ? 'border-b-2 border-primary text-primary'
              : 'text-gray-600'
          }`}
          onClick={() => setActiveTab('Cancelled')}
        >
          Cancelled
        </button>
      </div>

      {/* Orders List */}
      {filteredOrders.length > 0 ? (
        filteredOrders.map((order) => (
          <div
            key={order.id}
            className="bg-white rounded shadow-sm p-3 mb-2 cursor-pointer"
            onClick={() => handleOrderClick(order.id)}
          >
            <div className="flex">
              {/* Left Side - Product Image */}
              <div className="w-24 h-24 bg-gray-200">
                <img
                  src={order.image}
                  alt={order.productName}
                  className="w-full h-full object-cover rounded"
                />
              </div>

              {/* Right Side - Product Details */}
              <div className="ml-4 flex-1">
                <h2 className="font-medium text-gray-800">
                  {order.productName}
                </h2>
                <p className="text-sm text-gray-600">Order {order.orderNumber}</p>
                <p className="text-sm text-gray-600">Size: {order.size}</p>


                { order.status === 'ORDER CONFIRMED' &&(
                  <p className="text-sm font-semibold bg-primary text-primary-foreground px-2 py-1 w-fit rounded-md mt-1">
                    ORDER CONFIRMED
                  </p>
                )}
                { order.status === 'DELIVERED' &&(
                  <p className="text-sm font-semibold bg-green-600 text-white px-2 py-1 w-fit rounded-md mt-1">
                  DELIVERED
                  </p>
                )}
                { order.status === 'CANCELLED' &&(
                  <p className="text-sm font-semibold bg-gray-600 text-white px-2 py-1 w-fit  mt-1">
                  CANCELLED
                </p>
                )}

                <p className="text-sm text-gray-600 mt-1"> {order.date}</p>
              </div>
            </div>
          </div>
        ))
      ) : (
        <p className="text-sm text-gray-500">No orders found in this category.</p>
      )}
    </section>
  );
};

export default OrdersPage;
