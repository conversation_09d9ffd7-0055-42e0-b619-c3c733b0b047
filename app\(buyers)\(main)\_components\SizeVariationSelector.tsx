'use client';

import React from 'react';

type SizeVariationSelectorProps = {
  sizes: string[];
  unavailableSizes: string[];
  openSheet: () => void;
};

const SizeVariationSelector: React.FC<SizeVariationSelectorProps> = ({
  sizes,
  unavailableSizes,
  openSheet,
}) => {

  const handleSizeClick = (size: string) => {
    openSheet()
  };

  return (
    <div className="py-2">
      <div className="flex justify-between px-2 mb-4">
        <h1 className="text-sm font-semibold text-gray-600">VARIATIONS</h1>
        <button className="text-sm text-primary hover:text-primary/80 transition-colors duration-200">Size Guide</button>
      </div>

      <div className="flex gap-2 flex-wrap">
        {sizes.map((size) => (
          <button
            key={size}
            onClick={() => handleSizeClick(size)}
            disabled={unavailableSizes.includes(size)}
            className={`px-2 py-1 rounded border text-sm ${
              unavailableSizes.includes(size)
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-800 hover:bg-primary/10 hover:border-primary/20 transition-colors duration-200'
            }`}
          >
            {size}
          </button>
        ))}
      </div>

      {unavailableSizes.length > 0 && (
        <p className="text-sm text-red-500 mt-2">
          Currently unavailable: {unavailableSizes.join(', ')}
        </p>
      )}
    </div>
  );
};

export default SizeVariationSelector;
