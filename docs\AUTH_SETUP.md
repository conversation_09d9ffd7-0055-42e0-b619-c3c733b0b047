# Authentication Setup Documentation

## Overview

This document outlines the authentication system implemented for the Everyfash platform, supporting both buyers and creators with Zustand for state management and React Query for API integration.

## Architecture

### State Management
- **Zustand Store**: `lib/stores/auth-store.ts`
  - Persistent authentication state
  - Token management
  - User profile management
  - Role-based access control

### API Integration
- **React Query**: `@tanstack/react-query`
  - Mutation-based authentication flows
  - Automatic error handling
  - Loading states
  - Cache management

### Type Safety
- **TypeScript Types**: `lib/types/auth.ts`
  - User interface definitions
  - Authentication credentials
  - API response types
  - Error handling types

## Features Implemented

### ✅ Core Authentication
- [x] Login for buyers and creators
- [x] Registration for buyers and creators
- [x] Phone number verification
- [x] Password reset functionality
- [x] Token-based authentication
- [x] Persistent login sessions

### ✅ State Management
- [x] Zustand store with persistence
- [x] Role-based user management
- [x] Loading and error states
- [x] Automatic token refresh (prepared)

### ✅ UI Components
- [x] Updated login forms with role awareness
- [x] Updated registration forms with role awareness
- [x] Social authentication buttons
- [x] Phone verification page
- [x] Loading states and error handling

### ✅ Security Features
- [x] JWT token management
- [x] Secure token storage
- [x] Role-based route protection
- [x] Auth guards for protected routes

### 🔄 Social Authentication (Prepared)
- [x] Google OAuth integration (UI ready)
- [x] Facebook OAuth integration (UI ready)
- [x] Apple OAuth integration (UI ready)
- [ ] Backend OAuth implementation needed

## File Structure

```
lib/
├── types/
│   └── auth.ts                 # Authentication types
├── stores/
│   └── auth-store.ts          # Zustand auth store
├── api/
│   └── auth.ts                # API service functions
├── hooks/
│   └── use-auth.ts            # React Query auth hooks
├── providers/
│   └── query-provider.tsx     # React Query provider
└── components/
    └── AuthGuard.tsx          # Route protection component

app/
├── (buyers)/(auth)/
│   ├── _components/
│   │   ├── LoginForm.tsx      # Updated login form
│   │   ├── RegisterForm.tsx   # Updated registration form
│   │   └── SocialAuthButtons.tsx # Social auth buttons
│   ├── login/page.tsx         # Buyer login page
│   └── register/page.tsx      # Buyer registration page
├── creators/(auth)/
│   ├── login/page.tsx         # Creator login page
│   └── register/page.tsx      # Creator registration page
└── verify-phone/page.tsx      # Phone verification page
```

## Usage Examples

### Using Auth Store
```typescript
import { useAuth, useAuthActions } from '@/lib/stores/auth-store';

function MyComponent() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const { logout } = useAuthActions();
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please login</div>;
  
  return (
    <div>
      Welcome, {user?.email}!
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Using Auth Hooks
```typescript
import { useLogin, useRegister } from '@/lib/hooks/use-auth';

function LoginForm() {
  const loginMutation = useLogin();
  
  const handleSubmit = (data) => {
    loginMutation.mutate({
      email: data.email,
      password: data.password,
      role: 'buyer'
    });
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
      <button disabled={loginMutation.isPending}>
        {loginMutation.isPending ? 'Logging in...' : 'Login'}
      </button>
    </form>
  );
}
```

### Route Protection
```typescript
import { AuthGuard, BuyerGuard, CreatorGuard } from '@/lib/components/AuthGuard';

// Protect any authenticated route
function ProtectedPage() {
  return (
    <AuthGuard>
      <div>This page requires authentication</div>
    </AuthGuard>
  );
}

// Protect buyer-specific routes
function BuyerDashboard() {
  return (
    <BuyerGuard requireVerified>
      <div>Buyer dashboard content</div>
    </BuyerGuard>
  );
}

// Protect creator-specific routes
function CreatorDashboard() {
  return (
    <CreatorGuard requireVerified>
      <div>Creator dashboard content</div>
    </CreatorGuard>
  );
}
```

## Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
# Required
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# Optional (for social auth)
GOOGLE_CLIENT_ID=your-google-client-id
FACEBOOK_CLIENT_ID=your-facebook-client-id
APPLE_CLIENT_ID=your-apple-client-id
```

## Next Steps

### Backend Integration
1. Implement authentication API endpoints
2. Set up JWT token generation and validation
3. Implement phone verification service
4. Set up social OAuth providers

### Additional Features
1. Password strength validation
2. Two-factor authentication
3. Account recovery flows
4. Session management
5. Audit logging

### Testing
1. Unit tests for auth hooks
2. Integration tests for auth flows
3. E2E tests for complete user journeys

## API Endpoints (Updated for Backend)

The frontend now integrates with these actual API endpoints:

```
POST /api/v1/auth/register/buyer
POST /api/v1/auth/register/creator
POST /api/v1/auth/login
POST /api/v1/auth/logout
GET  /api/v1/auth/profile
PUT  /api/v1/auth/profile
POST /api/v1/auth/reset-password
PUT  /api/v1/auth/change-password
POST /api/v1/auth/google
POST /api/v1/auth/facebook
POST /api/v1/auth/apple
```

### Registration Request Format:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "Test1234",
  "passwordConfirm": "Test1234"
}
```

### Response Format:
```json
{
  "status": "success",
  "token": "jwt-token-here",
  "data": {
    "user": {
      "id": "user-id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "buyer",
      "userType": "Buyer",
      // ... other user fields
    }
  }
}
```

## Troubleshooting

### Common Issues
1. **Hydration errors**: Ensure auth state is properly persisted
2. **Token expiration**: Implement automatic token refresh
3. **Role conflicts**: Check role-based redirects in AuthGuard
4. **Social auth**: Verify OAuth provider configurations

### Debug Mode
Enable React Query devtools in development:
- Open browser dev tools
- Look for React Query tab
- Monitor query states and mutations
