'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { useVerifyEmail } from '@/lib/hooks/use-auth';
import { useAuth } from '@/lib/stores/auth-store';
import FullScreenLoader from '@/components/ui/FullScreenLoader';

const VerifyEmailForm = () => {
  const [token, setToken] = useState<string | null>(null);
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  
  const searchParams = useSearchParams();
  const router = useRouter();
  const verifyEmailMutation = useVerifyEmail();
  const { isLoading, error } = useAuth();

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (!tokenParam) {
      setVerificationStatus('error');
      return;
    }
    
    setToken(tokenParam);
    
    // Automatically verify email when component mounts
    verifyEmailMutation.mutate(tokenParam, {
      onSuccess: () => {
        setVerificationStatus('success');
      },
      onError: () => {
        setVerificationStatus('error');
      },
    });
  }, [searchParams]);

  if (verificationStatus === 'loading' || isLoading || verifyEmailMutation.isPending) {
    return (
      <FullScreenLoader
        title="Verifying Your Email"
        text="Please wait while we verify your email address..."
        size="xl"
      />
    );
  }

  if (verificationStatus === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md px-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Email Verified!</h2>
          <p className="text-gray-600 mb-6">
            Your email has been successfully verified. You're now logged in and ready to explore Everyfash!
          </p>
          <p className="text-sm text-gray-500">
            You'll be redirected to your dashboard in a few seconds...
          </p>
        </div>
      </div>
    );
  }

  // Error state
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center max-w-md px-4">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <XCircle className="w-8 h-8 text-red-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Verification Failed</h2>
        <p className="text-gray-600 mb-6">
          {error || 'The verification link is invalid or has expired. Please try requesting a new verification email.'}
        </p>
        
        <div className="space-y-3">
          <Button 
            onClick={() => router.push('/register')}
            className="w-full"
          >
            Create New Account
          </Button>
          
          <div className="text-center">
            <Link 
              href="/login" 
              className="text-sm text-primary hover:text-primary/80 transition-colors duration-200"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

const VerifyEmailPage = () => {
  return (
    <Suspense fallback={<FullScreenLoader text="Loading..." />}>
      <VerifyEmailForm />
    </Suspense>
  );
};

export default VerifyEmailPage;
