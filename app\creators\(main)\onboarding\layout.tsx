import type { Metada<PERSON> } from "next";
import { Toaster } from "@/components/ui/toaster"
import { CreatorGuard } from "@/lib/components/AuthGuard";

export const metadata: Metadata = {
  title: "Creator Onboarding - Everyfash",
  description: "Complete your creator profile setup",
};

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CreatorGuard>
      <main className="w-full min-h-screen">
        {children}
        <Toaster />
      </main>
    </CreatorGuard>
  );
}
