import React from 'react';
import { fashionCategories } from '@/lib/navlinks';
import ProductList from '@/components/ui/custom/ProductsList';
import FilterSort from '@/app/(buyers)/(main)/_components/FilterSort';


export default async function MainCategoryPage({
  params,
}: {
  params: Promise<{ mainCategory: string }>;
}) {
  const { mainCategory } = await params;

  // Find the main category based on the URL
  const category = fashionCategories.find(cat => cat.headerLink.split('/').pop() === mainCategory);


  if (!category) {
    return <div>Category not found</div>;
  }

  return (
    <section className="w-full">
       <FilterSort />
      <h1 className="text-gray-800 uppercase font-bold mb-4">{category.header}</h1>
      <ProductList/>
    </section>
  );
}
