'use client';

import React from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { CheckCircleIcon } from 'lucide-react';
import { CiCircleMore } from 'react-icons/ci';
import { BsInfoCircle } from 'react-icons/bs';
import { CreditCard, Store, Truck } from 'lucide-react';
import { useOnboardingStatus } from '@/lib/hooks/use-onboarding';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const steps: OnboardingStep[] = [
  {
    id: 'business-info',
    title: 'Business Information',
    description: 'Provide your business details and verification documents',
    icon: <BsInfoCircle />,
  },
  {
    id: 'payment-info',
    title: 'Payment Information',
    description: 'Set up your payment details for receiving payouts',
    icon: <CreditCard />,
  },
  {
    id: 'shop-info',
    title: 'Shop Information',
    description: 'Configure your shop details and branding',
    icon: <Store />,
  },
  {
    id: 'shipping-info',
    title: 'Shipping Information',
    description: 'Set up your shipping and delivery options',
    icon: <Truck />,
  },
];

interface OnboardingLayoutProps {
  children: React.ReactNode;
}

const OnboardingLayout = ({ children }: OnboardingLayoutProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const currentStep = searchParams.get("step") || "business-info";

  const { progress } = useOnboardingStatus();

  const getStepStatus = (stepId: string) => {
    if (!progress) return 'pending';

    switch (stepId) {
      case 'business-info':
        return progress.businessInfo ? 'completed' : 'pending';
      case 'payment-info':
        return progress.paymentInfo ? 'completed' : 'pending';
      case 'shop-info':
        return progress.shopInfo ? 'completed' : 'pending';
      case 'shipping-info':
        return progress.shippingInfo ? 'completed' : 'pending';
      default:
        return 'pending';
    }
  };

  const isCurrentStep = (stepId: string) => stepId === currentStep;

  const navigateToStep = (stepId: string) => {
    const newUrl = `${pathname}?step=${stepId}`;
    router.push(newUrl);
  };

  return (
    <div className="flex flex-col w-full min-h-screen px-2 md:px-10 lg:px-20 pb-6 bg-gray-100">
      {/* Steps Navigation - Matching Profile Page Style */}
      <nav className="w-full bg-white shadow-md rounded-lg border border-gray-200 px-2 py-4 mt-4">
        <h2 className="font-bold text-gray-800 mb-1">
          Welcome to Everyfash! Let's get your shop ready!
        </h2>
        <p className="text-sm text-gray-600 mb-4">
          Complete all these sections to start selling on Africa's largest fashion marketplace.
        </p>

        <ul className="space-y-2">
          {steps.map((step) => {
            const status = getStepStatus(step.id);
            const isCurrent = isCurrentStep(step.id);

            // Determine display status: completed steps show as completed even if current
            const displayStatus = status === 'completed' ? 'completed' : (isCurrent ? 'current' : 'pending');

            return (
              <li
                key={step.id}
                onClick={() => navigateToStep(step.id)}
                className={`flex items-center select-none justify-between p-4 rounded-lg cursor-pointer border ${
                  displayStatus === 'completed'
                    ? "border-green-200 bg-green-50 text-green-700 hover:border-green-300"
                    : displayStatus === 'current'
                    ? "border-primary/40 bg-primary/5 text-primary-700"
                    : "border-gray-200 bg-gray-50 text-gray-700 hover:border-primary/30 hover:bg-primary/5 hover:text-primary-700"
                } transition`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{step.icon}</span>
                  <div>
                    <span className="text-sm font-medium block">{step.title}</span>
                    <span className="text-xs text-gray-500">{step.description}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {displayStatus === 'completed' ? (
                    <>
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <span className="text-xs text-green-600">
                        {isCurrent ? 'Current (Completed)' : 'Completed'}
                      </span>
                    </>
                  ) : displayStatus === 'current' ? (
                    <>
                      <div className="h-5 w-5 border-2 border-primary rounded-full flex items-center justify-center">
                        <div className="h-2 w-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-xs text-primary-600">Current</span>
                    </>
                  ) : (
                    <>
                      <CiCircleMore className="h-5 w-5 text-gray-400" />
                      <span className="text-xs text-gray-500">Pending</span>
                    </>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Main Content */}
      <main className="w-full">
        {children}
      </main>
    </div>
  );
};

export default OnboardingLayout;
