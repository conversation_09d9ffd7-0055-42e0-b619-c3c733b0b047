"use client";
import React, { useState } from "react";
import { BsChevronDown, BsChevronUp } from "react-icons/bs";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import PaginationComponent from "../../_components/PaginationComponent";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FaGlobeAmericas, FaMapMarkerAlt, FaPhoneAlt } from "react-icons/fa";
import CustomSearch from "@/components/ui/custom/CustomSearchBar";
import FilterOrdersDrawer from "./_components/FiltersComponent";
import { sampleOrders } from "@/lib/helpers/orders";
import OrderStatusCell from "./_components/OrderStatusCell";
import CustomTabsTrigger from "@/components/ui/custom/CustomTabsTrigger";
import VerificationGuard from "@/lib/components/VerificationGuard";


const OrdersPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [openOrder, setOpenOrder] = useState<string | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState("");
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null);
  const [action, setAction] = useState<string | null>(null);
  const totalPages = 5;
  const [canceledOrders, setCanceledOrders] = useState<Record<string, boolean>>({});

  // Function to confirm cancellation after modal input
const confirmCancelOrder = () => {
  if (cancelReason.trim()) {
    setCanceledOrders((prev) => ({ ...prev, [currentOrderId as string]: true }));
    setShowCancelModal(false);
    setCancelReason("");
    setCurrentOrderId(null);
  }
};

  const filteredOrders = sampleOrders.filter((order) => {
    if (activeTab === "pending") return order.status === "Pending";
    if (activeTab === "processing") return order.status === "Processing";
    if (activeTab === "shipped") return order.status === "Shipped";
    if (activeTab === "delivered") return order.status === "Delivered";
    if (activeTab === "canceled") return order.status === "Canceled";
    return order;
  });

  const toggleOrderDetails = (orderId: string) => {
    setOpenOrder(openOrder === orderId ? null : orderId);
  };




  const handleStatusChange = (orderId: string, newStatus: string) => {
      setAction(newStatus);
  };


  const handleCancelOrder = (orderId: string) => {
    setShowCancelModal(true);
    setCurrentOrderId(orderId);
  };

    const getStatusClass = (status: string) => {
      switch (status.toLowerCase()) {
        case "pending":
          return "text-yellow-500 font-medium"; // Yellow for Pending
        case "shipped":
          return "text-green-600 font-medium"; // Green for shiped
        case "delivered":
          return "text-green-600 font-medium"; // Green for Completed
        case "canceled":
          return "text-red-500 font-medium"; // Red for Cancelled
        case "processing":
          return "text-primary font-medium"; // Primary for Processing
        case "failed":
          return "text-gray-500 font-medium"; // Gray for Failed
        default:
          return "text-gray-700"; // Default fallback color
      }
    };


  return (
    <VerificationGuard feature="orders">
      <section className="w-full max-w-4xl mx-auto py-4">
      <h1 className="text-sm font-bold uppercase text-gray-600 mb-4">Manage Orders</h1>

        {/* Search & Filters */}
        <section className="w-full flex items-center gap-2 mb-8">
        <div className="flex-1">
          <CustomSearch placeholder="Search for orders..." />
        </div>
        <div className="h-full">
         <FilterOrdersDrawer />
        </div>
      </section>

      <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)} className="mb-6">
        <TabsList className="bg-gray-100  w-[98vw] overflow-x-scroll flex space-x-3 mb-2 p-2 justify-around">
          <CustomTabsTrigger value="all" >All</CustomTabsTrigger>
          <CustomTabsTrigger value="pending" >Pending</CustomTabsTrigger>
          <CustomTabsTrigger value="processing" >Processing</CustomTabsTrigger>
          <CustomTabsTrigger value="shipped" >Shipped</CustomTabsTrigger>
          <CustomTabsTrigger value="delivered" >Delivered</CustomTabsTrigger>
          <CustomTabsTrigger value="canceled" >Canceled</CustomTabsTrigger>
        </TabsList>
      </Tabs>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <Table className="">
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead >Order Date</TableHead>
              <TableHead className="text-center">Actions</TableHead>
              <TableHead className="text-center"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <React.Fragment key={order.id}>
                  <TableRow className="cursor-pointer" >
                    <TableCell  onClick={() => toggleOrderDetails(order.id)} >{order.id}</TableCell>
                    <TableCell  onClick={() => toggleOrderDetails(order.id)}>{order.total}</TableCell>
                    <TableCell className={getStatusClass(order.status)}>
                      {order.status}
                    </TableCell>
                    <TableCell className="w-auto whitespace-nowrap">{order.date}</TableCell>
                    <OrderStatusCell
                        order={order}
                        onStatusChange={handleStatusChange}
                        onCancelOrder={handleCancelOrder}
                      isCanceled={canceledOrders[order.id] || false}
                      />

                    <TableCell className="text-center">
                      {/* Toggle button for order details */}
                      <button
                        onClick={() => toggleOrderDetails(order.id)}
                        className="p-2 rounded-full hover:bg-gray-200 transition"
                      >
                        {openOrder === order.id ? (
                          <BsChevronUp size={16} className="text-gray-500" />
                        ) : (
                          <BsChevronDown size={16} className="text-gray-500" />
                        )}
                      </button>
                    </TableCell>
                  </TableRow>

                  {openOrder === order.id && (
                    <TableRow className="bg-gray-50">
                      <TableCell colSpan={4}>
                        <div className="p-4 bg-white rounded shadow-sm ">
                          <h3 className="text-sm font-semibold mb-2">Order Products</h3>
                          <ul className="space-y-4">
                            {order.products.map((product, index) => (
                              <li key={index} className="flex items-center space-x-4">
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="w-12 h-12 rounded object-cover"
                                />
                                <div className="flex-1">
                                  <p className="text-sm font-medium">{product.name}</p>
                                  <p className="text-xs text-gray-500">Size: {product.size}</p>
                                </div>
                                <p className="text-sm">x{product.quantity}</p>
                                <p className="text-sm">{product.price}</p>
                              </li>
                            ))}
                          </ul>


                          {order.deliveryType === "shop" && order.address && (
                          <div className="mt-6   space-y-2">
                            <h4 className="text-sm font-semibold">Customer Address</h4>
                            <div className="flex items-center space-x-2 text-gray-700">
                              <span>{order.customer}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-gray-700">
                              <FaMapMarkerAlt />
                              <span>{order.address.street}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-gray-700">
                              <FaGlobeAmericas />
                              <span>{order.address.region}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-gray-700">
                              <FaPhoneAlt />
                              <span>{order.address.phone}</span>
                            </div>
                          </div>
                        )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}


                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4">
                  No orders found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <Dialog open={showCancelModal} onOpenChange={setShowCancelModal}>
        <DialogContent className="max-w-md w-[90%] rounded-lg p-6 sm:p-8">
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
          </DialogHeader>
          <Textarea
            placeholder="Provide a reason for cancellation"
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
          />
          <DialogFooter>
            <Button onClick={confirmCancelOrder}>Confirm Cancellation</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <PaginationComponent totalPages={totalPages} />
      </section>
    </VerificationGuard>
  );
};

export default OrdersPage;
