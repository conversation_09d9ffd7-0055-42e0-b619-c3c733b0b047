'use client';

import { useState } from 'react';
import citiesData from '@/lib/helpers/cities.json';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

export default function AddNewAddressPage() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [additionalPhoneNumber, setAdditionalPhoneNumber] = useState('');
  const [address, setAddress] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [region, setRegion] = useState('');
  const [city, setCity] = useState('');
  const [setAsDefault, setSetAsDefault] = useState(false);

  const handleRegionChange = (value: string) => {
    setRegion(value);
    setCity(''); // Reset city when region changes
  };

  const handleCityChange = (value: string) => {
    setCity(value);
  };

  const handleSubmit = () => {
    const formData = {
      firstName,
      lastName,
      phoneNumber,
      additionalPhoneNumber,
      address,
      additionalInfo,
      region,
      city,
      setAsDefault,
    };
    console.log(formData);
    // Implement actual submit logic (e.g., API call)
  };

  return (
    <section className='w-full mt-20'>
      <h2 className="text-sm uppercase font-bold text-gray-600 mb-3">Add New Address</h2>

      <section className="w-full bg-white p-4 rounded shadow-sm ">
        <div className="space-y-4">
          {/* First Name */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">First Name</label>
            <Input
              placeholder="Enter your first name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          </div>

          {/* Last Name */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Last Name</label>
            <Input
              placeholder="Enter your last name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
            />
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Phone Number</label>
            <Input
              placeholder="Enter your phone number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </div>

          {/* Additional Phone Number */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Additional Phone Number</label>
            <Input
              placeholder="Enter an additional phone number (optional)"
              value={additionalPhoneNumber}
              onChange={(e) => setAdditionalPhoneNumber(e.target.value)}
            />
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Address</label>
            <Input
              placeholder="Enter your address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
          </div>

          {/* Additional Information */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Additional Information</label>
            <Input
              placeholder="Additional info (optional)"
              value={additionalInfo}
              onChange={(e) => setAdditionalInfo(e.target.value)}
            />
          </div>

          {/* Region Selection */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">Region</label>
            <Select onValueChange={handleRegionChange} value={region}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="-- Choose Region --" />
              </SelectTrigger>
              <SelectContent>
                {Object.keys(citiesData).map((reg) => (
                  <SelectItem key={reg} value={reg}>
                    {reg}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* City Selection */}
          <div>
            <label className="block text-sm text-gray-700 mb-1">City</label>
            <Select onValueChange={handleCityChange} value={city} disabled={!region}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="-- Choose City --" />
              </SelectTrigger>
              <SelectContent>
                {region &&
                  citiesData[region as keyof typeof citiesData].map((ct) => (
                    <SelectItem key={ct} value={ct}>
                      {ct}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          {/* Set as Default Address */}
          <div className="flex items-center gap-2">
            <Checkbox
              id="set-as-default"
              checked={setAsDefault}
              onCheckedChange={(checked) => setSetAsDefault(checked === true)}
            />
            <label htmlFor="set-as-default" className="text-sm text-gray-700">
              Set as default address
            </label>
          </div>

          {/* Save Button */}
          <Button className="w-full bg-primary hover:bg-primary/90 text-white" onClick={handleSubmit}>
            Save Address
          </Button>
        </div>
      </section>
    </section>
  );
}
