'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

export default function ProfileUpdatePage() {
  const { toast } = useToast();

  const [firstName, setFirstName] = useState('John');
  const [middleName, setMiddleName] = useState('');
  const [lastName, setLastName] = useState('Doe');
  const [email, setEmail] = useState('<EMAIL>');
  const [gender, setGender] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('0241234567');

  const handleSaveProfile = async () => {
    console.log({
      firstName,
      middleName,
      lastName,
      email,
      gender,
      birthDate,
      phoneNumber,
    });

    toast({
      title: 'Success!',
      description: 'Profile updated successfully!',
      className: 'bg-green-100 text-green-800',
    });
  };

  return (
    <section className="w-full mt-20 space-y-6">
      <h2 className="text-sm uppercase font-bold text-gray-600 mb-1">Edit Profile</h2>

      <div className="bg-white px-2 py-4 mt-0 rounded shadow-sm space-y-4">
        {/* First Name */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">First Name</label>
          <Input
            placeholder="Enter your first name"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
          />
        </div>

        {/* Middle Name */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Middle Name</label>
          <Input
            placeholder="Enter your middle name (optional)"
            value={middleName}
            onChange={(e) => setMiddleName(e.target.value)}
          />
        </div>

        {/* Last Name */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Last Name</label>
          <Input
            placeholder="Enter your last name"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
          />
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Email</label>
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>

        {/* Gender */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Gender</label>
          <Select value={gender} onValueChange={setGender}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select your gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Birth Date */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Birth Date</label>
          <Input
            type="date"
            value={birthDate}
            onChange={(e) => setBirthDate(e.target.value)}
          />
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Phone Number</label>
          <Input
            placeholder="Enter your phone number"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
        </div>

        {/* Save Button */}
        <Button className="w-full bg-primary hover:bg-primary/90 text-white" onClick={handleSaveProfile}>
          Save Changes
        </Button>
      </div>
    </section>
  );
}
