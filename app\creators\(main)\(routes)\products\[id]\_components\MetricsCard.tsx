"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, ShoppingCart, Heart, Star, TrendingUp, Package, DollarSign, BarChart3, AlertTriangle } from "lucide-react";

interface MetricsCardProps {
  product: any;
}

const MetricsCard: React.FC<MetricsCardProps> = ({ product }) => {
  // Calculate metrics from actual product data
  const totalStock = product.totalStock || 0;
  const soldQuantity = product.sold || 0;
  const ratingsAverage = product.ratingsAverage || 0;
  const ratingsQuantity = product.ratingsQuantity || 0;

  // Calculate price range
  const minPrice = product.normalMinPrice || product.basePrice || 0;
  const maxPrice = product.normalMaxPrice || product.basePrice || 0;
  const hasDiscount = product.hasAnyDiscount || false;
  const maxDiscountPercentage = product.maxDiscountPercentage || 0;

  // Calculate stock status
  const stockStatus = totalStock === 0 ? 'Out of Stock' :
                     totalStock <= 5 ? 'Low Stock' : 'In Stock';
  const stockColor = totalStock === 0 ? 'text-red-600' :
                    totalStock <= 5 ? 'text-yellow-600' : 'text-green-600';
  const stockBgColor = totalStock === 0 ? 'bg-red-50' :
                      totalStock <= 5 ? 'bg-yellow-50' : 'bg-green-50';

  // Calculate available variations
  const availableColors = product.availableColors?.length || 0;
  const availableSizes = product.availableSizes?.length || 0;

  const metrics = [
    {
      label: "Total Stock",
      value: totalStock.toLocaleString(),
      icon: Package,
      color: stockColor,
      bgColor: stockBgColor,
      subtitle: stockStatus,
    },
    {
      label: "Units Sold",
      value: soldQuantity.toLocaleString(),
      icon: ShoppingCart,
      color: "text-green-600",
      bgColor: "bg-green-50",
      subtitle: "All time",
    },
    {
      label: "Customer Rating",
      value: ratingsAverage > 0 ? `${ratingsAverage.toFixed(1)}/5` : "No ratings",
      icon: Star,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      subtitle: `${ratingsQuantity} reviews`,
    },
    {
      label: "Price Range",
      value: minPrice === maxPrice ? `GHS ${minPrice}` : `GHS ${minPrice} - ${maxPrice}`,
      icon: DollarSign,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      subtitle: hasDiscount ? `${maxDiscountPercentage}% max discount` : "Regular pricing",
    },
    {
      label: "Color Options",
      value: availableColors.toString(),
      icon: BarChart3,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      subtitle: "Available colors",
    },
    {
      label: "Size Options",
      value: availableSizes.toString(),
      icon: BarChart3,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      subtitle: "Available sizes",
    },
  ];

  return (
    <Card className="bg-white shadow-sm border border-gray-100">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Product Metrics</h3>
          <div className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {metrics.map((metric, index) => {
            const IconComponent = metric.icon;
            return (
              <div key={index} className="flex flex-col items-center text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow">
                <div className={`p-3 rounded-full ${metric.bgColor} mb-3`}>
                  <IconComponent className={`w-6 h-6 ${metric.color}`} />
                </div>
                <div className="space-y-1">
                  <p className="text-xl font-bold text-gray-900">{metric.value}</p>
                  <p className="text-sm font-medium text-gray-700">{metric.label}</p>
                  <p className="text-xs text-gray-500">{metric.subtitle}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Product Status and Insights */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-1">Product Status</p>
              <div className="flex items-center justify-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  product.status === 'approved' ? 'bg-green-500' :
                  product.status === 'pending' ? 'bg-yellow-500' :
                  product.status === 'rejected' ? 'bg-red-500' : 'bg-gray-500'
                }`}></div>
                <p className={`font-semibold capitalize ${
                  product.status === 'approved' ? 'text-green-700' :
                  product.status === 'pending' ? 'text-yellow-700' :
                  product.status === 'rejected' ? 'text-red-700' : 'text-gray-700'
                }`}>
                  {product.status || 'Draft'}
                </p>
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-1">Featured Product</p>
              <div className="flex items-center justify-center gap-2">
                <div className={`w-2 h-2 rounded-full ${product.featured ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
                <p className={`font-semibold ${product.featured ? 'text-blue-700' : 'text-gray-600'}`}>
                  {product.featured ? 'Featured' : 'Standard'}
                </p>
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-1">Visibility</p>
              <div className="flex items-center justify-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  product.status === 'approved' ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
                <p className={`font-semibold ${
                  product.status === 'approved' ? 'text-green-700' : 'text-gray-600'
                }`}>
                  {product.status === 'approved' ? 'Public' : 'Private'}
                </p>
              </div>
            </div>
          </div>

          {/* Stock Alert */}
          {totalStock <= 5 && totalStock > 0 && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <p className="text-sm text-yellow-800">
                <span className="font-medium">Low Stock Alert:</span> Only {totalStock} units remaining
              </p>
            </div>
          )}

          {totalStock === 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <p className="text-sm text-red-800">
                <span className="font-medium">Out of Stock:</span> This product is currently unavailable
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricsCard;
