'use client';

import { FaShippingFast, FaBoxOpen, FaStar, FaChevronRight } from 'react-icons/fa';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import FilterSort from '@/app/(buyers)/(main)/_components/FilterSort'; 
import ProductList from '@/components/ui/custom/ProductsList';
import Link from 'next/link';

interface Performance {
  shipping: string;
  quality: string;
  customerRating: string;
}

interface ShopDetails {
  id: string,
  shopName: string;
  sellerScore: string;
  numFollowers: number;
}

const ShopDetailsPage: React.FC = () => {
  const shopDetails: ShopDetails = {
    id: '25',
    shopName: 'The Thrift Hub Shop',
    sellerScore: '95%',
    numFollowers: 2400,
  };

  const { id,shopName, sellerScore, numFollowers } = shopDetails;

  return (
    <div className="bg-gray-100 w-full mt-16 py-4 ">
      {/* Shop Info Card */}
      <section className="bg-white py-3 px-3 rounded shadow-sm mb-4">
        {/* Shop Name & Navigation */}
        <div className="flex justify-between items-center border-b pb-2">
          <h1 className="text-sm font-semibold text-gray-700">{shopName}</h1>
          <Link href={`/shops/${id}/profile`}>
            <FaChevronRight className="h-4 w-4 text-gray-500" />
          </Link>
        </div>

        {/* Seller Score & Followers */}
        <div className="mt-3 flex justify-between items-center text-sm">
          <div className="text-gray-600">
            <p>
              <span className="font-medium text-gray-700 mr-1">{sellerScore}</span>
              Seller Score
            </p>
            <p>
              <span className="font-medium text-gray-700 mr-1">{numFollowers}</span>
              Followers
            </p>
          </div>
          <Button className="text-xs bg-primary hover:bg-primary/90 text-white px-3 py-1 rounded">
            Follow
          </Button>
        </div>

      </section>

      {/* Products Section */}
      <section className="w-full">
        <FilterSort />
        <ProductList />
      </section>
    </div>
  );
};

export default ShopDetailsPage;
