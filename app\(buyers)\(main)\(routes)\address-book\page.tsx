'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2, Pencil, Plus } from 'lucide-react';
import Link from 'next/link';
import { Dialog, DialogTrigger, DialogContent, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

interface Address {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  additionalPhoneNumber?: string;
  address: string;
  additionalInfo?: string;
  region: string;
  city: string;
  isDefault: boolean;
}

const initialAddresses: Address[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    phoneNumber: '0241234567',
    additionalPhoneNumber: '0209876543',
    address: '123 Main Street',
    additionalInfo: 'Near the gas station',
    region: 'Greater Accra',
    city: 'Accra',
    isDefault: true,
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    phoneNumber: '**********',
    address: '456 Elm Street',
    region: 'Ashanti',
    city: 'Kumasi',
    isDefault: false,
  },
];

export default function AddressBookPage() {
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="account" title="Manage Your Addresses" description="Keep your delivery addresses organized and never miss a package. Add multiple addresses, set defaults, and enjoy seamless checkout experiences!" />;
  }

  const [addresses, setAddresses] = useState<Address[]>(initialAddresses);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);
  const { toast } = useToast()

  const handleMakeDefault = (id: string) => {
    setAddresses((prevAddresses) =>
      prevAddresses.map((address) => ({
        ...address,
        isDefault: address.id === id,
      }))
    );
    toast({
      description: 'Default address updated successfully.',
    });
  };

  const handleDelete = () => {
    if (!addressToDelete) return;
    setAddresses((prevAddresses) =>
      prevAddresses.filter((address) => address.id !== addressToDelete)
    );
    setDeleteDialogOpen(false);
    setAddressToDelete(null);

    toast({
      description: 'Address deleted successfully.',
    });
  };

  const confirmDelete = (id: string) => {
    setAddressToDelete(id);
    setDeleteDialogOpen(true);
  };

  return (
    <section className="w-full mt-20">
      <h2 className="text-sm uppercase font-bold text-gray-600 mb-3">Address Book</h2>

      <div className="space-y-4">
        {addresses.map((address) => (
          <div key={address.id} className="bg-white p-4 rounded shadow-sm border relative space-y-2">
            <p className="font-medium text-gray-800">{`${address.firstName} ${address.lastName}`}</p>
            <p className="text-sm text-gray-600">{address.address}</p>
            {address.additionalInfo && <p className="text-sm text-gray-500">{address.additionalInfo}</p>}
            <p className="text-sm text-gray-600">{`${address.city}, ${address.region}`}</p>
            <p className="text-sm text-gray-600">
              {address.phoneNumber}
              {address.additionalPhoneNumber && ` / ${address.additionalPhoneNumber}`}
            </p>

            {address.isDefault && (
            <h1 className="px-2 py-1 rounded w-fit bg-green-100 text-green-700">
              DEFAULT ADDRESS
            </h1>
            )}



            {/* Actions */}
            <div className="flex items-center justify-between pt-2  mt-2">
              <Button
                variant="link"
                className={`text-sm pl-0 font-medium ${
                  address.isDefault
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-primary hover:text-primary/80 hover:underline transition-colors duration-200'
                }`}
                disabled={address.isDefault}
                onClick={() => handleMakeDefault(address.id)}
              >
                Make Default
              </Button>

              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-600 hover:text-red-600"
                  onClick={() => confirmDelete(address.id)}
                >
                  <Trash2 size={18} />
                </Button>
                <Link href={`/address-book/${address.id}/edit`}>
                  <Button variant="ghost" size="icon" className="text-gray-600 hover:text-primary transition-colors duration-200">
                    <Pencil size={18} />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ))}

        {/* Add New Address */}
        <div className="mt-6">
          <Link href="/address-book/add">
            <Button className="w-full bg-primary hover:bg-primary/90 text-white flex items-center gap-2">
              <Plus size={16} />
              Add New Address
            </Button>
          </Link>
        </div>
      </div>

    {/* Delete Confirmation Dialog */}
    <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
    <DialogContent className="max-w-md w-[90%] rounded-lg p-6 sm:p-8">
        <DialogTitle className=" font-medium">Confirm Deletion</DialogTitle>
        <p className="text-sm text-gray-600">
        Are you sure you want to delete this address? This action cannot be undone.
        </p>
        <DialogFooter className="flex flex-col sm:flex-row sm:justify-end gap-3 mt-4">
        <Button
            variant="outline"
            onClick={() => setDeleteDialogOpen(false)}
            className="w-full sm:w-auto"
        >
            Cancel
        </Button>
        <Button
            variant="destructive"
            onClick={handleDelete}
            className="w-full sm:w-auto"
        >
            Delete
        </Button>
        </DialogFooter>
    </DialogContent>
    </Dialog>

    </section>
  );
}
