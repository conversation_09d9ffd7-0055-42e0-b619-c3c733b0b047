import React from 'react';
import { FaClipboard, FaRegCommentDots } from 'react-icons/fa';
import { IoMailOutline, IoHeartOutline } from 'react-icons/io5';

// Icon mapping for navigation links
export const getIconByName = (iconName: string, size: number = 20): React.ReactNode => {
  const iconMap: Record<string, React.ComponentType<{ size?: number }>> = {
    FaClipboard,
    FaRegCommentDots,
    IoMailOutline,
    IoHeartOutline,
  };

  const IconComponent = iconMap[iconName];
  
  if (!IconComponent) {
    return null;
  }

  return <IconComponent size={size} />;
};
