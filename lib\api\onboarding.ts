import {
  BusinessInfoResponse,
  BusinessInfoUpdateData,
  OnboardingStatusResponse,
  PaymentInfoResponse,
  PaymentInfoUpdateData,
  ShopInfoResponse,
  ShopInfoUpdateData,
  ShippingInfoResponse,
  ShippingInfoUpdateData,
  VerificationStatusResponse
} from '@/lib/types/auth';
import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';


// Create API client instance
const onboardingApiClient = new BaseApiClient();

// Creator Onboarding API functions
export const onboardingApi = {
  // Get onboarding status
  getOnboardingStatus: async (): Promise<OnboardingStatusResponse> => {
    return onboardingApiClient.get<OnboardingStatusResponse>(API_ENDPOINTS.ONBOARDING.STATUS);
  },

  // Get business information
  getBusinessInfo: async (): Promise<BusinessInfoResponse> => {
    return onboardingApiClient.get<BusinessInfoResponse>(API_ENDPOINTS.ONBOARDING.BUSINESS_INFO);
  },

  // Update business information
  updateBusinessInfo: async (data: BusinessInfoUpdateData): Promise<BusinessInfoResponse> => {
    console.log('Updating business info:', data);

    // Create FormData for file upload
    const formData = new FormData();

    // Add basic fields
    formData.append('businessName', data.businessName);
    formData.append('businessType', data.businessType);
    formData.append('ownerName', data.ownerName);
    formData.append('ownerID', data.ownerID);
    formData.append('phoneNumber', data.phoneNumber);
    formData.append('taxId', data.taxId);

    // Add address fields
    formData.append('businessAddress[addressLine1]', data.businessAddress.addressLine1);
    if (data.businessAddress.addressLine2) {
      formData.append('businessAddress[addressLine2]', data.businessAddress.addressLine2);
    }
    formData.append('businessAddress[city]', data.businessAddress.city);
    formData.append('businessAddress[state]', data.businessAddress.state);
    formData.append('businessAddress[country]', data.businessAddress.country);
    if (data.businessAddress.digitalGps) {
      formData.append('businessAddress[digitalGps]', data.businessAddress.digitalGps);
    }

    // Add verification documents (mix of new Files and existing URLs to keep)
    if (data.verificationDocuments && data.verificationDocuments.length > 0) {
      let newFileCount = 0;
      let existingUrlCount = 0;

      data.verificationDocuments.forEach((item) => {
        if (item instanceof File) {
          // New file to upload
          formData.append('verificationDocuments', item);
          newFileCount++;
        } else if (typeof item === 'string') {
          // Existing document URL to keep - send as string in verificationDocuments
          formData.append('verificationDocuments', item);
          existingUrlCount++;
        }
      });

      console.log(`Verification documents: ${newFileCount} new files, ${existingUrlCount} existing documents to keep`);
    }

    // Debug: Log FormData contents
    console.log('Business Info FormData entries:');
    for (const [key, value] of formData.entries()) {
      console.log(`${key}:`, value instanceof File ? `File: ${value.name}` : value);
    }

    // Use patchFormData with default config (browser sets Content-Type automatically)
    return onboardingApiClient.patchFormData<BusinessInfoResponse>(API_ENDPOINTS.ONBOARDING.BUSINESS_INFO, formData,{headers:{'Content-Type':'multipart/form-data'}});
  },

  // Get payment information
  getPaymentInfo: async (): Promise<PaymentInfoResponse> => {
    return onboardingApiClient.get<PaymentInfoResponse>(API_ENDPOINTS.ONBOARDING.PAYMENT_INFO);
  },

  // Update payment information
  updatePaymentInfo: async (data: PaymentInfoUpdateData): Promise<PaymentInfoResponse> => {
    console.log('Updating payment info:', data);
    return onboardingApiClient.patch<PaymentInfoResponse>(API_ENDPOINTS.ONBOARDING.PAYMENT_INFO, data);
  },

  // Get shop information
  getShopInfo: async (): Promise<ShopInfoResponse> => {
    return onboardingApiClient.get<ShopInfoResponse>(API_ENDPOINTS.ONBOARDING.SHOP_INFO);
  },

  // Update shop information
  updateShopInfo: async (data: ShopInfoUpdateData): Promise<ShopInfoResponse> => {
    console.log('Updating shop info:', data);

    // Create FormData for file upload (following business info pattern exactly)
    const formData = new FormData();

    // Add required fields (per API docs)
    formData.append('name', data.name);
    formData.append('sellerType', data.sellerType);

    // Add required contact information
    formData.append('contact[name]', data.contact.name);
    formData.append('contact[email]', data.contact.email);
    formData.append('contact[phone]', data.contact.phone);

    // Add optional description
    if (data.description) {
      formData.append('description', data.description);
    }



    // Add optional social media
    if (data.socialMedia?.instagram) {
      formData.append('socialMedia[instagram]', data.socialMedia.instagram);
    }
    if (data.socialMedia?.facebook) {
      formData.append('socialMedia[facebook]', data.socialMedia.facebook);
    }
    if (data.socialMedia?.twitter) {
      formData.append('socialMedia[twitter]', data.socialMedia.twitter);
    }
    if (data.socialMedia?.tiktok) {
      formData.append('socialMedia[tiktok]', data.socialMedia.tiktok);
    }
    if (data.socialMedia?.youtube) {
      formData.append('socialMedia[youtube]', data.socialMedia.youtube);
    }
    if (data.socialMedia?.website) {
      formData.append('socialMedia[website]', data.socialMedia.website);
    }



    // Add optional customer care
    if (data.customerCare?.name) {
      formData.append('customerCare[name]', data.customerCare.name);
    }
    if (data.customerCare?.email) {
      formData.append('customerCare[email]', data.customerCare.email);
    }
    if (data.customerCare?.phone) {
      formData.append('customerCare[phone]', data.customerCare.phone);
    }
    if (data.customerCare?.addressLine1) {
      formData.append('customerCare[addressLine1]', data.customerCare.addressLine1);
    }
    if (data.customerCare?.addressLine2) {
      formData.append('customerCare[addressLine2]', data.customerCare.addressLine2);
    }
    if (data.customerCare?.city) {
      formData.append('customerCare[city]', data.customerCare.city);
    }
    if (data.customerCare?.region) {
      formData.append('customerCare[region]', data.customerCare.region);
    }
    if (data.customerCare?.country) {
      formData.append('customerCare[country]', data.customerCare.country);
    }
    if (data.customerCare?.hours) {
      formData.append('customerCare[hours]', data.customerCare.hours);
    }
    if (data.customerCare?.supportWebsite) {
      formData.append('customerCare[supportWebsite]', data.customerCare.supportWebsite);
    }

    // Add file uploads if provided
    if (data.logo) {
      formData.append('logo', data.logo);
    }
    if (data.banner) {
      formData.append('banner', data.banner);
    }

    // Debug: Log FormData contents
    console.log('Shop Info FormData entries:');
    for (const [key, value] of formData.entries()) {
      console.log(`${key}:`, value instanceof File ? `File: ${value.name}` : value);
    }

    // Use patchFormData with default config (browser sets Content-Type automatically)
    return onboardingApiClient.patchFormData<ShopInfoResponse>(API_ENDPOINTS.ONBOARDING.SHOP_INFO, formData);
  },

  // Get shipping information
  getShippingInfo: async (): Promise<ShippingInfoResponse> => {
    return onboardingApiClient.get<ShippingInfoResponse>(API_ENDPOINTS.ONBOARDING.SHIPPING_INFO);
  },

  // Update shipping information
  updateShippingInfo: async (data: ShippingInfoUpdateData): Promise<ShippingInfoResponse> => {
    console.log('Updating shipping info:', data);
    return onboardingApiClient.patch<ShippingInfoResponse>(API_ENDPOINTS.ONBOARDING.SHIPPING_INFO, data);
  },

  // Get verification status
  getVerificationStatus: async (): Promise<VerificationStatusResponse> => {
    return onboardingApiClient.get<VerificationStatusResponse>(API_ENDPOINTS.ONBOARDING.VERIFICATION_STATUS);
  },
};

// Error handling utility (for backward compatibility)
export const handleOnboardingApiError = (error: unknown): { message: string } => {
  if (error instanceof Error) {
    return {
      message: error.message,
    };
  }
  return {
    message: 'An unexpected error occurred',
  };
};
