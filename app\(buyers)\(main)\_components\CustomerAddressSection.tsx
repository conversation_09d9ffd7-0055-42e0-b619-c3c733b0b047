import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FaPlus, FaEdit } from 'react-icons/fa';
import Link from 'next/link';

interface Address {
  id: string;
  firstName: string;
  lastName: string;
  address: string;
  additionalInfo?: string;
  city: string;
  region: string;
  phoneNumber: string;
  additionalPhoneNumber?: string;
  isDefault?: boolean;
}

const CustomerAddressSection = () => {
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: '1',
      firstName: '<PERSON>',
      lastName: 'Doe',
      address: 'No. 45 Spintex Road',
      city: 'Accra',
      region: 'Greater Accra',
      phoneNumber: '0244 123 456',
      additionalPhoneNumber: '020 987 654',
      isDefault: true,
    },
    {
      id: '2',
      firstName: 'Jane',
      lastName: 'Smith',
      address: '14th Floor, Silver Star Towers',
      city: 'Accra',
      region: 'Greater Accra',
      phoneNumber: '0244 654 321',
    },
  ]);

  const [selectedAddressId, setSelectedAddressId] = useState(
    addresses.find((addr) => addr.isDefault)?.id || addresses[0]?.id
  );

  return (
    <section className="w-full">
        <h2 className="font-medium text-gray-700 mb-1">CUSTOMER ADDRESS</h2>
        <section className="bg-white py-4 px-2 mb-4 rounded-lg shadow-sm">

        {addresses.length === 0 ? (
            <div className="text-center py-4">
            <p className="text-sm text-gray-600">No saved addresses yet.</p>
            <Link href="/address-book/add" passHref>
                <Button className="mt-3 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-200">
                <FaPlus className="mr-2" />
                Add New Address
                </Button>
            </Link>
            </div>
        ) : (
            <div className="space-y-4">
            {addresses.map((address) => (
                <label
                key={address.id}
                className={`block p-3 border rounded-lg cursor-pointer transition ${
                    selectedAddressId === address.id
                    ? 'border-primary/20 bg-primary-50'
                    : 'border-gray-200 bg-white hover:border-primary/20'
                }`}
                >
                <div className="flex justify-between">
                    <div className="space-y-1">
                    <p className="font-medium text-gray-800">{`${address.firstName} ${address.lastName}`}</p>
                    <p className="text-sm text-gray-600">{address.address}</p>
                    {address.additionalInfo && (
                        <p className="text-sm text-gray-500">{address.additionalInfo}</p>
                    )}
                    <p className="text-sm text-gray-600">{`${address.city}, ${address.region}`}</p>
                    <p className="text-sm text-gray-600">
                        {address.phoneNumber}
                        {address.additionalPhoneNumber && ` / ${address.additionalPhoneNumber}`}
                    </p>

                    </div>

                    <div className="flex items-start">
                    <input
                        type="radio"
                        name="address"
                        checked={selectedAddressId === address.id}
                        onChange={() => setSelectedAddressId(address.id)}
                        className="form-radio text-primary mt-1"
                    />
                    </div>
                </div>

                {/* Edit Address Link */}
                <div className="mt-2">
                    <Link
                    href={`/address-book/${address.id}/edit`}
                    className="text-primary hover:text-primary/80 text-sm flex items-center gap-1 underline transition-colors duration-200"
                    >
                    <FaEdit size={12} />
                    Edit
                    </Link>
                </div>
                </label>
            ))}

            {/* Add New Address Option */}
            <div className="mt-2">
                <Link href="/address-book/add" passHref>
                <Button variant="outline" className="w-full">
                    <FaPlus className="mr-2" />
                    Add New Address
                </Button>
                </Link>
            </div>
            </div>
        )}
        </section>
    </section>

  );
};

export default CustomerAddressSection;
