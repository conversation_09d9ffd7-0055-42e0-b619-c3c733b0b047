'use client';

import React from 'react';
import LottieLoader from '@/components/ui/LottieLoader';
import FullScreenLoader from '@/components/ui/FullScreenLoader';

/**
 * Debug component to test centering issues
 * Add this to any page to test: <CenteringTest />
 */
const CenteringTest: React.FC = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold text-center">Centering Debug Test</h1>
      
      {/* Test 1: Basic LottieLoader */}
      <section className="border border-red-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 1: Basic LottieLoader</h2>
        <div className="bg-gray-100 min-h-[200px] flex items-center justify-center">
          <LottieLoader size="lg" text="Basic Loader" />
        </div>
      </section>

      {/* Test 2: Centered LottieLoader */}
      <section className="border border-blue-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 2: Centered LottieLoader</h2>
        <div className="bg-gray-100 min-h-[200px]">
          <LottieLoader size="lg" text="Centered Loader" centered={true} />
        </div>
      </section>

      {/* Test 3: FullScreenLoader (not full screen) */}
      <section className="border border-green-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 3: FullScreenLoader (not full screen)</h2>
        <div className="bg-gray-100 min-h-[300px]">
          <FullScreenLoader 
            text="Not Full Screen" 
            size="lg" 
            fullScreen={false}
          />
        </div>
      </section>

      {/* Test 4: Manual centering */}
      <section className="border border-purple-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 4: Manual Centering</h2>
        <div className="bg-gray-100 min-h-[200px] flex items-center justify-center w-full">
          <div className="text-center">
            <LottieLoader size="lg" text="Manual Center" />
          </div>
        </div>
      </section>

      {/* Test 5: Full width container */}
      <section className="border border-yellow-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 5: Full Width Container</h2>
        <div className="bg-gray-100 min-h-[200px] w-full flex items-center justify-center">
          <LottieLoader size="lg" text="Full Width" centered={true} />
        </div>
      </section>

      {/* Test 6: Viewport width */}
      <section className="border border-pink-200 p-4">
        <h2 className="text-lg font-semibold mb-4">Test 6: Viewport Width</h2>
        <div className="bg-gray-100 min-h-[200px] w-screen flex items-center justify-center -mx-8">
          <LottieLoader size="lg" text="Viewport Width" centered={true} />
        </div>
      </section>

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">Debug Instructions:</h3>
        <ul className="text-sm space-y-1">
          <li>• Check which test shows proper centering</li>
          <li>• Look for any containers that are not full width</li>
          <li>• Inspect element to see actual widths and positioning</li>
          <li>• Test 6 should show true full-width centering</li>
        </ul>
      </div>
    </div>
  );
};

export default CenteringTest;
