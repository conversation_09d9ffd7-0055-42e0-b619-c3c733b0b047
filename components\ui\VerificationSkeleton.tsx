'use client';

import React from 'react';
import <PERSON>tie<PERSON>oader from './LottieLoader';

const VerificationSkeleton = () => {
  return (
    <div className="mb-6 animate-pulse">
      <div className="bg-white border border-gray-200 shadow-sm rounded-lg p-4">
        <div className="flex items-center justify-between">
          {/* Left side skeleton */}
          <div className="flex items-center space-x-3">
            {/* Icon skeleton */}
            <div className="w-9 h-9 bg-gray-200 rounded-lg"></div>
            <div>
              {/* Title and badge skeleton */}
              <div className="flex items-center space-x-2 mb-1">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-5 bg-gray-200 rounded-full w-16"></div>
              </div>
              {/* Status message skeleton */}
              <div className="h-3 bg-gray-200 rounded w-48"></div>
            </div>
          </div>

          {/* Right side button skeleton */}
          <div className="h-8 bg-gray-200 rounded w-20"></div>
        </div>

        {/* Restriction notice skeleton */}
        <div className="mt-3 p-3 bg-gray-100 border border-gray-200 rounded-lg">
          <div className="h-3 bg-gray-200 rounded w-full"></div>
        </div>
      </div>
      
      {/* Loading indicator */}
      <div className="mt-2 flex items-center justify-center">
        <LottieLoader
          size="sm"
          text="Loading verification status..."
          textSize="sm"
          centered={false}
        />
      </div>
    </div>
  );
};

export default VerificationSkeleton;
