"use client";
import React, { useState } from 'react';
import { Sheet, She<PERSON><PERSON>lose, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '../sheet';
import { BiMenuAltLeft } from 'react-icons/bi';
import { BsCart, BsSearch } from 'react-icons/bs';
import { RiLoginBoxLine } from 'react-icons/ri';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import NavbarLogo from '@/app/(buyers)/(main)/_components/NavbarLogo';
import { useScrollTop } from '@/hooks/use-scroll-top';
import { useRouter } from 'next/navigation';
import { FaChevronRight } from 'react-icons/fa';
import { ChevronLeft } from 'lucide-react';
import { useAuth } from '@/lib/stores/auth-store';
import { useCategoriesHierarchy } from '@/lib/hooks/use-categories';
import { useCategories } from '@/lib/stores/categories-store';
import { getIconByName } from '@/lib/utils/icons';

/**
 * User Avatar Component
 */
const UserAvatar = ({ name, onClick }: { name: string; onClick: () => void }) => {
  const initials = name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <button
      onClick={onClick}
      className="rounded-full hover:bg-primary/10 flex items-center justify-center"
      title={`Account - ${name}`}
    >
      <div className="w-8 h-8 bg-primary/70 text-primary-foreground rounded-full flex items-center justify-center text-xs font-semibold">
        {initials}
      </div>
    </button>
  );
};

/**
 * NavBar component renders the navigation bar for the application.
 */
const NavBar = () => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  // Fetch categories hierarchy
  const { isLoading: categoriesLoading, error: categoriesError } = useCategoriesHierarchy();
  const { getCombinedNavigation } = useCategories();

  // Get combined navigation categories (account + dynamic categories)
  const navigationCategories = getCombinedNavigation(isAuthenticated);

  // Show error in console for debugging
  if (categoriesError) {
    console.error('Categories loading error:', categoriesError);
  }

  const scrolled = useScrollTop();

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchInput.trim()) {
      router.push(`/catalog?q=${encodeURIComponent(searchInput.trim())}`);
    }
    setSearchOpen(false);
  };

  return (
    <nav
      className={cn(
        'z-50 bg-background dark:bg-[#1F1F1F] fixed top-0 flex items-center w-full px-6 py-4 sm:px-8 md:px-10 lg:px-20 justify-between',
        scrolled && 'border-b shadow-sm'
      )}
    >
      {/* Left Section - Logo & Menu */}
      <section className="flex items-center gap-4">
        <section className="w-full max-w-[264px]">
          <Sheet>
            <SheetTrigger>
              <BiMenuAltLeft size={30} className="cursor-pointer md:hidden" />
            </SheetTrigger>
            <SheetContent side="left" className="border-none bg-white overflow-y-scroll w-full">
              <SheetHeader>
                <SheetTitle>
                  <NavbarLogo />
                </SheetTitle>
              </SheetHeader>
              <div className="flex h-[calc(100vh-72px)] flex-col justify-between overflow-y-auto">
                <SheetClose asChild>
                  <section className="flex h-full flex-col items-start gap-1 space-y-4 pt-6 text-gray-800">
                    {/* Authentication Section */}
                    {!isAuthenticated && (
                      <div className="w-full">
                        <div className="flex justify-between items-center">
                          <SheetClose asChild>
                            <Link href="/login" className="text-sm uppercase">
                              <p>Login / Register</p>
                            </Link>
                          </SheetClose>
                          <Link href="/login" className="text-sm text-primary">
                            <FaChevronRight size={20} className="text-gray-500" />
                          </Link>
                        </div>
                        <div className="h-px bg-gray-200 my-4"></div>
                      </div>
                    )}

                    {/* Categories Section */}
                    {categoriesLoading ? (
                      <div className="w-full text-center py-4">
                        <p className="text-sm text-gray-500">Loading categories...</p>
                      </div>
                    ) : (
                      navigationCategories.map((section: any) => (
                        <div key={section.header} className="w-full">
                          <div className="flex justify-between items-center">
                            <SheetClose asChild key={section.headerLink}>
                              <Link href={section.headerLink} className="text-sm uppercase">
                                <p>
                                  {section.header === 'My Everyfash Account' && user
                                    ? `Hello, ${user.name.split(' ')[0]}`
                                    : section.header
                                  }
                                </p>
                              </Link>
                            </SheetClose>

                            {section.header === 'My Everyfash Account' ? (
                              <Link href={section.headerLink} className="text-sm text-primary">
                                <FaChevronRight size={20} className="text-gray-500" />
                              </Link>
                            ) : (
                              <Link href={section.headerLink} className="text-sm text-primary">
                                View All
                              </Link>
                            )}
                          </div>

                          <div className="space-y-2 mt-2">
                            {section.links.slice(0, 5).map((link: any) => (
                              <SheetClose asChild key={link.route}>
                                <Link
                                  href={link.route}
                                  className="flex gap-2 items-center rounded-lg w-full max-w-60 py-2 hover:bg-gray-100"
                                >
                                  {link.icon || (link.iconName && getIconByName(link.iconName, 20))}
                                  <p>{link.label}</p>
                                </Link>
                              </SheetClose>
                            ))}
                          </div>

                          <div className="h-px bg-gray-200 my-4"></div>
                        </div>
                      ))
                    )}
                  </section>
                </SheetClose>
              </div>
            </SheetContent>
          </Sheet>
        </section>
        <NavbarLogo />
      </section>

      {/* Search Bar (Desktop) */}
      <section className="hidden md:block w-4/12">
        <div className="relative">
          <form
            onSubmit={handleSearchSubmit}
            className="flex items-center bg-gray-100 rounded-md overflow-hidden"
          >
            <input
              type="text"
              className="flex-1 bg-transparent py-2 px-3 text-sm outline-none"
              placeholder="Search for products..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
            />
            <button type="submit" className="bg-primary text-primary-foreground px-4 py-2">
              Search
            </button>
          </form>
        </div>
      </section>

      {/* Icons */}
      <article className="flex gap-2 items-center">
        {/* Search Icon (Mobile Trigger) */}
        <button onClick={() => setSearchOpen(true)} className="rounded-full p-2 hover:bg-primary/10 md:hidden">
          <BsSearch size={20} />
        </button>

        {/* Account Icon */}
        {isAuthenticated && user ? (
          <UserAvatar
            name={user.name}
            onClick={() => router.push('/everyfash-account')}
          />
        ) : (
          <button
            onClick={() => router.push('/login')}
            className="rounded-full p-2 hover:bg-primary/10"
            title="Login"
          >
            <RiLoginBoxLine size={22} />
          </button>
        )}

        {/* Cart Icon */}
        <button onClick={() => router.push('/cart')} className="rounded-full p-2 hover:bg-primary/10">
          <BsCart size={22} />
        </button>
      </article>

      {/* Fullscreen Search Overlay */}
      {searchOpen && (
        <div className="fixed inset-0 bg-white z-9999 flex flex-col">
          <div className="flex items-center border-b p-4">
            <form onSubmit={handleSearchSubmit} className="flex-1 flex items-center">

              <button
               onClick={() => setSearchOpen(false)}
                className="text-gray-600 text-lg mr-4"
                >
                <ChevronLeft/>
                </button>
              <input
                type="text"
                className="flex-1 outline-none text-lg"
                placeholder="What are you looking for?"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleSearchSubmit(e as any);
                  }
                }}
                autoFocus
              />
              <button type="submit" className="text-primary font-semibold ml-2">
                <BsSearch size={22} className="text-gray-500 ml-2" />
              </button>
            </form>

          </div>
        </div>
      )}
    </nav>
  );
};

export default NavBar;
