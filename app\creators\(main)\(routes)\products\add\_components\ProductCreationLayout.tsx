'use client';

import React from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { CheckCircleIcon, Package, Settings, Palette } from 'lucide-react';
import { CiCircleMore } from 'react-icons/ci';
import { useProductForm } from '@/lib/contexts/ProductFormContext';

interface ProductStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const steps: ProductStep[] = [
  {
    id: 'product-info',
    title: 'Product Information',
    description: 'Basic product details, images, and descriptions',
    icon: <Package className="w-5 h-5" />,
  },
  {
    id: 'specifications',
    title: 'Specifications',
    description: 'Material, fit, care instructions, and occasions',
    icon: <Settings className="w-5 h-5" />,
  },
  {
    id: 'variations',
    title: 'Variations & Pricing',
    description: 'Colors, sizes, pricing, and related categories',
    icon: <Palette className="w-5 h-5" />,
  },
];

interface ProductCreationLayoutProps {
  children: React.ReactNode;
}

const ProductCreationLayout = ({ children }: ProductCreationLayoutProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const currentStep = searchParams.get("step") || "product-info";
  
  const { isStepComplete } = useProductForm();

  const getStepStatus = (stepId: string) => {
    switch (stepId) {
      case 'product-info':
        return isStepComplete('product-info') ? 'completed' : 'pending';
      case 'specifications':
        return isStepComplete('specifications') ? 'completed' : 'pending';
      case 'variations':
        return isStepComplete('variations') ? 'completed' : 'pending';
      default:
        return 'pending';
    }
  };

  const isCurrentStep = (stepId: string) => stepId === currentStep;

  const isStepAccessible = (stepId: string) => {
    switch (stepId) {
      case 'product-info':
        return true; // Always accessible
      case 'specifications':
        return isStepComplete('product-info'); // Accessible if product-info is complete
      case 'variations':
        return isStepComplete('product-info') && isStepComplete('specifications'); // Accessible if both previous steps are complete
      default:
        return false;
    }
  };

  const navigateToStep = (stepId: string) => {
    if (isStepAccessible(stepId)) {
      const newUrl = `${pathname}?step=${stepId}`;
      router.push(newUrl);
    }
  };

  return (
    <div className="flex flex-col w-full min-h-screen px-2 md:px-10 lg:px-20 pb-6 bg-gray-100">
      {/* Steps Navigation */}
      <nav className="w-full bg-white shadow-md rounded-lg border border-gray-200 px-2 py-4 mt-4">
        <h2 className="font-bold text-gray-800 mb-1">
          Add New Product
        </h2>
        <p className="text-sm text-gray-600 mb-4">
          Create a new product listing for your shop. Complete all sections to publish your product.
        </p>

        <ul className="space-y-2">
          {steps.map((step) => {
            const status = getStepStatus(step.id);
            const isCurrent = isCurrentStep(step.id);
            const isAccessible = isStepAccessible(step.id);

            // Determine display status: completed steps show as completed even if current
            const displayStatus = status === 'completed' ? 'completed' : (isCurrent ? 'current' : 'pending');

            return (
              <li
                key={step.id}
                onClick={() => navigateToStep(step.id)}
                className={`flex items-center select-none justify-between p-4 rounded-lg border transition ${
                  !isAccessible
                    ? "border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed opacity-50"
                    : displayStatus === 'completed'
                    ? "border-green-200 bg-green-50 text-green-700 hover:border-green-300 cursor-pointer"
                    : displayStatus === 'current'
                    ? "border-primary/40 bg-primary/5 text-primary-700 cursor-pointer"
                    : "border-gray-200 bg-gray-50 text-gray-700 hover:border-primary/30 hover:bg-primary/5 hover:text-primary-700 cursor-pointer"
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className={`text-lg ${!isAccessible ? 'text-gray-400' : ''}`}>
                    {step.icon}
                  </span>
                  <div>
                    <span className="text-sm font-medium block">{step.title}</span>
                    <span className="text-xs text-gray-500">{step.description}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {displayStatus === 'completed' ? (
                    <>
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <span className="text-xs text-green-600">
                        {isCurrent ? 'Current (Completed)' : 'Completed'}
                      </span>
                    </>
                  ) : displayStatus === 'current' ? (
                    <>
                      <div className="h-5 w-5 border-2 border-primary rounded-full flex items-center justify-center">
                        <div className="h-2 w-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-xs text-primary-600">Current</span>
                    </>
                  ) : (
                    <>
                      <CiCircleMore className="h-5 w-5 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {isAccessible ? 'Pending' : 'Locked'}
                      </span>
                    </>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Main Content */}
      <main className="w-full">
        {children}
      </main>
    </div>
  );
};

export default ProductCreationLayout;
