"use client";
import React, { useState } from "react";
import { AiOutlineDelete } from "react-icons/ai";
import { Package } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import CustomTabsTrigger from "@/components/ui/custom/CustomTabsTrigger";
import { useRouter } from "next/navigation";
import { useCreatorProducts, useDeleteProduct, useProductCounts } from "@/lib/hooks/use-products";
import { Product } from "@/lib/types/products";
import { Input } from "@/components/ui/input";
import VerificationGuard from "@/lib/components/VerificationGuard";


const ProductsPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  // Build query parameters based on current filters
  const queryParams = React.useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: 20,
      sort: '-createdAt',
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    if (activeTab !== "all") {
      switch (activeTab) {
        case "approved":
          params.status = "approved";
          break;
        case "pending":
          params.status = "pending";
          break;
        case "rejected":
          params.status = "rejected";
          break;
        case "draft":
          params.status = "draft";
          break;
      }
    }

    return params;
  }, [activeTab, searchTerm, currentPage]);

  const { data: productsResponse, isLoading, error } = useCreatorProducts(queryParams);
  const { data: countsResponse } = useProductCounts();
  const deleteProductMutation = useDeleteProduct();

  const products = productsResponse?.data?.products || [];
  const totalProducts = productsResponse?.total || 0;
  const totalPages = Math.ceil(totalProducts / (queryParams.limit || 20));
  const counts = countsResponse?.data || {
    all: 0,
    approved: 0,
    pending: 0,
    rejected: 0,
    lowStock: 0,
    outOfStock: 0,
  };

  // Client-side filtering for stock-based filters (not supported by API)
  const filteredProducts = products.filter((product: Product) => {
    if (activeTab === "low-stock") return product.totalStock > 0 && product.totalStock <= 10;
    if (activeTab === "out-of-stock") return product.totalStock === 0;
    return true; // Other filters are handled server-side
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeleteProduct = async (productId: string, productName: string) => {
    if (window.confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
      try {
        await deleteProductMutation.mutateAsync(productId);
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  const viewDetails = (id: string) => router.push(`/creators/products/${id}`);

  return (
    <VerificationGuard feature="products">
      <section className="w-full max-w-md mx-auto py-4">
    {/* Header */}
    <div className="flex justify-between items-center mb-4">
        <h1 className="text-sm font-bold uppercase text-gray-600">Manage Products</h1>
        <Link
          href="/creators/products/add?step=product-info"
          className="bg-primary text-primary-foreground text-sm px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200"
        >
          Add Product
        </Link>
      </div>

    <section className="w-full mb-8">
        <Input
          placeholder="Search products by name, description, or brand..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setCurrentPage(1); // Reset to first page when searching
          }}
          className="w-full"
        />
    </section>


      {/* Filter Tabs */}
      <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)} className="mb-6">
        <TabsList className="bg-gray-100  w-[98vw] overflow-x-scroll flex space-x-3 mb-2 p-2 justify-around">
          <CustomTabsTrigger value="all">All ({counts.all})</CustomTabsTrigger>
          <CustomTabsTrigger value="approved">Approved ({counts.approved})</CustomTabsTrigger>
          <CustomTabsTrigger value="pending">Pending ({counts.pending})</CustomTabsTrigger>
          <CustomTabsTrigger value="rejected">Rejected ({counts.rejected})</CustomTabsTrigger>
          <CustomTabsTrigger value="low-stock">Low Stock ({counts.lowStock})</CustomTabsTrigger>
          <CustomTabsTrigger value="out-of-stock">Out of Stock ({counts.outOfStock})</CustomTabsTrigger>
        </TabsList>
      </Tabs>

      {isLoading ? (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
            <p className="text-gray-500">Loading products...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">Failed to load products</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <Table className="">
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Brand</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Stock</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.length > 0 ? (
                filteredProducts.map((product: Product) => (
                  <TableRow key={product._id} className="whitespace-nowrap">
                    {/* Product Column with Image & Name */}
                    <TableCell className="flex items-center space-x-2 max-w-[250px]" onClick={() => viewDetails(product._id)}>
                      {product.images && product.images.length > 0 ? (
                        <div className="w-10 h-10 rounded overflow-hidden relative">
                          <Image
                            src={product.images[0]}
                            alt={product.name}
                            fill
                            sizes="40px"
                            className="object-cover"
                            onError={() => {
                              console.error('Product image failed to load:', product.images[0]);
                            }}
                          />
                        </div>
                      ) : (
                        <div className="w-10 h-10 rounded bg-gray-100 flex items-center justify-center">
                          <Package className="w-5 h-5 text-gray-400" />
                        </div>
                      )}
                      <span className="truncate max-w-[230px] text-sm leading-tight">
                        {product.name}
                      </span>
                    </TableCell>

                    {/* Brand */}
                    <TableCell className="text-sm text-gray-700">{product.brand}</TableCell>

                    {/* Price */}
                    <TableCell className="text-sm font-medium">GHS {product.basePrice}</TableCell>

                    {/* Stock */}
                    <TableCell className="text-sm">{product.totalStock}</TableCell>

                    {/* Status */}
                    <TableCell>
                      <Badge className={getStatusColor(product.status)}>
                        {product.status}
                      </Badge>
                    </TableCell>

                    {/* Actions */}
                    <TableCell className="w-auto whitespace-nowrap space-x-2">
                      <section className="flex items-center gap-2">
                        <Button
                          aria-label="View Product"
                          className="bg-primary hover:bg-primary/90"
                          size="sm"
                          onClick={() => viewDetails(product._id)}
                        >
                          View
                        </Button>
                        <Button
                          variant="destructive"
                          aria-label="Delete Product"
                          size="sm"
                          onClick={() => handleDeleteProduct(product._id, product.name)}
                          disabled={deleteProductMutation.isPending}
                        >
                          <AiOutlineDelete className="text-white" size={16} />
                        </Button>
                      </section>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">No products found for this filter</p>
                    {activeTab === "all" && (
                      <Link href="/creators/products/add?step=product-info">
                        <Button>Add Your First Product</Button>
                      </Link>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages} ({totalProducts} total)
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      </section>
    </VerificationGuard>
  );
};

export default ProductsPage;
