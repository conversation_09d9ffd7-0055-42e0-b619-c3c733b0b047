"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { BsSearch } from "react-icons/bs";

interface CustomSearchProps {
  placeholder?: string;
}

const CustomSearch: React.FC<CustomSearchProps> = ({
  placeholder = "What are you looking for?",
}) => {
  const [searchInput, setSearchInput] = useState("");
  const router = useRouter();

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchInput.trim()) {
      router.push(`?q=${encodeURIComponent(searchInput.trim())}`);
    }
    setSearchInput(""); // Clear input after search
  };

  return (
    <form
      onSubmit={handleSearchSubmit}
      className="flex items-center w-full h-12 border border-gray-300 text-sm bg-white rounded-lg px-4 py-2"
    >
      <input
        type="text"
        className="flex-1 bg-transparent outline-none text-sm placeholder-gray-500"
        placeholder={placeholder}
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            handleSearchSubmit(e as any);
          }
        }}
      />
      <button type="submit" className="text-primary font-semibold ml-2">
        <BsSearch size={18} className="text-primary" />
      </button>
    </form>
  );
};

export default CustomSearch;
