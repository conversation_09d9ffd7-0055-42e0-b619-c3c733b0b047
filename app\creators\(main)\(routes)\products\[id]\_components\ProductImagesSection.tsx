"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Edit3, Upload, X, GripVertical } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useUpdateProductImages } from "@/lib/hooks/use-products";
import Image from "next/image";

interface ProductImagesSectionProps {
  product: any;
}

const ProductImagesSection: React.FC<ProductImagesSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imageOrder, setImageOrder] = useState<string[]>(product.images || []);

  const updateImagesMutation = useUpdateProductImages();

  // Sync imageOrder with product.images when product changes
  useEffect(() => {
    setImageOrder(product.images || []);
  }, [product.images]);

  const toggleEdit = () => {
    setIsEditing(!isEditing);
    // Reset states when toggling edit mode
    if (isEditing) {
      setSelectedImages([]);
      setImageOrder(product.images || []);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      setSelectedImages(prev => [...prev, ...files]);
    }
  };

  const removeSelectedImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = (imageUrl: string) => {
    setImageOrder(prev => prev.filter(url => url !== imageUrl));
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newOrder = [...imageOrder];
    const [movedImage] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedImage);
    setImageOrder(newOrder);
  };

  const handleSave = async () => {
    try {
      console.log('=== Image Update Debug ===');
      console.log('Product ID:', product._id);
      console.log('Current product images:', product.images);
      console.log('Image order to send:', imageOrder);
      console.log('Selected new images:', selectedImages.map(f => f.name));
      console.log('========================');

      await updateImagesMutation.mutateAsync({
        productId: product._id,
        images: imageOrder,
        productImages: selectedImages.length > 0 ? selectedImages : undefined
      });

      setIsEditing(false);
      setSelectedImages([]);
    } catch (error) {
      // Error is handled by the mutation
      console.error('Failed to update images:', error);
    }
  };

  if (isEditing) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Product Images</h3>
          <Button variant="ghost" onClick={toggleEdit}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Current Images */}
        {imageOrder.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Current Images</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {imageOrder.map((imageUrl, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square relative rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src={imageUrl}
                      alt={`Product image ${index + 1}`}
                      fill
                      sizes="(max-width: 768px) 50vw, 33vw"
                      className="object-cover"
                      onError={(e) => {
                        console.error('Image failed to load:', imageUrl);
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => removeExistingImage(imageUrl)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="cursor-move"
                        >
                          <GripVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  {index === 0 && (
                    <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      Main
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

                {/* Selected New Images Preview */}
        {selectedImages.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">New Images to Upload</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {selectedImages.map((file, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square relative rounded-lg overflow-hidden border border-gray-200">
                    <Image
                      src={URL.createObjectURL(file)}
                      alt={`New image ${index + 1}`}
                      fill
                      sizes="(max-width: 768px) 50vw, 33vw"
                      className="object-cover"
                      onError={(e) => {
                        console.error('Image failed to load:', file.name);
                      }}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => removeSelectedImage(index)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upload New Images */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Add New Images</h4>
          <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, GIF up to 10MB each
              </p>
            </div>
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleImageUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            />
          </div>
        </div>


        {/* Action Buttons */}
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={toggleEdit}
            disabled={updateImagesMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={updateImagesMutation.isPending}
          >
            {updateImagesMutation.isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    );
  }

  // View Mode
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">Product Images</h3>
        <Button variant="ghost" onClick={toggleEdit}>
          <Edit3 className="h-5 w-5" />
        </Button>
      </div>

      {product.images && product.images.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {product.images.map((imageUrl: string, index: number) => (
            <div key={index} className="relative group">
              <div className="aspect-square relative rounded-lg overflow-hidden border border-gray-200">
                <Image
                  src={imageUrl}
                  alt={`Product image ${index + 1}`}
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  className="object-cover transition-transform duration-200 group-hover:scale-105"
                  onError={(e) => {
                    console.error('Image failed to load:', imageUrl);
                    // You could set a fallback image here if needed
                  }}
                />
              </div>
              {index === 0 && (
                <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                  Main Image
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <Upload className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p>No images uploaded</p>
          <p className="text-sm">Click edit to add product images</p>
        </div>
      )}
    </div>
  );
};

export default ProductImagesSection;
