import React from 'react';
import ProductsCarousel from './ProductsCarousel';

interface Brand {
  id: number;
  name: string;
  image: string;
}

const brands: Brand[] = [
  { id: 1, name: "<PERSON>", image: "/images/brands/nike.jpg" },
  { id: 2, name: "Adidas", image: "/images/brands/adidas.jpg" },
  { id: 3, name: "<PERSON><PERSON>", image: "/images/brands/zara.jpg" },
  { id: 4, name: "H&M", image: "/images/brands/hnm.jpg" },
  { id: 5, name: "Gucci", image: "/images/brands/gucci.jpg" },
  { id: 6, name: "<PERSON>", image: "/images/brands/lv.jpg" },
  { id: 7, name: "Prada", image: "/images/brands/prada.jpg" },
  { id: 8, name: "Versace", image: "/images/brands/versace.jpg" }
];

const TopFashionBrands: React.FC = () => {
  return (
    <section className="flex flex-col w-full mt-6">
      {/* Header */}
      <section className="w-full bg-primary py-4 px-3 mb-2 text-primary-foreground">
        <div className="flex justify-between items-center rounded-sm">
          <h1 className="text-base font-bold">Top Fashion Brands</h1>
        </div>
      </section>

      {/* Carousel with Brand Cards */}
      <ProductsCarousel>
        {brands.map((brand) => (
          <div key={brand.id} className="mr-2">
            <div className="relative w-full h-40">
              <img
                src={brand.image}
                alt={brand.name}
                className="w-full h-full object-cover rounded-lg"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center rounded-lg">
                <h2 className="text-white text-sm font-bold">{brand.name}</h2>
              </div>
            </div>
          </div>
        ))}
      </ProductsCarousel>
    </section>
  );
};

export default TopFashionBrands;
