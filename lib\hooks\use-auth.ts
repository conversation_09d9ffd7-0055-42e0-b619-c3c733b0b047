import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { authApi, handleApiError } from '@/lib/api';
import { useAuth, useAuthActions } from '@/lib/stores/auth-store';
import { UserRole } from '@/lib/types/auth';

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
};

// Login mutation with auto-verification email support
export const useLogin = () => {
  const router = useRouter();
  const { login, setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.login,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: (data) => {
      login(data.data.user, data.token);

      toast({
        title: 'Welcome back!',
        description: 'You have been successfully logged in.',
        className: 'bg-green-100 text-green-800',
      });

      // Redirect based on user role
      if (data.data.user.role === 'creator') {
        // For creators, we'll check onboarding status in the CreatorGuard
        router.push('/creators');
      } else {
        router.push('/');
      }
    },
    onError: (error: any) => {
      console.log('Login Error:', error);

      // Parse error response to check for unverified email auto-verification
      let errorData = null;
      try {
        // Handle different error response formats
        if (error?.response?.data) {
          errorData = error.response.data;
        } else if (error?.message) {
          // Try to parse error message if it contains JSON
          try {
            errorData = JSON.parse(error.message);
          } catch {
            errorData = { message: error.message };
          }
        }
      } catch {
        errorData = { message: 'An unexpected error occurred' };
      }

      console.log('Parsed Error Data:', errorData);

      // Check if this is an unverified email error with auto-verification
      if (errorData?.emailSent && errorData?.userEmail) {
        // Show friendly message about verification email being sent
        toast({
          title: 'Email Verification Required',
          description: errorData.message || 'We\'ve sent a verification email to your inbox. Please check your email and verify your account.',
          className: 'bg-blue-100 text-blue-800',
          duration: 8000, // Show longer for important message
        });

        // Determine user role from the login form context or email
        // For now, we'll use the current route to determine role
        const currentPath = window.location.pathname;
        const isCreatorLogin = currentPath.includes('/creators/');
        const checkEmailPath = isCreatorLogin ? '/creators/check-email' : '/check-email';

        router.push(`${checkEmailPath}?email=${encodeURIComponent(errorData.userEmail)}`);

        return; // Don't show generic error
      }

      // Handle other login errors normally
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Login Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Register mutation - now handles email verification flow
export const useRegister = () => {
  const router = useRouter();
  const { setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.register,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: (data) => {
      console.log('Registration Success Response:', data);

      // Don't login user yet - they need to verify email first
      toast({
        title: 'Account Created!',
        description: data.message || 'Please check your email to verify your account.',
        className: 'bg-green-100 text-green-800',
      });

      // Redirect to check email page with user role
      const checkEmailPath = data.data.user.role === 'creator'
        ? '/creators/check-email'
        : '/check-email';
      router.push(`${checkEmailPath}?email=${encodeURIComponent(data.data.user.email)}`);
    },
    onError: (error) => {
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Registration Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Note: Phone verification removed as backend doesn't require it
// export const useVerifyPhone = () => { ... }

// Logout mutation
export const useLogout = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { logout } = useAuthActions();
  const { user } = useAuth();

  return useMutation({
    mutationFn: authApi.logout,
    onSuccess: () => {
      const userRole = user?.role;
      logout();
      queryClient.clear();

      toast({
        title: 'Logged Out',
        description: 'You have been successfully logged out.',
      });

      // Redirect based on user role
      if (userRole === 'creator') {
        router.push('/creators/login');
      } else {
        router.push('/');
      }
    },
    onError: () => {
      // Even if server logout fails, clear local state
      const userRole = user?.role;
      logout();
      queryClient.clear();

      // Redirect based on user role
      if (userRole === 'creator') {
        router.push('/creators/login');
      } else {
        router.push('/');
      }
    },
  });
};

// Forgot password mutation
export const useForgotPassword = () => {
  const { setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.forgotPassword,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: () => {
      toast({
        title: 'Reset Link Sent',
        description: 'Check your email for password reset instructions.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Reset Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Reset password mutation
export const useResetPassword = () => {
  const router = useRouter();
  const { login, setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: ({ token, credentials }: { token: string; credentials: any }) =>
      authApi.resetPassword(token, credentials),
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: (data) => {
      login(data.data.user, data.token);

      toast({
        title: 'Password Reset Successful',
        description: 'Your password has been reset and you are now logged in.',
        className: 'bg-green-100 text-green-800',
      });

      // Redirect based on user role
      const redirectPath = data.data.user.role === 'creator' ? '/creators' : '/';
      router.push(redirectPath);
    },
    onError: (error) => {
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Password Reset Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Email verification mutation
export const useVerifyEmail = () => {
  const router = useRouter();
  const { login, setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.verifyEmail,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: (data) => {
      login(data.data.user, data.token);

      toast({
        title: 'Email Verified!',
        description: 'Your email has been verified successfully. Welcome to Everyfash!',
        className: 'bg-green-100 text-green-800',
      });

      // Redirect based on user role
      const redirectPath = data.data.user.role === 'creator' ? '/creators' : '/';
      router.push(redirectPath);
    },
    onError: (error) => {
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Verification Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Resend verification email mutation
export const useResendVerification = () => {
  const { setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.resendVerification,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: (data) => {
      console.log('Resend Verification Success:', data);
      toast({
        title: 'Email Sent!',
        description: data.message || 'Verification email sent! Please check your inbox.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      console.log('Resend Verification Error:', error);
      const apiError = handleApiError(error);
      setError(apiError.message);

      toast({
        variant: 'destructive',
        title: 'Failed to Send Email',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Change password mutation
export const useChangePassword = () => {
  const { setError, setLoading } = useAuthActions();

  return useMutation({
    mutationFn: authApi.changePassword,
    onMutate: () => {
      setLoading(true);
      setError(null);
    },
    onSuccess: () => {
      toast({
        title: 'Password Changed',
        description: 'Your password has been successfully updated.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      const apiError = handleApiError(error);
      setError(apiError.message);
      
      toast({
        variant: 'destructive',
        title: 'Password Change Failed',
        description: apiError.message,
      });
    },
    onSettled: () => {
      setLoading(false);
    },
  });
};

// Get user profile query
export const useProfile = () => {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: authKeys.profile(),
    queryFn: authApi.getProfile,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Google OAuth authentication
export const useGoogleAuth = () => {
  const { isLoading } = useAuth();

  // Initiate Google OAuth flow
  const initiateAuth = (role: UserRole) => {
    const authUrl = authApi.initiateGoogleAuth(role);
    window.location.href = authUrl;
  };

  return {
    initiateAuth,
    isLoading,
  };
};
