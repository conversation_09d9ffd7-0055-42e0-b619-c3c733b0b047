'use client';

import Link from 'next/link';
import { FaShippingFast, FaBoxOpen, FaStar, FaRegStar, FaChevronRight } from 'react-icons/fa';
import { Button } from '@/components/ui/button';
import { Suspense, useState } from 'react';


const shop = {
  id: '1',
  shopName: 'The Thrift Hub',
  sellerScore: 4.8,
  numFollowers: 1200,
  performance: {
    shipping: 'Excellent',
    quality: 'Good',
    customerRating: '4.5',
  },
  products: [
    { id: 1, name: 'Product 1', price: 29.99, image: '/product1.jpg' },
    { id: 2, name: 'Product 2', price: 49.99, image: '/product2.jpg' },
  ],
  sellingDuration: '1 year',
  successfulSales: '500+',
  reviews: Array.from({ length: 240 }, (_, i) => ({
    id: i + 1,
    user: `User ${i + 1}`,
    title: `Review Title ${i + 1}`,
    comment: `This is a sample review comment for review number ${i + 1}.`,
    rating: Math.floor(Math.random() * 5) + 1,
    date: '2025-01-15',
  })),
  totalReviews: 240,
};



const ShopInsightsPage: React.FC = () => {
  
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const reviewsPerPage = 5;
  const totalPages = Math.ceil(shop.reviews.length / reviewsPerPage);

  const updatePage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());
    router.push(`?${params.toString()}`);
  };


  const paginatedReviews = shop.reviews.slice(
    (currentPage - 1) * reviewsPerPage,
    currentPage * reviewsPerPage
  );

  const hasReviews = shop.reviews.length > 0;

  return (
    <section className=" my-2  mt-20  w-full">
      <div className="px-3 py-3 bg-white rounded shadow-sm">
        <div className="flex justify-between items-center border-b pb-2">
          <h1 className="text-sm font-semibold text-gray-600">{shop.shopName}</h1>
        </div>

        <div className="mt-3 flex justify-between items-center text-sm">
          <div className="text-sm text-gray-600">
            <p>
              <span className="font-medium text-gray-600 mr-1">{shop.sellerScore}</span> Seller Score
            </p>
            <p>
              <span className="font-medium text-gray-600 mr-1">{shop.numFollowers}</span> Followers
            </p>
          </div>
          <Button className="text-xs bg-primary hover:bg-primary/90 text-white px-3 py-1 rounded">Follow</Button>
        </div>
      </div>
      
      <h2 className="text-sm uppercase mt-4font-medium text-gray-700 mb-1">Seller Performance</h2>
      <div className="px-3 bg-white py-3 rounded shadow-sm">
        <div className="space-y-2 text-sm text-gray-600">
          <p className="flex items-center gap-2">
            <FaShippingFast className="text-green-600" />
            Shipping Score: <span className="font-medium text-green-600">{shop.performance.shipping}</span>
          </p>
          <p className="flex items-center gap-2">
            <FaBoxOpen className="text-green-600" />
            Quality Score: <span className="font-medium text-green-600">{shop.performance.quality}</span>
          </p>
          <p className="flex items-center gap-2">
            <FaStar className="text-yellow-500" />
            Customer Rating: <span className="font-medium text-yellow-600">{shop.performance.customerRating}</span>
          </p>
        </div>
      </div>

      <h2 className="text-sm uppercase mt-4  font-medium text-gray-700 mb-1">Seller Information</h2>
      <div className="px-3 bg-white py-3 space-y-2  rounded shadow-sm">
        <p className="text-sm text-gray-600">Selling on Everyfash: {shop.sellingDuration}</p>
        <p className="text-sm text-gray-600">Successful Sales: {shop.successfulSales}</p>
      </div>

      <h1 className="uppercase font-medium text-gray-600 mb-1 mt-4">
        Comments from Verified Purchases ({shop.totalReviews})
      </h1>
      <div className="mt-2">
        {hasReviews ? (
          <div className="space-y-2">
            {paginatedReviews.map((review) => (
              <div key={review.id} className="border-b pb-4 last:border-b-0 bg-white p-3 rounded">
                <div className="flex justify-between items-center mb-1">
                  <div className="flex items-center gap-1">
                    {Array.from({ length: review.rating }, (_, i) => (
                      <FaStar key={i} className="text-yellow-400 text-xs" />
                    ))}
                    {Array.from({ length: 5 - review.rating }, (_, i) => (
                      <FaRegStar key={i} className="text-gray-300 text-xs" />
                    ))}
                  </div>
                  <p className="text-xs text-gray-500">{review.date}</p>
                </div>
                <p className="text-sm font-bold text-gray-800">{review.title}</p>
                <p className="text-sm text-gray-700 mt-1">{review.comment}</p>
                <p className="text-xs text-gray-500 mt-1">By {review.user}</p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-10 text-gray-500">
            <p className="text-sm">No reviews yet. Be the first to review this product!</p>
          </div>
        )}
      </div>

      {hasReviews && (
        <div className="flex justify-between items-center mt-4">
          <button
            onClick={() => updatePage(Math.max(currentPage - 1, 1))}
            className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
            disabled={currentPage === 1}
          >
            Previous
          </button>
          <span className="text-sm font-semibold">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => updatePage(Math.min(currentPage + 1, totalPages))}
            className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
            disabled={currentPage === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </section>
  );
};

import React from 'react'
import { useRouter, useSearchParams } from 'next/navigation';

const ShopProfile = () => {
  return (
    <Suspense fallback={<div>Loading comments...</div>}>
      <ShopInsightsPage  />
    </Suspense>
  )
}

export default ShopProfile

