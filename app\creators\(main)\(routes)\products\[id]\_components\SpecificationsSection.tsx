"use client";

import React, { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Edit3, X, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useUpdateProductSpecifications } from "@/lib/hooks/use-products";
import {
  FIT_TYPE_OPTIONS,
  MAIN_MATERIAL_OPTIONS,
  DRESS_STYLE_OPTIONS,
  PANT_TYPE_OPTIONS,
  SKIRT_TYPE_OPTIONS,
  PATTERN_OPTIONS,
  CLOSURE_OPTIONS,
  NECKLINE_OPTIONS,
  SLEEVE_LENGTH_OPTIONS,
  WAISTLINE_OPTIONS,
  HEMLINE_OPTIONS
} from "@/lib/types/products";

// Helper component for displaying details
const DetailItem = ({ label, value }: { label: string; value: string | number }) => (
  <div className="text-sm">
    <p className="text-gray-500">{label}</p>
    <p className="text-gray-900">{value || "Not specified"}</p>
  </div>
);

// Form validation schema - exact copy from SpecificationsForm
const specificationsSchema = z.object({
  mainMaterial: z.string().optional(),
  dressStyle: z.string().optional(),
  pantType: z.string().optional(),
  skirtType: z.string().optional(),
  mensPantSize: z.string().optional(),
  fitType: z.enum(['Slim', 'Regular', 'Loose', 'Oversized', 'Tailored', 'Skinny', 'Straight', 'Relaxed']).optional(),
  pattern: z.string().optional(),
  closure: z.string().optional(),
  neckline: z.string().optional(),
  sleeveLength: z.string().optional(),
  waistline: z.string().optional(),
  hemline: z.string().optional(),
});

type SpecificationsFormData = z.infer<typeof specificationsSchema>;

interface SpecificationsSectionProps {
  product: any;
}

const SpecificationsSection: React.FC<SpecificationsSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(false);

  // Use the specific update hook
  const updateSpecificationsMutation = useUpdateProductSpecifications();

  // Form setup - exact copy from SpecificationsForm
  const form = useForm<SpecificationsFormData>({
    resolver: zodResolver(specificationsSchema),
    defaultValues: {
      mainMaterial: product.specifications?.mainMaterial || "",
      dressStyle: product.specifications?.dressStyle || "",
      pantType: product.specifications?.pantType || "",
      skirtType: product.specifications?.skirtType || "",
      mensPantSize: product.specifications?.mensPantSize || "",
      fitType: product.specifications?.fitType || undefined,
      pattern: product.specifications?.pattern || "",
      closure: product.specifications?.closure || "",
      neckline: product.specifications?.neckline || "",
      sleeveLength: product.specifications?.sleeveLength || "",
      waistline: product.specifications?.waistline || "",
      hemline: product.specifications?.hemline || "",
    },
  });

  const { control, handleSubmit, formState: { errors } } = form;

  const toggleEdit = () => setIsEditing(!isEditing);

  const onSubmit = async (data: SpecificationsFormData) => {
    try {
      await updateSpecificationsMutation.mutateAsync({
        productId: product._id,
        specifications: {
          mainMaterial: data.mainMaterial,
          dressStyle: data.dressStyle,
          pantType: data.pantType,
          skirtType: data.skirtType,
          mensPantSize: data.mensPantSize,
          fitType: data.fitType,
          pattern: data.pattern,
          closure: data.closure,
          neckline: data.neckline,
          sleeveLength: data.sleeveLength,
          waistline: data.waistline,
          hemline: data.hemline,
        },
      });

      toast({
        title: "Specifications updated",
        description: "Product specifications have been successfully updated.",
        className: "bg-green-100 text-green-800 border-green-200",
      });

      toggleEdit();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Specifications update failed:', error);
    }
  };

  if (isEditing) {
    return (
      <div className="w-full">
        <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-bold">Product Specifications</h2>
            <Button variant="ghost" onClick={toggleEdit}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Main Material */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Main Material</label>
              <Controller
                name="mainMaterial"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select main material" />
                    </SelectTrigger>
                    <SelectContent>
                      {MAIN_MATERIAL_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Fit Type */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Fit Type</label>
              <Controller
                name="fitType"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select fit type" />
                    </SelectTrigger>
                    <SelectContent>
                      {FIT_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Dress Style */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Dress Style</label>
              <Controller
                name="dressStyle"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select dress style" />
                    </SelectTrigger>
                    <SelectContent>
                      {DRESS_STYLE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Pant Type */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Pant Type</label>
              <Controller
                name="pantType"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select pant type" />
                    </SelectTrigger>
                    <SelectContent>
                      {PANT_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Skirt Type */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Skirt Type</label>
              <Controller
                name="skirtType"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select skirt type" />
                    </SelectTrigger>
                    <SelectContent>
                      {SKIRT_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Men's Pant Size */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Men's Pant Size</label>
              <Controller
                name="mensPantSize"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="e.g., 32x34, 30x32"
                  />
                )}
              />
            </div>

            {/* Pattern */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Pattern</label>
              <Controller
                name="pattern"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select pattern" />
                    </SelectTrigger>
                    <SelectContent>
                      {PATTERN_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Closure */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Closure</label>
              <Controller
                name="closure"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select closure type" />
                    </SelectTrigger>
                    <SelectContent>
                      {CLOSURE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>

          {/* Additional Specifications */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Neckline */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Neckline</label>
              <Controller
                name="neckline"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select neckline" />
                    </SelectTrigger>
                    <SelectContent>
                      {NECKLINE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Sleeve Length */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Sleeve Length</label>
              <Controller
                name="sleeveLength"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select sleeve length" />
                    </SelectTrigger>
                    <SelectContent>
                      {SLEEVE_LENGTH_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Waistline */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Waistline</label>
              <Controller
                name="waistline"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select waistline" />
                    </SelectTrigger>
                    <SelectContent>
                      {WAISTLINE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Hemline */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Hemline</label>
              <Controller
                name="hemline"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select hemline" />
                    </SelectTrigger>
                    <SelectContent>
                      {HEMLINE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>



          {/* Submit Button */}
          <div className="flex justify-end mt-8">
            <Button type="submit" className="px-8">
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    );
  }

  // View Mode
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-800">Product Specifications</h3>
        <Button variant="ghost" onClick={toggleEdit}>
          <Edit3 className="h-5 w-5" />
        </Button>
      </div>

      <section className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DetailItem label="Main Material" value={product.specifications?.mainMaterial} />
          <DetailItem label="Fit Type" value={product.specifications?.fitType} />
          <DetailItem label="Dress Style" value={product.specifications?.dressStyle} />
          <DetailItem label="Pant Type" value={product.specifications?.pantType} />
          <DetailItem label="Skirt Type" value={product.specifications?.skirtType} />
          <DetailItem label="Men's Pant Size" value={product.specifications?.mensPantSize} />
          <DetailItem label="Pattern" value={product.specifications?.pattern} />
          <DetailItem label="Closure" value={product.specifications?.closure} />
          <DetailItem label="Neckline" value={product.specifications?.neckline} />
          <DetailItem label="Sleeve Length" value={product.specifications?.sleeveLength} />
          <DetailItem label="Waistline" value={product.specifications?.waistline} />
          <DetailItem label="Hemline" value={product.specifications?.hemline} />
        </div>
      </section>
    </div>
  );
};

export default SpecificationsSection;
