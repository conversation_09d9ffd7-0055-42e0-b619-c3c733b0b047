import Footer from "@/components/ui/custom/Footer";
import NavBar from "@/components/ui/custom/NavBar";
import type { Metadata } from "next";
import { Toaster } from "@/components/ui/toaster"
import BuyersBottomNavigation from "@/components/ui/custom/BuyersBottomBar";
import { BuyerOrGuestGuard } from "@/lib/components/AuthGuard";


export const metadata: Metadata = {
  title: "Everyfash",
  description: "Africa's largest fashion marketplace",
};

export default function HomeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <BuyerOrGuestGuard>
      <main className="w-full">
          <NavBar/>
              <section className="flex min-h-screen w-full px-2 md:px-10 lg:px-20 pb-6 bg-gray-100">
                  {children}
              </section>
              <BuyersBottomNavigation/>
              <Toaster />
          <Footer/>
      </main>
    </BuyerOrGuestGuard>
  );
}
