#!/usr/bin/env node

/**
 * Migration script to help identify remaining Loader2 usage
 * Run with: node scripts/migrate-to-lottie.js
 */

const fs = require('fs');
const path = require('path');

const searchDir = process.cwd();
const excludeDirs = ['node_modules', '.next', '.git', 'dist', 'build'];
const includeExtensions = ['.tsx', '.ts', '.jsx', '.js'];

function searchFiles(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!excludeDirs.includes(file)) {
        searchFiles(filePath, results);
      }
    } else {
      const ext = path.extname(file);
      if (includeExtensions.includes(ext)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for Loader2 usage
        if (content.includes('Loader2')) {
          const lines = content.split('\n');
          const matches = [];
          
          lines.forEach((line, index) => {
            if (line.includes('Loader2')) {
              matches.push({
                line: index + 1,
                content: line.trim()
              });
            }
          });
          
          if (matches.length > 0) {
            results.push({
              file: path.relative(searchDir, filePath),
              matches
            });
          }
        }
      }
    }
  }
  
  return results;
}

console.log('🔍 Searching for remaining Loader2 usage...\n');

const results = searchFiles(searchDir);

if (results.length === 0) {
  console.log('✅ No Loader2 usage found! Migration complete.');
} else {
  console.log(`📋 Found Loader2 usage in ${results.length} files:\n`);
  
  results.forEach(({ file, matches }) => {
    console.log(`📄 ${file}`);
    matches.forEach(({ line, content }) => {
      console.log(`   Line ${line}: ${content}`);
    });
    console.log('');
  });
  
  console.log('💡 Migration suggestions:');
  console.log('   • Full-screen loading: Replace with <FullScreenLoader />');
  console.log('   • Button loading: Replace with <ButtonLoader />');
  console.log('   • Custom loading: Replace with <LottieLoader />');
  console.log('   • Suspense fallback: Replace with <FullScreenLoader />');
}

console.log('\n📚 See docs/LOTTIE_LOADER_USAGE.md for detailed migration guide');
