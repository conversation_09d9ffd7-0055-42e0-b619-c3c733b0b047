import { useState } from 'react';

const DeliveryTypeSelector = () => {
  const [deliveryType, setDeliveryType] = useState<'standard' | 'express'>('standard');

  return (
    <section className='w-full'>
        <h2 className="font-medium text-gray-700 mb-1">DELIVERY METHOD</h2>
        <section className="bg-white py-4 px-2 mb-4 rounded-lg shadow-sm">
        <div className="space-y-3">
            {/* Standard Delivery */}
            <label
            className={`block p-3 border rounded-lg cursor-pointer transition ${
                deliveryType === 'standard'
                ? 'border-primary/20 bg-primary-50'
                : 'border-gray-200 bg-white hover:border-primary/20 hover:bg-gray-50 transition-colors duration-200'
            }`}
            onClick={() => setDeliveryType('standard')}
            >
            <div className="flex justify-between items-center">
                <div>
                <p className="font-medium text-gray-800">Standard Delivery</p>
                <p className="text-sm text-gray-600">Delivery in 2 to 3 days (Depends on the seller)</p>
                </div>
                <input
                type="radio"
                name="deliveryType"
                checked={deliveryType === 'standard'}
                onChange={() => setDeliveryType('standard')}
                className="form-radio text-primary"
                />
            </div>
            </label>

            {/* Everyfash Express */}
            <label
            className={`block p-3 border rounded-lg cursor-pointer transition ${
                deliveryType === 'express'
                ? 'border-primary/20 bg-primary-50'
                : 'border-gray-200 bg-white hover:border-primary/20 hover:bg-gray-50 transition-colors duration-200'
            }`}
            onClick={() => setDeliveryType('express')}
            >
            <div className="flex justify-between items-center">
                <div>
                <p className="font-medium text-gray-800">Everyfash Express</p>
                <p className="text-sm text-gray-600">Delivery in less than 24 hours (Handled by platform)</p>
                </div>
                <input
                type="radio"
                name="deliveryType"
                checked={deliveryType === 'express'}
                onChange={() => setDeliveryType('express')}
                className="form-radio text-primary"
                />
            </div>
            </label>
        </div>
        </section>
    </section>

  );
};

export default DeliveryTypeSelector;
