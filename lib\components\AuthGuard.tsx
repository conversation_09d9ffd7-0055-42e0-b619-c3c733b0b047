'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/stores/auth-store';
import { UserRole } from '@/lib/types/auth';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import { useOnboardingStatus } from '@/lib/hooks/use-onboarding';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: UserRole;
  redirectTo?: string;
  requireVerified?: boolean;
}

export const AuthGuard = ({
  children,
  requireAuth = true,
  requiredRole,
  redirectTo,
  requireVerified = false,
}: AuthGuardProps) => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // If auth is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      const loginPath = requiredRole === 'creator' ? '/creators/login' : '/login';
      router.push(redirectTo || loginPath);
      return;
    }

    // If user is authenticated but doesn't have required role
    if (isAuthenticated && requiredRole && user?.role !== requiredRole) {
      const defaultPath = user?.role === 'creator' ? '/creators' : '/';
      router.push(redirectTo || defaultPath);
      return;
    }


    // If auth is not required but user is authenticated, redirect to appropriate dashboard
    if (!requireAuth && isAuthenticated && user) {
      const dashboardPath = user.role === 'creator' ? '/creators' : '/';
      console.log('AuthGuard (GuestGuard) - Authenticated user on guest page, redirecting to:', dashboardPath);
      router.push(redirectTo || dashboardPath);
      return;
    }
  }, [
    isLoading,
    isAuthenticated,
    user,
    requireAuth,
    requiredRole,
    requireVerified,
    redirectTo,
    router,
  ]);

  // Show loading spinner while checking auth
  if (isLoading) {
    return <FullScreenLoader text="Loading..." size="xl" />;
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // If user is authenticated but doesn't have required role, don't render children
  if (isAuthenticated && requiredRole && user?.role !== requiredRole) {
    return null;
  }


  // If auth is not required but user is authenticated, don't render children (will redirect)
  if (!requireAuth && isAuthenticated) {
    return null;
  }

  return <>{children}</>;
};

// Convenience components for specific use cases
export const BuyerGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requiredRole'>) => (
  <AuthGuard {...props} requiredRole="buyer">
    {children}
  </AuthGuard>
);

export const CreatorGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requiredRole'>) => (
  <AuthGuard {...props} requiredRole="creator">
    {children}
  </AuthGuard>
);

// Creator guard with onboarding check
export const CreatorGuardWithOnboarding = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { isLoading: onboardingLoading, needsOnboarding, isComplete } = useOnboardingStatus();

  useEffect(() => {
    console.log('CreatorGuardWithOnboarding - State:', {
      authLoading,
      onboardingLoading,
      isAuthenticated,
      userRole: user?.role,
      needsOnboarding,
      isComplete,
      currentPath: window.location.pathname
    });

    // Don't redirect while loading
    if (authLoading || onboardingLoading) {
      console.log('CreatorGuardWithOnboarding - Still loading, waiting...');
      return;
    }

    // If not authenticated, redirect to creator login
    if (!isAuthenticated) {
      console.log('CreatorGuardWithOnboarding - Not authenticated, redirecting to login');
      router.push('/creators/login');
      return;
    }

    // If not a creator, redirect to buyer dashboard
    if (user?.role !== 'creator') {
      console.log('CreatorGuardWithOnboarding - Not a creator, redirecting to buyer dashboard');
      router.push('/');
      return;
    }

    // STRICT: If onboarding is not explicitly complete, redirect to onboarding
    // This ensures no access to any creator pages until isComplete === true
    if (needsOnboarding || isComplete !== true) {
      console.log('CreatorGuardWithOnboarding - Onboarding incomplete, redirecting to onboarding');
      router.push('/creators/onboarding');
      return;
    }

    console.log('CreatorGuardWithOnboarding - All checks passed, rendering children');
  }, [authLoading, onboardingLoading, isAuthenticated, user, needsOnboarding, isComplete, router]);

  // Show loading spinner while checking auth and onboarding
  if (authLoading || onboardingLoading) {
    return <FullScreenLoader text="Verifying access..." size="2xl" />;
  }

  // If not authenticated, don't render children
  if (!isAuthenticated) {
    return null;
  }

  // If not a creator, don't render children
  if (user?.role !== 'creator') {
    return null;
  }

  // STRICT: If onboarding is not explicitly complete, don't render children
  if (needsOnboarding || isComplete !== true) {
    return null;
  }

  return <>{children}</>;
};

export const GuestGuard = ({ children, ...props }: Omit<AuthGuardProps, 'requireAuth'>) => (
  <AuthGuard {...props} requireAuth={false}>
    {children}
  </AuthGuard>
);

// Special guard for buyer routes that allows both guests and buyers, but redirects creators
export const BuyerOrGuestGuard = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // If user is authenticated and is a creator, redirect to creator dashboard
    if (isAuthenticated && user?.role === 'creator') {
      router.push('/creators');
      return;
    }
  }, [isLoading, isAuthenticated, user, router]);

  // Show loading spinner while checking auth
  if (isLoading) {
    return <FullScreenLoader text="Loading..." size="xl" />;
  }

  // If user is a creator, don't render children (will redirect)
  if (isAuthenticated && user?.role === 'creator') {
    return null;
  }

  return <>{children}</>;
};
