import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { profileApi } from '@/lib/api/profile';
import { 
  BusinessInfoUpdateData, 
  PaymentInfoUpdateData, 
  ShopInfoUpdateData, 
  ShippingInfoUpdateData,
  BusinessInfoResponse,
  PaymentInfoResponse,
  ShopInfoResponse,
  ShippingInfoResponse
} from '@/lib/types/auth';
import { toast } from '@/hooks/use-toast';

// Query keys for profile data
export const PROFILE_QUERY_KEYS = {
  businessInfo: ['profile', 'business-info'] as const,
  paymentInfo: ['profile', 'payment-info'] as const,
  shopInfo: ['profile', 'shop-info'] as const,
  shippingInfo: ['profile', 'shipping-info'] as const,
} as const;

// Business Info Profile Hooks
export const useBusinessInfoProfile = () => {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.businessInfo,
    queryFn: profileApi.getBusinessInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useUpdateBusinessInfoProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: profileApi.updateBusinessInfo,
    onSuccess: (data) => {
      // Update the cache with new data
      queryClient.setQueryData(PROFILE_QUERY_KEYS.businessInfo, data);
      
      toast({
        title: 'Business Information Updated',
        description: 'Your business information has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: error?.message || 'Failed to update business information. Please try again.',
      });
    },
  });
};

// Payment Info Profile Hooks
export const usePaymentInfoProfile = () => {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.paymentInfo,
    queryFn: profileApi.getPaymentInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useUpdatePaymentInfoProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: profileApi.updatePaymentInfo,
    onSuccess: (data) => {
      // Update the cache with new data
      queryClient.setQueryData(PROFILE_QUERY_KEYS.paymentInfo, data);
      
      toast({
        title: 'Payment Information Updated',
        description: 'Your payment information has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: error?.message || 'Failed to update payment information. Please try again.',
      });
    },
  });
};

// Shop Info Profile Hooks
export const useShopInfoProfile = () => {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.shopInfo,
    queryFn: profileApi.getShopInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useUpdateShopInfoProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: profileApi.updateShopInfo,
    onSuccess: (data) => {
      // Update the cache with new data
      queryClient.setQueryData(PROFILE_QUERY_KEYS.shopInfo, data);
      
      toast({
        title: 'Shop Information Updated',
        description: 'Your shop information has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: error?.message || 'Failed to update shop information. Please try again.',
      });
    },
  });
};

// Shipping Info Profile Hooks
export const useShippingInfoProfile = () => {
  return useQuery({
    queryKey: PROFILE_QUERY_KEYS.shippingInfo,
    queryFn: profileApi.getShippingInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export const useUpdateShippingInfoProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: profileApi.updateShippingInfo,
    onSuccess: (data) => {
      // Update the cache with new data
      queryClient.setQueryData(PROFILE_QUERY_KEYS.shippingInfo, data);
      
      toast({
        title: 'Shipping Information Updated',
        description: 'Your shipping information has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: error?.message || 'Failed to update shipping information. Please try again.',
      });
    },
  });
};

// Utility hook to invalidate all profile queries
export const useInvalidateProfileQueries = () => {
  const queryClient = useQueryClient();
  
  return () => {
    queryClient.invalidateQueries({ queryKey: ['profile'] });
  };
};
