/**
 * Utility functions for handling API error responses
 */

interface ApiErrorResponse {
  status: string;
  error?: {
    statusCode: number;
    status: string;
    isOperational: boolean;
  };
  message: string;
  stack?: string;
}

/**
 * Extracts the error message from an API error response
 * @param error - The error object from the API call
 * @returns The user-friendly error message
 */
export function getApiErrorMessage(error: any): string {
  // If it's already a string, return it
  if (typeof error === 'string') {
    return error;
  }

  // Check if it's an API error response with the expected structure
  if (error?.response?.data) {
    const apiError = error.response.data as ApiErrorResponse;
    
    // Return the message from the API response
    if (apiError.message) {
      return apiError.message;
    }
  }

  // Check if the error has a direct message property
  if (error?.message) {
    return error.message;
  }

  // Check if it's a network error
  if (error?.code === 'NETWORK_ERROR' || error?.name === 'NetworkError') {
    return 'Network error. Please check your internet connection and try again.';
  }

  // Check for timeout errors
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }

  // Check for specific HTTP status codes
  if (error?.response?.status) {
    switch (error.response.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'You are not authorized. Please log in and try again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 409:
        return 'There was a conflict with your request. Please try again.';
      case 422:
        return 'The data provided is invalid. Please check your input.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Service temporarily unavailable. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return `Request failed with status ${error.response.status}. Please try again.`;
    }
  }

  // Fallback to a generic error message
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Extracts validation errors from an API error response
 * @param error - The error object from the API call
 * @returns An array of validation error messages
 */
export function getApiValidationErrors(error: any): string[] {
  const errors: string[] = [];

  // Check if it's an API error response with validation errors
  if (error?.response?.data?.errors) {
    const validationErrors = error.response.data.errors;
    
    // Handle different validation error formats
    if (Array.isArray(validationErrors)) {
      errors.push(...validationErrors.map((err: any) => err.message || err.msg || String(err)));
    } else if (typeof validationErrors === 'object') {
      // Handle object format like { field: ['error1', 'error2'] }
      Object.values(validationErrors).forEach((fieldErrors: any) => {
        if (Array.isArray(fieldErrors)) {
          errors.push(...fieldErrors);
        } else if (typeof fieldErrors === 'string') {
          errors.push(fieldErrors);
        }
      });
    }
  }

  return errors;
}

/**
 * Creates a user-friendly error title based on the error type
 * @param error - The error object from the API call
 * @returns A descriptive error title
 */
export function getApiErrorTitle(error: any): string {
  // Check for specific error types
  if (error?.response?.status) {
    switch (error.response.status) {
      case 400:
        return 'Invalid Request';
      case 401:
        return 'Authentication Required';
      case 403:
        return 'Access Denied';
      case 404:
        return 'Not Found';
      case 409:
        return 'Conflict';
      case 422:
        return 'Validation Error';
      case 429:
        return 'Rate Limited';
      case 500:
        return 'Server Error';
      case 502:
      case 503:
        return 'Service Unavailable';
      default:
        return 'Request Failed';
    }
  }

  // Check for network errors
  if (error?.code === 'NETWORK_ERROR' || error?.name === 'NetworkError') {
    return 'Network Error';
  }

  // Check for timeout errors
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return 'Request Timeout';
  }

  return 'Error';
}
