"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast"; // Updated import
import { InputField } from "./_components/InputField"; // Updated import

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState<"holiday" | "commissions">("holiday");

  return (
    <div className=" mt-6 ">
      {/* Navigation */}
      <nav className="w-full bg-white shadow-md rounded-lg border border-gray-200 px-2 py-4 mb-2">
        <h2 className="font-bold text-gray-800 mb-1">Shop Settings</h2>
        <p className="text-sm text-gray-600 mb-4">Manage your shop’s holiday mode and commission details.</p>

        <ul className="flex gap-4 overflow-x-auto">
          <li
            onClick={() => setActiveTab("holiday")}
            className={`flex-1 text-center py-2 rounded-lg cursor-pointer border transition ${
              activeTab === "holiday"
                ? "border-primary/40 bg-primary-50 text-primary-700"
                : "border-gray-200 bg-gray-50 text-gray-700 hover:border-primary/30 hover:bg-primary-50 hover:text-primary-700"
            }`}
          >
            Holiday Mode
          </li>
          <li
            onClick={() => setActiveTab("commissions")}
            className={`flex-1 text-center py-2 rounded-lg cursor-pointer border transition ${
              activeTab === "commissions"
                ? "border-primary/40 bg-primary-50 text-primary-700"
                : "border-gray-200 bg-gray-50 text-gray-700 hover:border-primary/30 hover:bg-primary-50 hover:text-primary-700"
            }`}
          >
            Commissions & Fees
          </li>
        </ul>
      </nav>

      {/* Content */}
      {activeTab === "holiday" ? <HolidayMode /> : <CommissionsAndFees />}
    </div>
  );
};

// Holiday Mode Section
const HolidayMode = () => {
  const [holiday, setHoliday] = useState({
    startDate: "",
    endDate: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHoliday({ ...holiday, [e.target.name]: e.target.value });
  };

  const handleSave = () => {
    if (!holiday.startDate || !holiday.endDate) {
      toast({
        title: "Error",
        description: "Please fill in both start and end dates.",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Holiday Mode Activated",
      description: `Your shop will be on holiday from ${holiday.startDate} to ${holiday.endDate}.`,
    });
  };

  return (
    <section className="bg-white rounded-lg shadow-md px-2 py-4">
      <h3 className="font-semibold text-gray-800 mb-2">Holiday Mode</h3>
      <p className="text-sm text-gray-600 mb-6">
        Your shop will be delisted and will not receive any orders during your holiday dates. If you have pending orders,
        you still need to ship them; otherwise, they will be canceled, which will negatively impact your Seller Score.
      </p>
      <div className="grid gap-4">
        <InputField
          label="Start Date"
          type="date"
          name="startDate"
          value={holiday.startDate}
          onChange={handleChange}
          required
        />
        <InputField
          label="End Date"
          type="date"
          name="endDate"
          value={holiday.endDate}
          onChange={handleChange}
          required
        />
      </div>
      <Button className="bg-primary w-full mt-4 hover:bg-primary/90" onClick={handleSave}>
        Save
      </Button>
    </section>
  );
};

// Commissions & Fees Section
const CommissionsAndFees = () => {
  const [productAmount, setProductAmount] = useState<string>("");
  const [desiredProfit, setDesiredProfit] = useState<string>("");
  const [recommendedPrice, setRecommendedPrice] = useState<string | null>(null);
  const commissionRate = 0.01; // 1% commission

  const calculateRecommendedPrice = () => {
    const amount = parseFloat(productAmount);
    const profit = parseFloat(desiredProfit);

    if (isNaN(amount) || isNaN(profit)) {
      toast({
        title: "Error",
        description: "Please enter valid numbers for product amount and desired profit.",
        variant: "destructive",
      });
      return;
    }

    const totalCost = amount + profit;
    const commission = totalCost * commissionRate;
    const finalPrice = totalCost + commission;

    setRecommendedPrice(finalPrice.toFixed(2));
  };

  return (
    <section className="bg-white rounded-lg shadow-md px-2 py-4">
      <h3 className="font-semibold text-gray-800 mb-2">Commissions & Fees</h3>
      <p className="text-sm text-gray-600 mb-6">
        The platform takes a <strong>1% commission</strong> on every sale. To avoid losses, please factor this into your pricing.
      </p>

      <p className="text-sm text-gray-700 mb-4">
        <strong>Global Price:</strong> This is your standard price — the regular price your product is listed at.
        <br />
        <strong>Sale Price:</strong> Shops with lower sale prices often get better visibility and rank higher on the platform, which can help boost your sales. We recommend setting a competitive sale price to increase your shop’s exposure.
      </p>

      <div className="grid gap-4">
        <InputField
          label="Product Cost (GHS)"
          type="number"
          value={productAmount}
          onChange={(e:any) => setProductAmount(e.target.value)}
          placeholder="e.g. 100"
        />
        <InputField
          label="Desired Profit (GHS)"
          type="number"
          value={desiredProfit}
          onChange={(e:any) => setDesiredProfit(e.target.value)}
          placeholder="e.g. 20"
        />
        <Button className="bg-primary hover:bg-primary/90" onClick={calculateRecommendedPrice}>
          Calculate Recommended Global Price
        </Button>
        {recommendedPrice && (
          <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-lg">
            <p>
              To cover your costs and make your desired profit after the 1% commission, we recommend setting your sale price
              at <strong>GH₵ {recommendedPrice}</strong> or higher.
            </p>
            <p className="text-sm text-gray-600 mt-2">
              For your global price, consider setting it much higher than the sale price .
            </p>
          </div>
        )}
      </div>
    </section>
  );
};


export default SettingsPage;
