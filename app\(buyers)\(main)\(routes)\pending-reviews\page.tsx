'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/stores/auth-store';
import AuthPrompt from '@/components/ui/custom/AuthPrompt';

interface ReviewItem {
  id: string;
  productName: string;
  image: string;
  price: number;
  discount: number;
  size: string;
  orderNumber: string;
  deliveredDate: string;
}

const pendingReviews: ReviewItem[] = [
  {
    id: '1',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    price: 280.0,
    discount: 20,
    size: 'XL',
    orderNumber: '********',
    deliveredDate: '22-01-25',
  },
  {
    id: '2',
    productName: 'Casual Shirt',
    image: '/images/men/Shirts_TN.png',
    price: 150.0,
    discount: 15,
    size: 'L',
    orderNumber: '********',
    deliveredDate: '20-01-25',
  },
  {
    id: '3',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    price: 280.0,
    discount: 20,
    size: 'XL',
    orderNumber: '********',
    deliveredDate: '22-01-25',
  },
  {
    id: '4',
    productName: 'Casual Shirt',
    image: '/images/men/Shirts_TN.png',
    price: 150.0,
    discount: 15,
    size: 'L',
    orderNumber: '********',
    deliveredDate: '20-01-25',
  },
  {
    id: '5',
    productName: 'Elegant Dress',
    image: '/images/ladies/short.jpg',
    price: 280.0,
    discount: 20,
    size: 'XL',
    orderNumber: '********',
    deliveredDate: '22-01-25',
  },
  {
    id: '6',
    productName: 'Casual Shirt',
    image: '/images/men/Shirts_TN.png',
    price: 150.0,
    discount: 15,
    size: 'L',
    orderNumber: '********',
    deliveredDate: '20-01-25',
  },
];

const PendingReviewsPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="account" title="Share Your Experience" description="Help other shoppers by sharing your honest reviews. Your feedback shapes the community and helps others make better choices!" />;
  }

  return (
    <section className="w-full mt-20 py-4">
      <h1 className="text-lg font-semibold text-gray-700 mb-4">Pending Reviews</h1>

      {pendingReviews.length > 0 ? (
        pendingReviews.map((product) => (
          <div
            key={product.id}
            className="bg-white rounded shadow-sm p-3 mb-3 flex flex-col"
          >
            <div className="flex">
              {/* Product Image */}
              <div className="w-24 h-24 bg-gray-200">
                <img
                  src={product.image}
                  alt={product.productName}
                  className="w-full h-full object-cover rounded"
                />
              </div>

              {/* Product Details */}
              <div className="ml-4 flex-1">
                <h2 className="font-medium text-gray-800">{product.productName}</h2>

                {/* Size, Order Number, Delivery Date */}
                <p className="text-sm text-gray-600">Size: {product.size}</p>
                <p className="text-sm text-gray-600">Order: {product.orderNumber}</p>
                <p className="text-sm text-gray-600">
                  Delivered on: {product.deliveredDate}
                </p>
              </div>
            </div>

            {/* Rate this product */}
            <div className="flex border-t items-center mt-3 justify-center">
              <Button variant="link" onClick={()=>{router.push(`/pending-reviews/${product.id}`)}} className=" px-3 py-1 text-primary/90">
                Rate this product
              </Button>
            </div>
          </div>
        ))
      ) : (
        <p className="text-sm text-gray-500">No pending reviews at the moment.</p>
      )}
    </section>
  );
};

export default PendingReviewsPage;
