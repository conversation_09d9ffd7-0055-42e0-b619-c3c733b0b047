"use client"

import * as React from "react"
import { Check, ChevronLeft, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DynamicEditor } from "@/app/creators/(main)/_components/DynamicEditor"
import { useRouter } from "next/navigation"
import StepNavigation from "../_components/StepNavigation"



// Sample categories data
const categories = [
  {
    name: "Men's Fashion",
    subcategories: [
      {
        name: "Clothing",
        subcategories: [
          {
            name: "Shirts",
            subcategories: [
              { name: "T-Shirts", subcategories: [] },
              { name: "Polo Shirts", subcategories: [] },
              { name: "Formal Shirts", subcategories: [] },
              { name: "Casual Shirts", subcategories: [] },
              { name: "Denim Shirts", subcategories: [] },
            ],
          },
          {
            name: "Trousers & Shorts",
            subcategories: [
              { name: "Jeans", subcategories: [] },
              { name: "Chinos", subcategories: [] },
              { name: "Formal Trousers", subcategories: [] },
              { name: "Cargo Pants", subcategories: [] },
              { name: "Shorts", subcategories: [] },
              { name: "Joggers & Sweatpants", subcategories: [] },
            ],
          },
          {
            name: "Suits & Blazers",
            subcategories: [
              { name: "Formal Suits", subcategories: [] },
              { name: "Blazers", subcategories: [] },
              { name: "Tuxedos", subcategories: [] },
            ],
          },
          {
            name: "Traditional Wear",
            subcategories: [
              { name: "Kaftans", subcategories: [] },
              { name: "Agbada", subcategories: [] },
              { name: "Native Sets", subcategories: [] },
            ],
          },
          { name: "Jackets & Coats", subcategories: [] },
          { name: "Hoodies & Sweatshirts", subcategories: [] },
          { name: "Underwear & Sleepwear", subcategories: [] },
        ],
      },
      {
        name: "Footwear",
        subcategories: [
          { name: "Sneakers", subcategories: [] },
          { name: "Formal Shoes", subcategories: [] },
          { name: "Loafers", subcategories: [] },
          { name: "Boots", subcategories: [] },
          { name: "Sandals & Slippers", subcategories: [] },
          { name: "Sports Shoes", subcategories: [] },
        ],
      },
      {
        name: "Accessories",
        subcategories: [
          { name: "Watches", subcategories: [] },
          { name: "Sunglasses", subcategories: [] },
          { name: "Belts", subcategories: [] },
          { name: "Hats & Caps", subcategories: [] },
          { name: "Bags & Wallets", subcategories: [] },
          { name: "Cufflinks & Tie Clips", subcategories: [] },
          { name: "Scarves & Gloves", subcategories: [] },
        ],
      },
    ],
  },
  {
    name: "Women's Fashion",
    subcategories: [
      {
        name: "Clothing",
        subcategories: [
          {
            name: "Dresses",
            subcategories: [
              { name: "Casual Dresses", subcategories: [] },
              { name: "Evening Dresses", subcategories: [] },
              { name: "Maxi Dresses", subcategories: [] },
              { name: "Bodycon Dresses", subcategories: [] },
            ],
          },
          {
            name: "Tops & Blouses",
            subcategories: [
              { name: "T-Shirts", subcategories: [] },
              { name: "Blouses", subcategories: [] },
              { name: "Tank Tops", subcategories: [] },
              { name: "Crop Tops", subcategories: [] },
            ],
          },
          {
            name: "Bottoms",
            subcategories: [
              { name: "Jeans", subcategories: [] },
              { name: "Leggings", subcategories: [] },
              { name: "Skirts", subcategories: [] },
              { name: "Palazzo Pants", subcategories: [] },
            ],
          },
          { name: "Jumpsuits & Rompers", subcategories: [] },
          { name: "Jackets & Coats", subcategories: [] },
          { name: "Lingerie & Nightwear", subcategories: [] },
          { name: "Traditional Wear", subcategories: [] },
        ],
      },
      {
        name: "Footwear",
        subcategories: [
          { name: "Heels", subcategories: [] },
          { name: "Flats", subcategories: [] },
          { name: "Sneakers", subcategories: [] },
          { name: "Sandals", subcategories: [] },
          { name: "Boots", subcategories: [] },
          { name: "Slippers", subcategories: [] },
        ],
      },
      {
        name: "Accessories",
        subcategories: [
          { name: "Handbags", subcategories: [] },
          { name: "Jewelry", subcategories: [] },
          { name: "Watches", subcategories: [] },
          { name: "Sunglasses", subcategories: [] },
          { name: "Scarves & Shawls", subcategories: [] },
          { name: "Belts", subcategories: [] },
          { name: "Hats & Caps", subcategories: [] },
        ],
      },
    ],
  },
  {
    name: "Kids' Fashion",
    subcategories: [
      {
        name: "Boys' Clothing",
        subcategories: [
          { name: "T-Shirts & Polos", subcategories: [] },
          { name: "Shirts", subcategories: [] },
          { name: "Trousers & Shorts", subcategories: [] },
          { name: "Sweaters & Hoodies", subcategories: [] },
        ],
      },
      {
        name: "Girls' Clothing",
        subcategories: [
          { name: "Dresses", subcategories: [] },
          { name: "Tops & Tees", subcategories: [] },
          { name: "Skirts & Shorts", subcategories: [] },
          { name: "Sweaters & Hoodies", subcategories: [] },
        ],
      },
      {
        name: "Kids' Footwear",
        subcategories: [
          { name: "Sneakers", subcategories: [] },
          { name: "Sandals", subcategories: [] },
          { name: "Boots", subcategories: [] },
          { name: "School Shoes", subcategories: [] },
        ],
      },
      {
        name: "Kids' Accessories",
        subcategories: [
          { name: "Backpacks", subcategories: [] },
          { name: "Hats & Caps", subcategories: [] },
          { name: "Watches", subcategories: [] },
          { name: "Gloves & Scarves", subcategories: [] },
        ],
      },
    ],
  },
  {
    name: "Unisex Fashion",
    subcategories: [
      {
        name: "Clothing",
        subcategories: [
          { name: "T-Shirts", subcategories: [] },
          { name: "Hoodies", subcategories: [] },
          { name: "Tracksuits", subcategories: [] },
        ],
      },
      {
        name: "Footwear",
        subcategories: [
          { name: "Sneakers", subcategories: [] },
          { name: "Slides", subcategories: [] },
        ],
      },
      {
        name: "Accessories",
        subcategories: [
          { name: "Sunglasses", subcategories: [] },
          { name: "Caps & Hats", subcategories: [] },
          { name: "Backpacks", subcategories: [] },
        ],
      },
    ],
  },
];




// Types for clarity
type Category = {
  name: string
  subcategories?: Category[]
}

type FlattenedCategory = {
  name: string
  path: string
}

// Recursive function to get all categories and their paths
const flattenCategories = (categories: Category[], path: string[] = []): FlattenedCategory[] => {
  return categories.flatMap((category) => {
    const currentPath = [...path, category.name]

    if (category.subcategories && category.subcategories.length > 0) {
      // Go deeper for categories with subcategories
      return flattenCategories(category.subcategories, currentPath)
    }

    // Only add final node categories (no subcategories)
    return [{ name: category.name, path: currentPath.join(" > ") }]
  })
}


const allCategories = flattenCategories(categories)



// Sample brands and colors
const brands = [
  { value: "nike", label: "Nike" },
  { value: "adidas", label: "Adidas" },
  { value: "puma", label: "Puma" },
  { value: "zara", label: "Zara" },
]

const colors = [
  { value: "red", label: "Red" },
  { value: "blue", label: "Blue" },
  { value: "black", label: "Black" },
  { value: "white", label: "White" },
]



export default function ProductInformation() {
  const [mainImage, setMainImage] = React.useState<string | null>(null)
  const [otherImages, setOtherImages] = React.useState<(string | null)[]>(Array(7).fill(null))
  const [productName, setProductName] = React.useState("")
  const [category, setCategory] = React.useState("")
  const [brand, setBrand] = React.useState("")
  const [color, setColor] = React.useState("")
  const [description, setDescription] = React.useState("")
  const [highlights, setHighlights] = React.useState("")
  const [currentStep, setCurrentStep] = React.useState(1);
  
  // Categories related objects
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentCategories, setCurrentCategories] = React.useState(categories)
  const [breadcrumb, setBreadcrumb] = React.useState<string[]>([])
  const [searchTerm, setSearchTerm] = React.useState("")
  const router = useRouter()



  const handleImageUpload = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const imageUrl = URL.createObjectURL(file)
      if (index === 0) {
        setMainImage(imageUrl)
      } else {
        const newImages = [...otherImages]
        newImages[index - 1] = imageUrl
        setOtherImages(newImages)
      }
    }
  }

  const renderCombobox = (
    label: string,
    value: string,
    options: { label: string; value: string }[],
    onSelect: (val: string) => void
  ) => {
    const [open, setOpen] = React.useState(false)

    return (
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" role="combobox" className="w-full justify-between">
              {value
                ? options.find((option) => option.value === value)?.label
                : `Select ${label}...`}
              <ChevronsUpDown className="opacity-50 ml-2" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder={`Search ${label}...`} className="h-9" />
              <CommandList>
                <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => {
                        onSelect(option.value === value ? "" : option.value)
                        setOpen(false)
                      }}
                    >
                      {option.label}
                      <Check className={cn("ml-auto", value === option.value ? "opacity-100" : "opacity-0")} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    )
  }

  // Categories functions
  const openCategoryModal = () => {
    setSearchTerm("")
    setCurrentCategories(categories)
    setBreadcrumb([])
    setIsModalOpen(true)
  }

  const selectCategory = (category: string) => {
    setCategory(category)
    setIsModalOpen(false)
  }

  const goDeeper = (subcategory: { name: string; subcategories: any[] }) => {
    setBreadcrumb((prev) => [...prev, subcategory.name])
    setCurrentCategories(subcategory.subcategories)
  }


  const handleFormSubmit = () => {
    
  };


  const goBack = () => {
    const updatedBreadcrumb = [...breadcrumb]
    updatedBreadcrumb.pop()
    setBreadcrumb(updatedBreadcrumb)

    let newCurrent = categories
    for (const step of updatedBreadcrumb) {
      newCurrent = newCurrent.find((cat) => cat.name === step)?.subcategories || []
    }
    setCurrentCategories(newCurrent)
  }

  const filteredCategories = searchTerm
    ? allCategories.filter((cat) =>
        cat.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : currentCategories



  // Type guard to distinguish between search results and category objects
  function isFlattenedCategory(cat: any): cat is FlattenedCategory {
  return "path" in cat
  }


  

  return (
    <div className="w-full ">

      <section className="w-full p-4 rounded-lg bg-white mb-4">

        
      {/* Image upload */}
      <div className="grid grid-cols-4 gap-4 mb-8">
        {[mainImage, ...otherImages].map((image, index) => (
          <div
            key={index}
            className="border rounded-lg flex items-center justify-center h-20 bg-gray-100 cursor-pointer relative"
          >
            {image ? (
              <>
                <img src={image} alt="Product" className="w-full h-full object-cover rounded-lg" />
                <button
                  type="button"
                  onClick={() => {
                    if (index === 0) {
                      setMainImage(null)
                    } else {
                      const updatedImages = [...otherImages]
                      updatedImages[index - 1] = null
                      setOtherImages(updatedImages)
                    }
                  }}
                  className="absolute top-0 right-1 bg-white text-red-500 rounded-full p-0.5 text-xs"
                >
                  ✕
                </button>
              </>
            ) : (
              <label className="text-center text-gray-500 cursor-pointer text-xs">
                {index === 0 ? "Main Image" : "Image"}
                <input type="file" className="hidden" onChange={(e) => handleImageUpload(index, e)} />
              </label>
            )}
          </div>
        ))}
      </div>


      {/* Product name */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
        <Input
          placeholder="Enter product name"
          value={productName}
          onChange={(e) => setProductName(e.target.value)}
        />
      </div>

      
    <section className="w-full bg-white">
        {/* Category Field */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <Button variant="outline" className="w-full" onClick={openCategoryModal}>
            {category || "Select Category"}
          </Button>
        </div>

      

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="w-full max-w-[90%] max-h-[90vh] overflow-y-auto rounded-lg">
            <DialogHeader>
              <DialogTitle>Select Category</DialogTitle>
            </DialogHeader>

            {breadcrumb.length > 0 && !searchTerm && (
              <Button variant="ghost" onClick={goBack} className="mb-2">
                ← Back
              </Button>
            )}

            <Input
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mb-4"
            />

            <ul>
              {filteredCategories.map((cat, index) => (
                <li key={`${cat.name}-${index}`} className="mb-2">
                  {isFlattenedCategory(cat) ? (
                    // Search result view
                    <Button
                      variant="outline"
                      className="w-full text-left"
                      onClick={() => selectCategory(cat.name)}
                    >
                      {cat.name}
                      <span className="block text-xs text-gray-500">under {cat.path}</span>
                    </Button>
                  ) : cat.subcategories && cat.subcategories.length > 0 ? (
                    // Navigable category view
                    <Button
                      variant="ghost"
                      className="w-full text-left"
                      onClick={() => goDeeper(cat)}
                    >
                      {cat.name} →
                    </Button>
                  ) : (
                    // Selectable category without subcategories
                    <Button
                      variant="outline"
                      className="w-full text-left"
                      onClick={() => selectCategory(cat.name)}
                    >
                      {cat.name}
                    </Button>
                  )}
                </li>
              ))}
              {filteredCategories.length === 0 && (
                <p className="text-center text-gray-500">No categories found.</p>
              )}
            </ul>
          </DialogContent>
        </Dialog>

    </section>

      

      <div className="grid grid-cols-2 gap-4">
        {/* Brand */}
        {renderCombobox("Brand", brand, brands, setBrand)}

        {/* Color */}
        {renderCombobox("Color", color, colors, setColor)}
      </div>
      

      {/* Description */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">Product Description</label>

        <DynamicEditor initialContent={description} onChange={setDescription}  />
      </div>

      {/* Highlights */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">Product Highlights</label>

        <DynamicEditor initialContent={highlights} onChange={setHighlights}  />
      </div>

      
      </section>

      <StepNavigation onSubmit={handleFormSubmit} />
    </div>
  )
}
