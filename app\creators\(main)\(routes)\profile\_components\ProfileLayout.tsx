'use client';

import React from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { CheckCircleIcon } from 'lucide-react';
import { CiCircleMore } from 'react-icons/ci';
import { BsInfoCircle } from 'react-icons/bs';
import { CreditCard, Store, Truck } from 'lucide-react';

interface ProfileStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const steps: ProfileStep[] = [
  {
    id: 'shop-info',
    title: 'Shop Information',
    description: 'Manage your shop details, branding, and social media',
    icon: <Store />,
  },
  {
    id: 'business-info',
    title: 'Business Information',
    description: 'Update your business details and verification documents',
    icon: <BsInfoCircle />,
  },
  {
    id: 'payment-info',
    title: 'Payment Information',
    description: 'Manage your payment methods and payout preferences',
    icon: <CreditCard />,
  },
  {
    id: 'shipping-info',
    title: 'Shipping Information',
    description: 'Configure your shipping and return addresses',
    icon: <Truck />,
  },
];

interface ProfileLayoutProps {
  children: React.ReactNode;
}

const ProfileLayout = ({ children }: ProfileLayoutProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const currentStep = searchParams.get("step") || "shop-info";

  const isCurrentStep = (stepId: string) => stepId === currentStep;

  const navigateToStep = (stepId: string) => {
    const newUrl = `${pathname}?step=${stepId}`;
    router.push(newUrl);
  };

  return (
    <div className="flex flex-col w-full min-h-screen  md:px-10 lg:px-20 pb-6 bg-gray-100">
      {/* Steps Navigation - Matching Onboarding Style */}
      <nav className="w-full bg-white shadow-md rounded-lg border border-gray-200 px-2 py-4 mt-4">
        <h2 className="font-bold text-gray-800 mb-1">
          Profile Management
        </h2>
        <p className="text-sm text-gray-600 mb-4">
          Update and manage your creator profile information. Keep your details current to maintain your shop's credibility.
        </p>

        <ul className="space-y-2">
          {steps.map((step) => {
            const isCurrent = isCurrentStep(step.id);

            return (
              <li
                key={step.id}
                onClick={() => navigateToStep(step.id)}
                className={`flex items-center select-none justify-between p-4 rounded-lg cursor-pointer border ${
                  isCurrent
                    ? "border-primary/40 bg-primary/5 text-primary-700"
                    : "border-gray-200 bg-gray-50 text-gray-700 hover:border-primary/30 hover:bg-primary/5 hover:text-primary-700"
                } transition`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{step.icon}</span>
                  <div>
                    <span className="text-sm font-medium block">{step.title}</span>
                    <span className="text-xs text-gray-500">{step.description}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {isCurrent ? (
                    <>
                      <div className="h-5 w-5 border-2 border-primary rounded-full flex items-center justify-center">
                        <div className="h-2 w-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-xs text-primary-600">Current</span>
                    </>
                  ) : (
                    <>
                      <CiCircleMore className="h-5 w-5 text-gray-400" />
                      <span className="text-xs text-gray-500">Available</span>
                    </>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Main Content */}
      <main className="w-full">
        {children}
      </main>
    </div>
  );
};

export default ProfileLayout;
