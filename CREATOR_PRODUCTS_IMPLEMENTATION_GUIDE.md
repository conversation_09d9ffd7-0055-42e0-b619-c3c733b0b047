# 🛍️ Creator Products Implementation Guide

## Overview
This guide details the complete implementation of the Creator Products system in EveryFash, including multi-step product creation, product management, and detailed product editing.

## 📁 File Structure

```
app/creators/(main)/(routes)/products/
├── page.tsx                           # Products listing page
├── add/
│   ├── layout.tsx                     # Multi-step form layout
│   ├── variant/
│   │   └── page.tsx                   # Step 1: Variations
│   ├── product-info/
│   │   └── page.tsx                   # Step 2: Product Info
│   ├── specifications/
│   │   └── page.tsx                   # Step 3: Specifications
│   └── _components/
│       ├── VariationsForm.tsx         # Variations form component
│       ├── ProductInfoForm.tsx        # Product info form component
│       ├── SpecificationsForm.tsx     # Specifications form component
│       └── StepIndicator.tsx          # Progress indicator
└── [id]/
    ├── page.tsx                       # Product details page
    └── _components/
        ├── ProductInfoSection.tsx     # Editable product info section
        ├── VariationsSection.tsx      # Editable variations section
        ├── SpecificationsSection.tsx  # Editable specifications section
        └── MetricsCard.tsx            # Product metrics display

lib/
├── contexts/
│   └── ProductFormContext.tsx        # Multi-step form state management
├── hooks/
│   └── use-products.ts               # Product-related React Query hooks
├── api/
│   └── products.ts                   # Product API client
└── types/
    └── products.ts                   # Product TypeScript interfaces
```

## 🔄 Multi-Step Product Creation Flow

### Step 1: Variations Form
**Route**: `/creators/products/add/variant`

**Purpose**: Define product variations (color, size, quantity, pricing)

**Key Features**:
- Dynamic variation management (add/remove)
- Sale pricing with date validation
- Related categories selection
- Form validation with Zod

**Implementation**:
```typescript
// Validation Schema
const variationsSchema = z.object({
  variations: z.array(z.object({
    color: z.string().min(1, "Color is required"),
    size: z.string().min(1, "Size is required"),
    quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
    price: z.union([z.string(), z.number()]).pipe(z.coerce.number().min(0.01, "Price must be greater than 0")),
    salePrice: z.union([z.string(), z.number()]).transform((val) => {
      if (val === "" || val === null || val === undefined) return undefined;
      return Number(val);
    }).optional(),
    saleStartDate: z.string().optional(),
    saleEndDate: z.string().optional(),
  }).refine((data) => {
    // Sale pricing validation logic
    if (data.salePrice && data.salePrice > 0) {
      if (!data.saleStartDate || !data.saleEndDate) return false;
      if (data.salePrice >= data.price) return false;
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const startDate = new Date(data.saleStartDate);
      // if (startDate < today) return false;
      
      const endDate = new Date(data.saleEndDate);
      if (endDate <= startDate) return false;
    }
    return true;
  }, {
    message: "Sale pricing requires valid start/end dates, sale price must be less than regular price, and start date cannot be in the past"
  })).min(1, "At least one variation is required"),
  relatedCategories: z.array(z.string()).optional(),
});
```

### Step 2: Product Info Form
**Route**: `/creators/products/add/product-info`

**Purpose**: Basic product information and media upload

**Key Features**:
- Product images upload (up to 5 images)
- Image reordering and main image selection
- Category selection with hierarchical navigation
- Highlights and tags management
- Rich form validation

**Implementation**:
```typescript
// Form Schema
const productInfoSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().min(1, "Brand is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  category: z.string().min(1, "Category is required"),
  gender: z.enum(['Men', 'Women', 'Unisex'], {
    required_error: "Please select a gender",
  }),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
});

// Image Upload Handling
const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
  const files = Array.from(e.target.files || []);
  if (files.length === 0) return;

  const remainingSlots = 5 - productImages.length;
  const filesToAdd = files.slice(0, remainingSlots);

  if (filesToAdd.length < files.length) {
    toast({
      variant: "destructive",
      title: "Too many images",
      description: "You can only upload up to 5 images total.",
    });
  }

  const newImages = [...productImages, ...filesToAdd];
  setProductImages(newImages);
  updateProductInfo({ productImages: newImages });

  // Create preview URLs
  const newPreviewUrls = filesToAdd.map(file => URL.createObjectURL(file));
  setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);
};
```

### Step 3: Specifications Form
**Route**: `/creators/products/add/specifications`

**Purpose**: Product specifications and attributes

**Key Features**:
- Material and fit type selection
- Season and occasion management
- Care instructions and origin
- Dynamic array management for seasons/occasions

**Implementation**:
```typescript
// Specifications Schema
const specificationsSchema = z.object({
  material: z.string().optional(),
  fitType: z.string().optional(),
  care: z.string().optional(),
  origin: z.string().optional(),
  season: z.array(z.string()).min(1, "At least one season is required"),
  occasion: z.array(z.string()).min(1, "At least one occasion is required"),
});

// Season Management
const addSeason = (seasonValue: string) => {
  const currentSeasons = watch('season') || [];
  if (seasonValue && !currentSeasons.includes(seasonValue)) {
    setValue('season', [...currentSeasons, seasonValue]);
  }
};

const removeSeason = (index: number) => {
  const currentSeasons = watch('season') || [];
  setValue('season', currentSeasons.filter((_, i) => i !== index));
};
```

## 🗂️ State Management

### ProductFormContext
**Purpose**: Manage state across multi-step form

```typescript
interface ProductFormContextType {
  // Form data
  productInfo: ProductInfoFormData;
  specifications: SpecificationsFormData;
  variations: VariationsFormData;
  
  // Update functions
  updateProductInfo: (data: Partial<ProductInfoFormData>) => void;
  updateSpecifications: (data: Partial<SpecificationsFormData>) => void;
  updateVariations: (data: Partial<VariationsFormData>) => void;
  
  // Utility functions
  resetForm: () => void;
  isFormValid: () => boolean;
}

export const ProductFormProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [productInfo, setProductInfo] = useState<ProductInfoFormData>(initialProductInfo);
  const [specifications, setSpecifications] = useState<SpecificationsFormData>(initialSpecifications);
  const [variations, setVariations] = useState<VariationsFormData>(initialVariations);

  const updateProductInfo = useCallback((data: Partial<ProductInfoFormData>) => {
    setProductInfo(prev => ({ ...prev, ...data }));
  }, []);

  const updateSpecifications = useCallback((data: Partial<SpecificationsFormData>) => {
    setSpecifications(prev => ({ ...prev, ...data }));
  }, []);

  const updateVariations = useCallback((data: Partial<VariationsFormData>) => {
    setVariations(prev => ({ ...prev, ...data }));
  }, []);

  const resetForm = useCallback(() => {
    setProductInfo(initialProductInfo);
    setSpecifications(initialSpecifications);
    setVariations(initialVariations);
  }, []);

  const isFormValid = useCallback(() => {
    return !!(
      productInfo.name &&
      productInfo.brand &&
      productInfo.description &&
      productInfo.basePrice > 0 &&
      productInfo.category &&
      productInfo.highlights.length > 0 &&
      productInfo.tags.length > 0 &&
      variations.variations.length > 0 &&
      specifications.season.length > 0 &&
      specifications.occasion.length > 0
    );
  }, [productInfo, specifications, variations]);

  return (
    <ProductFormContext.Provider value={{
      productInfo,
      specifications,
      variations,
      updateProductInfo,
      updateSpecifications,
      updateVariations,
      resetForm,
      isFormValid,
    }}>
      {children}
    </ProductFormContext.Provider>
  );
};
```

## 🔌 API Integration

### FormData Construction for Product Creation

**Key Implementation Details**:
```typescript
// lib/api/products.ts
export const createProduct = async (data: CreateProductData): Promise<CreateProductResponse> => {
  const formData = new FormData();

  // Add basic product information
  formData.append('name', data.name);
  formData.append('brand', data.brand);
  formData.append('description', data.description);
  formData.append('basePrice', data.basePrice.toString());
  formData.append('category', data.category);
  formData.append('gender', data.gender);

  // Add product images (field name must be 'productImages')
  data.productImages.forEach((image) => {
    formData.append('productImages', image);
  });

  // Add highlights array
  if (data.highlights && data.highlights.length > 0) {
    data.highlights.forEach(highlight => {
      formData.append('highlights[]', highlight);
    });
  }

  // Add tags array
  if (data.tags && data.tags.length > 0) {
    data.tags.forEach(tag => {
      formData.append('tags[]', tag);
    });
  }

  // Add variations using nested array notation
  if (data.variations && data.variations.length > 0) {
    data.variations.forEach((variation, index) => {
      formData.append(`variations[${index}][color]`, variation.color);
      formData.append(`variations[${index}][size]`, variation.size);
      formData.append(`variations[${index}][quantity]`, variation.quantity.toString());
      formData.append(`variations[${index}][price]`, variation.price.toString());

      if (variation.salePrice && variation.salePrice > 0) {
        formData.append(`variations[${index}][salePrice]`, variation.salePrice.toString());
      }
      if (variation.saleStartDate) {
        formData.append(`variations[${index}][saleStartDate]`, variation.saleStartDate);
      }
      if (variation.saleEndDate) {
        formData.append(`variations[${index}][saleEndDate]`, variation.saleEndDate);
      }
    });
  }

  // Add specifications using nested notation
  if (data.specifications) {
    Object.entries(data.specifications).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(item => {
          formData.append(`specifications[${key}][]`, item);
        });
      } else if (value !== undefined && value !== null && value !== '') {
        formData.append(`specifications[${key}]`, value.toString());
      }
    });
  }

  // Add related categories
  if (data.relatedCategories && data.relatedCategories.length > 0) {
    data.relatedCategories.forEach(categoryId => {
      formData.append('relatedCategories[]', categoryId);
    });
  }

  const response = await fetch(`${API_BASE_URL}/api/v1/creators/products`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      // Note: Don't set Content-Type for FormData
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};
```

### React Query Hooks

```typescript
// lib/hooks/use-products.ts

// Query keys for cache management
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: string) => [...productKeys.lists(), { filters }] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
};

// Get creator's products with pagination and filtering
export const useCreatorProducts = (params?: {
  page?: number;
  limit?: number;
  sort?: string;
  search?: string;
  status?: string;
  fields?: string;
}) => {
  return useQuery({
    queryKey: productKeys.list(JSON.stringify(params || {})),
    queryFn: () => productsApi.getCreatorProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Get single product for editing
export const useCreatorProduct = (productId: string) => {
  return useQuery({
    queryKey: [...productKeys.detail(productId), 'creator'],
    queryFn: () => productsApi.getCreatorProduct(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000,
    retry: (failureCount, error: any) => {
      if (error?.message?.includes('Session expired') || error?.message?.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Create product mutation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductData) => productsApi.createProduct(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      toast({
        title: 'Product Created Successfully',
        description: 'Your product has been created and is pending approval.',
        className: 'bg-green-100 text-green-800',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    },
  });
};
```

## 📊 Product Details Page Implementation

### Editable Sections Pattern

Each section of the product details page follows the same pattern:
1. **View Mode**: Display product information with edit button
2. **Edit Mode**: Show exact copy of add form with pre-populated data
3. **Form Submission**: Update product and return to view mode

### Key Implementation Features

**Safe Data Population from API Response:**
```typescript
// Handle both populated and unpopulated category objects
category: product.category?._id || "", // Extract ID from populated object

// Safe array data extraction
const getSafeArrayData = (data: any): string[] => {
  if (!data) return [];
  if (Array.isArray(data)) {
    return data.map((item: any) => {
      if (typeof item === 'object' && item !== null) {
        return item.name || item.value || String(item);
      }
      return String(item);
    });
  }
  return [];
};

// Safe variations conversion
const getFormVariations = () => {
  if (product.variations && product.variations.length > 0) {
    return product.variations.map((variation: any) => ({
      color: variation.color || "",
      size: variation.size || "",
      quantity: variation.quantity || 1,
      price: variation.price || 0,
      salePrice: variation.salePrice || undefined,
      saleStartDate: variation.saleStartDate || "",
      saleEndDate: variation.saleEndDate || "",
    }));
  }
  return [{ /* default variation */ }];
};
```

**Professional View Mode Display:**
```typescript
// View mode with proper data display
return (
  <div className="bg-white rounded-md shadow-sm p-4">
    <div className="flex justify-between items-center mb-4">
      <h3 className="font-semibold text-gray-700">Product Information</h3>
      <Button variant="ghost" onClick={toggleEdit}>
        <Edit3 className="h-5 w-5" />
      </Button>
    </div>

    <section className="space-y-3">
      <DetailItem label="Product Name" value={product.name} />
      <DetailItem label="Category" value={product.category?.name} />
      <DetailItem label="Brand" value={product.brand} />
      <DetailItem label="Gender" value={product.gender} />
      <DetailItem label="Base Price" value={`GHS ${product.basePrice}`} />

      {/* Array data with badges */}
      <div className="text-sm">
        <p className="text-gray-500">Highlights</p>
        {product.highlights && product.highlights.length > 0 ? (
          <div className="flex flex-wrap gap-2 mt-1">
            {product.highlights.map((highlight: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {highlight}
              </Badge>
            ))}
          </div>
        ) : (
          <p className="text-gray-400 italic">No highlights available</p>
        )}
      </div>
    </section>
  </div>
);
```

**Edit Mode with Exact Form Copy:**
```typescript
// Edit mode using exact form structure
if (isEditing) {
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-bold">Product Information</h2>
        <Button variant="ghost" onClick={toggleEdit}>
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Exact copy of ProductInfoForm fields */}
      <Controller
        name="name"
        control={control}
        render={({ field }) => (
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Product Name <span className="text-red-500">*</span>
            </label>
            <Input
              {...field}
              placeholder="Enter product name"
              className={errors.name ? 'border-red-300' : ''}
            />
            {errors.name && (
              <p className="text-xs text-red-600 mt-1">{errors.name.message}</p>
            )}
          </div>
        )}
      />

      {/* More form fields... */}
    </form>
  );
}
```

## 🎯 Best Practices Implemented

### 1. Data Safety
- **Null/Undefined Checks**: All data access includes fallback values
- **Type Safety**: Proper TypeScript interfaces for all data structures
- **Object vs String Handling**: Safe extraction of IDs from populated objects
- **Array Safety**: Proper handling of array fields with fallbacks

### 2. Form Consistency
- **Exact Form Copies**: Edit forms are identical to add forms
- **Pre-populated Data**: All fields populated from API response
- **Validation**: Same Zod schemas used across add and edit flows
- **Error Handling**: Consistent error display and handling

### 3. User Experience
- **Professional UI**: Clean, consistent design across all components
- **Loading States**: Proper loading indicators during API calls
- **Error Feedback**: User-friendly error messages and toast notifications
- **Responsive Design**: Works perfectly on all screen sizes

### 4. Performance
- **React Query Caching**: Efficient data caching and invalidation
- **Optimistic Updates**: Immediate UI updates with rollback on error
- **Lazy Loading**: Components loaded only when needed
- **Memory Management**: Proper cleanup of event listeners and subscriptions

## 🚀 Integration Checklist

When implementing similar features, ensure:

- [ ] **TypeScript Interfaces**: Define proper types for all data structures
- [ ] **API Client**: Implement CRUD operations with proper error handling
- [ ] **React Query Hooks**: Create hooks for data fetching and mutations
- [ ] **Form Schemas**: Define Zod validation schemas
- [ ] **Form Components**: Implement forms with proper validation
- [ ] **List Components**: Create list views with pagination and filtering
- [ ] **Detail Components**: Implement detail views with edit capabilities
- [ ] **Route Protection**: Ensure proper authentication and authorization
- [ ] **Error Handling**: Implement comprehensive error handling
- [ ] **Testing**: Write tests for all components and hooks
- [ ] **Documentation**: Document all APIs and components

This implementation provides a robust, scalable foundation for product management in the EveryFash platform! 🎉
