import Link from 'next/link';

interface OrderDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function OrderDetailsPage({ params }: OrderDetailsPageProps) {
    const {id} = await params;
  // Simulated order data (You can fetch from an API here)
  const order = {
    id,
    orderNumber: '35554455',
    placedOn: '18-02-2024',
    total: 'GHS 150.00',
    items: [
      {
        id: '1',
        productName: 'Elegant Dress',
        size: 'M',
        price: 'GHS 150.00',
        image: '/images/ladies/dress1.jpg',
      },
      {
        id: '2',
        productName: 'Underwear',
        size: '42',
        price: 'GHS 250.00',
        image: '/images/ladies/underwear.jpg',
      },
    ],
    deliveryDate: '21-02-2024',
    paymentMethod: 'Card',
    paymentDetails: {
      itemsTotal: 'GHS 150.00',
      deliveryFees: 'GHS 8.06',
      total: 'GHS 158.06',
    },
    deliveryMethod: 'Everyfash Express',
    shippingAddress: {
      name: '<PERSON>',
      address: '123 Accra Street, Ghana',
    },
    shippingDetails: 'Door Delivery Fulfilled by Everyfash',
  };

  return (
    <section className="w-full mt-16 py-4">
      {/* Order Summary */}
      <div className="bg-white rounded shadow-sm p-3 mb-4">
        <h1 className="font-semibold text-gray-600">Order #{order.orderNumber}</h1>
        <p className='text-gray-600 text-sm'>{order.items.length} Items</p>
        <p className='text-gray-600 text-sm'>Placed on {order.placedOn}</p>
        <p className=" text-sm text-gray-600 font-semibold">
          Total: {order.total}
        </p>
      </div>

      {/* Items in Your Order */}
      <h2 className="font-medium text-gray-600 uppercase text-sm mb-1">Items in Your Order</h2>

      <div className="bg-white rounded shadow-sm p-4 mb-4 ">
        {/* Delivered Badge */}
        <div className="w-fit bg-green-600 text-white text-xs font-bold px-2 py-1 rounded">
          Delivered
        </div>
        <p className="text-sm font-medium text-gray-600 mb-3 mt-1">On {order.deliveryDate}</p>

        {order.items.map((product) => (
          <div
            key={product.id}
            className="bg-white border rounded p-3 mb-3 flex flex-col"
          >
            <div className="flex">
              {/* Product Image */}
              <div className="w-24 h-24 bg-gray-200">
                <img
                  src={product.image}
                  alt={product.productName}
                  className="w-full h-full object-cover rounded"
                />
              </div>

              {/* Product Details */}
              <div className="ml-4 flex-1">
                <h2 className="font-medium text-gray-800">{product.productName}</h2>
                <p className="text-sm text-gray-600">Size: {product.size}</p>
                <p className="text-sm font-medium text-gray-600">{product.price}</p>
              </div>
            </div>

            {/* Status History */}
            <div className="flex border-t items-center mt-3 justify-center">
              <Link
                href={`/orders/${id}/track`}
                className="text-primary font-medium text-sm hover:text-primary/80 hover:underline pt-1 transition-colors duration-200"
              >
                See Status History
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Payment Information */}
      <h2 className="font-medium text-gray-600 uppercase text-sm mb-1">Payment Information</h2>
      <div className="bg-white rounded shadow-sm p-3 mb-4">
        <p className="text-sm font-semibold text-gray-700">Payment Method</p>
        <p className="text-sm text-gray-600 mb-3 border-b pb-2">{order.paymentMethod}</p>

        <p className="text-sm font-semibold text-gray-700">Payment Details</p>
        <div className="text-sm text-gray-600 mt-1">
          <p>Items Total: {order.paymentDetails.itemsTotal}</p>
          <p>Delivery Fees: {order.paymentDetails.deliveryFees}</p>
          <p className="text-gray-800 mt-1">Total: {order.paymentDetails.total}</p>
        </div>
      </div>

      {/* Delivery Information */}
      <h2 className="font-medium text-gray-600 uppercase text-sm mb-1">Delivery Information</h2>
      <div className="bg-white rounded shadow-sm p-3 mb-4">
        <p className="text-sm font-semibold text-gray-700">Delivery Method</p>
        <p className="text-sm text-gray-600 mb-3 border-b pb-2">{order.deliveryMethod}</p>

        <p className="text-sm font-semibold text-gray-700">Shipping Address</p>
        <p className="text-sm text-gray-600">{order.shippingAddress.name}</p>
        <p className="text-sm text-gray-600 mb-3 border-b pb-2">{order.shippingAddress.address}</p>

        <p className="text-sm font-semibold text-gray-700">Shipping Details</p>
        <p className="text-sm text-gray-600">{order.shippingDetails}</p>
        <p className="text-sm text-gray-600">Delivery on {order.deliveryDate}</p>
      </div>
    </section>
  );
}
