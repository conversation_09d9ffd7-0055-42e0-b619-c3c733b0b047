import type { Metada<PERSON> } from "next";
import { Toaster } from "@/components/ui/toaster";
import { ProductFormProvider } from "@/lib/contexts/ProductFormContext";


export const metadata: Metadata = {
  title: "Everyfash Vendors",
  description: "Largest Fashion marketplace",
};

export default function AddProductLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProductFormProvider>
      <main className="w-full min-h-screen">
        {children}
        <Toaster />
      </main>
    </ProductFormProvider>
  );
}
