"use client"
import { IoCheckmarkCircle } from 'react-icons/io5';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

const OrderConfirmationPage = () => {
  const router = useRouter();
  const orderNumber = '544556';
  const deliveryDates = '21 February and 22nd February';
  const deliveryMethod = 'Door Delivery';
  const paymentMethod = 'Mobile Money on Delivery';

  return (
    <div className="mt-20 w-full">
      <div className=" ">
        {/* Success Icon & Message */}
        <div className="flex items-center gap-4">
          <IoCheckmarkCircle className="text-green-500" size={60} />
          <div>
            <h1 className=" font-medium text-gray-800">Thank you for placing an order on Everyfash.</h1>
            <p className="text-sm text-gray-600">Order No. <span className="font-medium">{orderNumber}</span></p>
          </div>
        </div>

        {/* Delivery Information */}
        <div className="bg-white mt-3 px-2 py-2 rounded-lg space-y-1">
          <h3 className="font-medium text-sm text-gray-800">{deliveryMethod}</h3>
          <p className="text-sm text-gray-600">Fulfilled by Everyfash</p>
          <p className="text-sm text-gray-600">Delivery between <span className="font-medium">{deliveryDates}</span></p>
        </div>

        {/* Payment Information */}
        <div className="bg-white p-2 mb-4 mt-2 rounded-lg ">
          <h3 className="font-medium text-sm text-gray-800">{paymentMethod}</h3>
          <p className="text-sm text-gray-600">Ensure you have enough cash balance in your mobile money wallet to ease the transaction.</p>
        </div>

        {/* CTA */}
        <Button onClick={()=>{router.push('/orders/5')}} className="bg-primary text-primary-foreground text-sm w-full hover:bg-primary/90 transition-colors duration-200">See Order Details</Button>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
