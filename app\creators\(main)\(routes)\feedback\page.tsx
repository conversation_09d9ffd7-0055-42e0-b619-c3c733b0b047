"use client";
import { useState } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON>pp, FaTelegramPlane, FaEnvelope, FaPhoneAlt } from "react-icons/fa";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

export default function FeedbackPage() {
  const [message, setMessage] = useState("");

  const prefilledMessage = encodeURIComponent(
    "Hello, I’d like to share feedback or report an issue regarding my shop."
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message) {
      toast({
        title: "Incomplete!",
        description: "Please enter your message!"
      });
      return;
    }

    console.log({ message });
    toast({
      title: "Success!",
      description: "Your feedback has been sent! Thank you."
    });
    setMessage("");
  };

  return (
    <section className="py-6 px-2 w-full">
      {/* Page Heading */}
      <h1 className="text-sm font-bold uppercase text-gray-600 mb-2">
        Customer Support & Feedback
      </h1>

      {/* Feedback Form */}
      <div className="bg-white rounded shadow-sm p-4 mb-6 w-full">
        <h2 className="text-sm font-semibold text-gray-700 mb-3">
          Send Us a Message
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Textarea
            placeholder="Describe your issue or feedback..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full h-24"
          />
          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary/90 text-white text-sm  py-2 rounded-md"
          >
            Send Feedback
          </Button>
        </form>
      </div>

      {/* Quick Contact Options */}
      <h2 className="text-sm font-bold uppercase text-gray-600 mb-2">
        Other Ways to Reach Us
      </h2>
      <div className="bg-white rounded shadow-sm p-4">
        {/* WhatsApp */}
        <div className="space-y-2 mb-4">
          <p className="text-sm font-semibold text-gray-700">WhatsApp Support</p>
          <a
            href={`https://wa.me/233508996091?text=${prefilledMessage}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-sm text-green-700 hover:underline"
          >
            <FaWhatsapp size={16} />
            <span>Support Line 1: +233 50 899 6091</span>
          </a>
          <a
            href={`https://wa.me/233202069074?text=${prefilledMessage}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-sm text-green-700 hover:underline"
          >
            <FaWhatsapp size={16} />
            <span>Support Line 2: +233 20 206 9074</span>
          </a>
        </div>

        {/* Telegram */}
        <div className="space-y-2 mb-4">
          <p className="text-sm font-semibold text-gray-700">Telegram Support</p>
          <a
            href={`https://t.me/Kanjay01`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-sm text-blue-700 hover:underline"
          >
            <FaTelegramPlane size={16} />
            <span>@Everyfash</span>
          </a>
        </div>

        {/* Email */}
        <div className="space-y-2 mb-4">
          <p className="text-sm font-semibold text-gray-700">Email Support</p>
          <a
            href="mailto:<EMAIL>"
            className="flex items-center space-x-2 text-sm text-yellow-700 hover:underline"
          >
            <FaEnvelope size={16} />
            <span><EMAIL></span>
          </a>
        </div>

        {/* Call Support */}
        <div className="space-y-2">
          <p className="text-sm font-semibold text-gray-700">Call Support</p>
          <a
            href="tel:+1234567890"
            className="flex items-center space-x-2 text-sm text-gray-700 hover:underline"
          >
            <FaPhoneAlt size={16} />
            <span>Support Line 1: +233 50 899 6091</span>
          </a>
          <a
            href="tel:+0987654321"
            className="flex items-center space-x-2 text-sm text-gray-700 hover:underline"
          >
            <FaPhoneAlt size={16} />
            <span>Support Line 2: +233 20 206 9074</span>
          </a>
        </div>
      </div>
    </section>
  );
}
