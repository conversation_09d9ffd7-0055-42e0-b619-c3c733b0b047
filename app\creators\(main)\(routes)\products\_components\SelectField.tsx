import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
interface SelectFieldProps<T extends Record<string, any>> {
    label: string;
    name: keyof T; // Ensures name is a valid key of SpecificationsFormData
    value: string;
    onChange: (field: keyof T, value: string) => void;
    options: { value: string; label: string }[];
  }
  
 function SelectField<T extends Record<string, any>>({
    label,
    name,
    value,
    onChange,
    options,
  }: SelectFieldProps<T>) {
    return (
      <div>
        <label className="block text-sm font-medium mb-1">{label}</label>
        <Select onValueChange={(selectedValue) => onChange(name, selectedValue)}>
          <SelectTrigger>
            <SelectValue placeholder="Select an option" />
          </SelectTrigger>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }
  

  export default SelectField