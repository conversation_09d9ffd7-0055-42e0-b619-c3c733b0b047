import { Input } from "@/components/ui/input";

export const InputField = ({ label,type, name, value, onChange, required = false, disabled = false }: any) => (
    <div>
      <label className="text-sm text-gray-700">{label}{required && <span className="text-red-500">*</span>}</label>
      <Input
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        disabled={disabled}
        placeholder={label}
        className={`w-full border rounded-lg p-2 mt-1 ${disabled ? "bg-gray-100 text-gray-400" : ""}`}
      />
    </div>
  );