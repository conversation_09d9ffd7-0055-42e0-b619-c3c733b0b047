'use client';

import { But<PERSON> } from "@/components/ui/button";
import CartItem from "../../_components/CartItem";
import WishListComponent from "../../_components/WislistComponent";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/stores/auth-store";
import AuthPrompt from "@/components/ui/custom/AuthPrompt";



const cartProducts = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    name: `Product ${i + 1}`,
    price: (Math.random() * 100).toFixed(2),
    discount: Math.floor(Math.random() * 30) + 5,
    size: 'XL',
    shop: `Shop ${i + 1}`,
    image: `/images/ladies/dress1.jpg`,
    available: Math.random() > 0.3,
    stockLeft: Math.floor(Math.random() * 10) + 1, // Random stock count
  }));

const CartPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  // Show auth prompt if not authenticated
  if (!isAuthenticated) {
    return <AuthPrompt type="cart" />;
  }

  const subtotal = cartProducts.reduce(
        (acc, product) => acc + Number(product.price),
        0
      );

  return (
    <section className="w-full mt-20 py-4">
      <h1 className="text-sm uppercase mb-2 font-semibold text-gray-700">Cart Summary</h1>
      {/* SubTotal */}
      <section className="bg-white py-3 px-3 rounded shadow-sm mb-4">
        {/* Sub Total and amount */}
        <div className="flex justify-between items-center pb-2">
          <h1 className="text-sm font-semibold text-gray-700">Subtotal</h1>
          <h1 className="font-bold text-gray-800">GH₵ {subtotal.toFixed(2)}</h1>
        </div>

        {/* Seller Score & Followers */}
        <div className=" flex justify-between items-center text-sm">
          <div className="text-gray-600">
            <p>
              Delivery fees not included yet
            </p>
          </div>
        </div>

      </section>

       {/* Cart Items */}
       <section className="w-full">
        <h1 className="text-sm uppercase mb-2 font-semibold text-gray-700">CART (8)</h1>
            {cartProducts.map((product) => (
            <CartItem key={product.id} product={product} />
            ))}
       </section>

       {/* Checkout Button - Positioned above the bottom navigation */}
       <section className="sticky bottom-24 z-10 w-full bg-white border-t py-2 px-4 mb-4">
         <Button
           onClick={() => router.push('/checkout')}
           className="h-10 w-full bg-primary hover:bg-primary/90 text-primary-foreground flex items-center justify-center gap-4 transition-colors duration-200"
         >
           Checkout (GH₵ {subtotal.toFixed(2)})
         </Button>
       </section>

       <WishListComponent />

       {/* Added padding at the bottom to prevent content from being hidden behind the bottom navigation */}
       <div className="h-16"></div>

    </section>
  );
};

export default CartPage;
