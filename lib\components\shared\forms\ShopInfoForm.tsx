'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Upload } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';

import { useShopInfo, useUpdateShopInfo } from '@/lib/hooks/use-onboarding';
import { useShopInfoProfile, useUpdateShopInfoProfile } from '@/lib/hooks/useProfile';
import { ShopInfoUpdateData } from '@/lib/types/auth';
import { toast } from '@/hooks/use-toast';
import { getApiErrorMessage, getApiErrorTitle } from '@/lib/utils/error-utils';
import {
  FormLayout,
  FormSection,
  FormInput,
  FormSelect,
  FormTextarea,
  FormActions
} from './FormComponents';

// Validation schema (based on API documentation)
const shopInfoSchema = z.object({
  // Required fields per API docs
  name: z.string().min(1, 'Shop name is required'),
  sellerType: z.enum(['individual', 'business', 'both'], {
    required_error: 'Please select a seller type',
  }),
  // Required contact information
  contactName: z.string().min(1, 'Contact name is required'),
  contactEmail: z.string().email('Please enter a valid email address'),
  contactPhone: z.string().min(1, 'Contact phone is required'),
  // Optional fields
  description: z.string().optional(),
  // Social media (optional)
  instagram: z.string().optional(),
  facebook: z.string().optional(),
  twitter: z.string().optional(),
  tiktok: z.string().optional(),
  youtube: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
});

type ShopInfoFormData = z.infer<typeof shopInfoSchema>;

interface ShopInfoFormProps {
  onSuccess?: () => void;
  onBack?: () => void;
  isProfileMode?: boolean;
}

const ShopInfoForm = ({ onSuccess, onBack, isProfileMode = false }: ShopInfoFormProps) => {
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [existingLogoUrl, setExistingLogoUrl] = useState<string | null>(null);
  const [existingBannerUrl, setExistingBannerUrl] = useState<string | null>(null);

  // Use different hooks based on mode
  const { data: shopInfoResponse, isLoading: isLoadingShopInfo } = isProfileMode
    ? useShopInfoProfile()
    : useShopInfo();
  const updateShopInfoMutation = isProfileMode
    ? useUpdateShopInfoProfile()
    : useUpdateShopInfo();

  const form = useForm<ShopInfoFormData>({
    resolver: zodResolver(shopInfoSchema),
    mode: 'onChange', // Validate on change for better UX
    defaultValues: {
      name: '',
      description: '',
      sellerType: 'individual',
      contactName: '',
      contactEmail: '',
      contactPhone: '',
      instagram: '',
      facebook: '',
      twitter: '',
      tiktok: '',
      youtube: '',
      website: '',
    },
  });

  const { control, handleSubmit, formState: { errors }, reset } = form;

  // Update form when data loads
  useEffect(() => {
    if (shopInfoResponse?.data) {
      const { shopInfo, socialMedia } = shopInfoResponse.data;

      // Prepare comprehensive form data
      const formData = {
        // Basic shop info
        name: shopInfo?.name || '',
        description: shopInfo?.description || '',
        sellerType: (shopInfo?.sellerType || 'individual') as 'individual' | 'business' | 'both',

        // Contact info
        contactName: shopInfo?.contact?.name || '',
        contactEmail: shopInfo?.contact?.email || '',
        contactPhone: shopInfo?.contact?.phone || '',

        // Social media
        instagram: socialMedia?.instagram || '',
        facebook: socialMedia?.facebook || '',
        twitter: socialMedia?.twitter || '',
        tiktok: socialMedia?.tiktok || '',
        youtube: socialMedia?.youtube || '',
        website: socialMedia?.website || '',
      };

      // Reset the entire form with the new data - add small delay to ensure components are mounted
      setTimeout(() => {
        reset(formData);
      }, 50);

      // Set existing logo and banner URLs
      setExistingLogoUrl(shopInfo?.logo || null);
      setExistingBannerUrl(shopInfo?.banner || null);
    }
  }, [shopInfoResponse, reset]);

  const onSubmit = async (data: ShopInfoFormData) => {
    try {
      // Check for validation errors and show toast if any
      const errorKeys = Object.keys(errors);
      if (errorKeys.length > 0) {
        const errorMessages = errorKeys.map(key => {
          const error = errors[key as keyof ShopInfoFormData];
          return error?.message;
        }).filter(Boolean);

        toast({
          variant: 'destructive',
          title: 'Form Validation Error',
          description: errorMessages.length > 1
            ? `Please fix ${errorMessages.length} validation errors: ${errorMessages[0]} and ${errorMessages.length - 1} more.`
            : errorMessages[0] || 'Please fill in all required fields correctly.',
        });
        return;
      }

      const updateData: ShopInfoUpdateData = {
        // Required fields
        name: data.name.trim(),
        sellerType: data.sellerType,
        contact: {
          name: data.contactName.trim(),
          email: data.contactEmail.trim(),
          phone: data.contactPhone.trim(),
        },
        // Optional fields
        description: data.description?.trim(),
        socialMedia: {
          instagram: data.instagram?.trim() || undefined,
          facebook: data.facebook?.trim() || undefined,
          twitter: data.twitter?.trim() || undefined,
          tiktok: data.tiktok?.trim() || undefined,
          youtube: data.youtube?.trim() || undefined,
          website: data.website?.trim() || undefined,
        },
      };

      // Add files if selected
      if (logoFile) updateData.logo = logoFile;
      if (bannerFile) updateData.banner = bannerFile;

      await updateShopInfoMutation.mutateAsync(updateData);
      onSuccess?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    }
  };

  if (isLoadingShopInfo) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <FormLayout>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-4">
        {/* Basic Information */}
        <FormSection
          title="Basic Information"
          description="Essential details about your shop"
        >
          <div className="space-y-4">
            <FormInput
              label="Shop Name"
              name="name"
              control={control}
              errors={errors}
              placeholder="Enter your shop name"
              required
              disabled={updateShopInfoMutation.isPending}
            />

            <FormTextarea
              label="Shop Description"
              name="description"
              control={control}
              errors={errors}
              placeholder="Describe what your shop offers..."
              rows={4}
            />

            <FormSelect
              label="Seller Type"
              name="sellerType"
              control={control}
              errors={errors}
              placeholder="Select seller type"
              required
              disabled={updateShopInfoMutation.isPending}
              options={[
                { value: 'individual', label: 'Individual' },
                { value: 'business', label: 'Business' },
                { value: 'both', label: 'Both' }
              ]}
            />
          </div>
        </FormSection>

        {/* Contact Information */}
        <FormSection
          title="Contact Information"
          description="How customers can reach you"
        >
          <div className="space-y-4">
            <FormInput
              label="Contact Name"
              name="contactName"
              control={control}
              errors={errors}
              placeholder="Contact person name"
              required
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="Contact Email"
              name="contactEmail"
              control={control}
              errors={errors}
              placeholder="<EMAIL>"
              type="email"
              required
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="Contact Phone"
              name="contactPhone"
              control={control}
              errors={errors}
              placeholder="+************"
              required
              disabled={updateShopInfoMutation.isPending}
            />
          </div>
        </FormSection>

        {/* Social Media */}
        <FormSection
          title="Social Media & Website"
          description="Connect your social media accounts (optional)"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Instagram"
              name="instagram"
              control={control}
              errors={errors}
              placeholder="@yourshop"
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="Facebook"
              name="facebook"
              control={control}
              errors={errors}
              placeholder="facebook.com/yourshop"
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="Twitter"
              name="twitter"
              control={control}
              errors={errors}
              placeholder="@yourshop"
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="TikTok"
              name="tiktok"
              control={control}
              errors={errors}
              placeholder="@yourshop"
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="YouTube"
              name="youtube"
              control={control}
              errors={errors}
              placeholder="youtube.com/yourshop"
              disabled={updateShopInfoMutation.isPending}
            />

            <FormInput
              label="Website"
              name="website"
              control={control}
              errors={errors}
              placeholder="https://yourshop.com"
              disabled={updateShopInfoMutation.isPending}
            />
          </div>
        </FormSection>

        {/* File Uploads */}
        <FormSection
          title="Shop Branding"
          description="Upload your shop logo and banner"
        >
          <div className="space-y-6">
            {/* Logo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shop Logo
              </label>

              {/* Show existing logo if available */}
              {existingLogoUrl && !logoFile && (
                <div className="mb-3 p-3 bg-primary/5 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <img
                        src={existingLogoUrl}
                        alt="Current logo"
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div>
                        <p className="text-sm font-medium text-primary-700">Current Logo</p>
                        <a
                          href={existingLogoUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-primary hover:text-primary/80 underline"
                        >
                          View full size
                        </a>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => setExistingLogoUrl(null)}
                      className="text-red-500 hover:text-red-700 text-sm"
                      disabled={updateShopInfoMutation.isPending}
                    >
                      Remove current logo
                    </button>
                  </div>
                </div>
              )}

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) setLogoFile(file);
                  }}
                  className="hidden"
                  id="logo-upload"
                  disabled={updateShopInfoMutation.isPending}
                />
                <label
                  htmlFor="logo-upload"
                  className="cursor-pointer flex flex-col items-center justify-center space-y-2"
                >
                  <Upload className="h-8 w-8 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {logoFile ? logoFile.name : existingLogoUrl ? 'Click to upload new logo' : 'Click to upload logo'}
                  </span>
                </label>
                {logoFile && (
                  <button
                    type="button"
                    onClick={() => setLogoFile(null)}
                    className="mt-2 text-red-500 hover:text-red-700 text-sm"
                    disabled={updateShopInfoMutation.isPending}
                  >
                    Remove new logo
                  </button>
                )}
              </div>
            </div>

            {/* Banner Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shop Banner
              </label>

              {/* Show existing banner if available */}
              {existingBannerUrl && !bannerFile && (
                <div className="mb-3 p-3 bg-primary/5 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <img
                        src={existingBannerUrl}
                        alt="Current banner"
                        className="w-16 h-10 object-cover rounded"
                      />
                      <div>
                        <p className="text-sm font-medium text-primary-700">Current Banner</p>
                        <a
                          href={existingBannerUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-primary hover:text-primary/80 underline"
                        >
                          View full size
                        </a>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => setExistingBannerUrl(null)}
                      className="text-red-500 hover:text-red-700 text-sm"
                      disabled={updateShopInfoMutation.isPending}
                    >
                      Remove current banner
                    </button>
                  </div>
                </div>
              )}

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) setBannerFile(file);
                  }}
                  className="hidden"
                  id="banner-upload"
                  disabled={updateShopInfoMutation.isPending}
                />
                <label
                  htmlFor="banner-upload"
                  className="cursor-pointer flex flex-col items-center justify-center space-y-2"
                >
                  <Upload className="h-8 w-8 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {bannerFile ? bannerFile.name : existingBannerUrl ? 'Click to upload new banner' : 'Click to upload banner'}
                  </span>
                </label>
                {bannerFile && (
                  <button
                    type="button"
                    onClick={() => setBannerFile(null)}
                    className="mt-2 text-red-500 hover:text-red-700 text-sm"
                    disabled={updateShopInfoMutation.isPending}
                  >
                    Remove new banner
                  </button>
                )}
              </div>
            </div>
          </div>
        </FormSection>

        {/* Form Actions */}
        <FormActions>
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={updateShopInfoMutation.isPending}
          >
            {updateShopInfoMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save & Continue'
            )}
          </Button>
        </FormActions>
      </form>
    </FormLayout>
  );
};

export default ShopInfoForm;
