'use client';

import { creatorMenuLinks } from '@/lib/helpers/menulinks';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import React, { useEffect } from 'react';
import { useAuth } from '@/lib/stores/auth-store';
import { useLogout } from '@/lib/hooks/use-auth';
import { useRouter } from 'next/navigation';
import { MdLogout } from 'react-icons/md';



const MenuPage = () => {
  const { user, isAuthenticated, isCreator } = useAuth();
  const logoutMutation = useLogout();
  const router = useRouter();

  // Redirect if not authenticated or not a creator
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/creators/login');
    } else if (!isCreator) {
      router.push('/login');
    }
  }, [isAuthenticated, isCreator, router]);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // Show loading or redirect if not authenticated
  if (!isAuthenticated || !user || !isCreator) {
    return (
      <div className="w-full max-w-lg mx-auto mt-4 py-4 text-center">
        <p>Redirecting...</p>
      </div>
    );
  }

  return (
    <section className="w-full max-w-lg mx-auto mt-4 py-4">
      {/* User Info Section */}
      <div className="bg-white px-4 py-6 rounded-lg shadow-sm mb-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary/70 text-primary-foreground rounded-full flex items-center justify-center text-xl font-semibold mx-auto mb-3">
            {user.name
              .split(' ')
              .map(word => word.charAt(0))
              .join('')
              .toUpperCase()
              .slice(0, 2)}
          </div>
          <h1 className="text-lg font-semibold text-gray-800">{user.name}</h1>
          <p className="text-sm text-gray-600">{user.email}</p>
          <p className="text-xs text-gray-500 capitalize mt-1">Creator Account</p>
        </div>
      </div>

      {/* Header */}
      <h2 className="text-sm uppercase font-bold text-gray-600 mb-3">More Options</h2>

      {/* Menu list */}
      <div className="bg-white  py-4 rounded-lg shadow-sm">
        {creatorMenuLinks.map((item) => (
          <Link href={item.link} key={item.label} className="block hover:bg-primary/10 px-4 rounded-md transition-colors duration-200">
            <div className="flex justify-between items-center py-4 border-b last:border-b-0">
              <div className="flex items-center gap-3">
                {item.icon}
                <p className="  text-sm font-medium">{item.label}</p>
              </div>
              <ChevronRight size={18} className="text-gray-500" />
            </div>
          </Link>
        ))}
      </div>

      {/* Logout button */}
      <div className="text-center flex justify-center w-full mt-6">
        <button
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
          className="text-red-600 hover:text-red-700 uppercase font-semibold flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <MdLogout size={18} />
          {logoutMutation.isPending ? 'Logging out...' : 'Logout'}
        </button>
      </div>
    </section>
  );
};

export default MenuPage;
