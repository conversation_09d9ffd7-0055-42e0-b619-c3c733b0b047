import { Check } from "lucide-react";


interface TrackOrderPageProps {
params: Promise<{
    id: string;
}>;
}
  
export default async function TrackOrderPage({ params }: TrackOrderPageProps) {
const { id } = await params;

// Simulating order tracking progress (replace this with an API call later)
const trackingProgress = [
    { status: "Order Placed", date: "10-Feb-2025", completed: true },
    { status: "Pending Confirmation", date: "11-Feb-2025", completed: true },
    { status: "Order Confirmed", date: "11-Feb-2025", completed: true },
    { status: "Waiting to be Shipped", date: "13-Feb-2025", completed: true },
    { status: "Shipped", date: "15-Feb-2025", completed: true },
    { status: "Out For Delivery", date: "17-Feb-2025", completed: false },
    { status: "Delivered", date: "19-Feb-2025", completed: false },
  ];

const isDelivered = trackingProgress.some(
    (step) => step.status === 'Delivered' && step.completed
);

return (
    <section className="w-full max-w-lg mx-auto mt-20 py-4">
    <h1 className="font-medium text-gray-600 uppercase text-sm mb-1">Track Your Order</h1>


    {/* Tracking Progress */}
    <div className="bg-white px-2 py-4 rounded shadow-sm">

        <div className="relative">
        {trackingProgress.map((step, index) => (
            <div key={index} className="flex items-start mb-6 last:mb-0 relative">
            {/* Vertical Line */}
            {index !== trackingProgress.length - 1 && (
                <div
                className={`absolute left-3 top-5 w-[2px] h-full ${
                    step.completed ? 'bg-green-500' : 'bg-gray-300'
                }`}
                />
            )}

            {/* Progress Circle */}
            <div
                className={`w-6 h-6 flex items-center justify-center rounded-full ${
                step.completed ? 'bg-green-500' : 'bg-gray-300'
                }`}
            >
                {step.completed ? (
                <span className="text-white text-sm font-bold"><Check size={16}/></span>
                ) : (
                <span className="text-gray-700 text-xs">•</span>
                )}
            </div>

            {/* Status & Date */}
            <div className="ml-4">
                <p
                className={`text-sm  font-medium ${
                    step.completed ? 'text-green-600' : 'text-gray-700'
                }`}
                >
                {step.status}
                </p>
                <p className="text-xs text-gray-500">{step.date}</p>
            </div>
            </div>
        ))}
        </div>

        {/* Final Delivery Note */}
        {!isDelivered && (
        <div className="mt-6 bg-gray-50 p-3 rounded text-sm text-gray-600 border">
            Your item/order will be delivered soon. We’ll notify you when it’s delivered.
        </div>
        )}

        {isDelivered && (
        <div className="mt-6 bg-green-50 p-3 rounded text-sm text-green-600 border border-green-200">
            Your item/order has been successfully delivered.
        </div>
        )}
    </div>
    </section>
);
}

