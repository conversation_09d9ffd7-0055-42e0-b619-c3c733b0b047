'use client';

import React from 'react';
import <PERSON><PERSON> from 'lottie-react';
import { cn } from '@/lib/utils';
import loaderAnimation from '../../loader.json';

interface LottieLoaderProps {
  /**
   * Size of the loader
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /**
   * Custom className for additional styling
   */
  className?: string;
  /**
   * Loading text to display below the animation
   */
  text?: string;
  /**
   * Text size
   * @default 'md'
   */
  textSize?: 'sm' | 'md' | 'lg';
  /**
   * Whether to show the animation in a centered container
   * @default false
   */
  centered?: boolean;

  /**
   * Whether to loop the animation
   * @default true
   */
  loop?: boolean;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-30 h-30',
  xl: 'w-36 h-36',
  '2xl': 'w-40 h-40',
};

const textSizeClasses = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
};

const LottieLoader: React.FC<LottieLoaderProps> = ({
  size = 'md',
  className,
  text,
  textSize = 'md',
  centered = false,
  loop = true,
}) => {
  const loaderContent = (
    <div className={cn('flex flex-col items-center justify-center space-y-2 w-full', className)}>
      <div className={cn(sizeClasses[size], 'mx-auto')}>
        <Lottie
          animationData={loaderAnimation}
          loop={loop}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
      {text && (
        <p className={cn('text-gray-600 text-center', textSizeClasses[textSize])}>
          {text}
        </p>
      )}
    </div>
  );

  if (centered) {
    return (
      <div className="flex items-center justify-center w-full">
        {loaderContent}
      </div>
    );
  }

  return loaderContent;
};

export default LottieLoader;
