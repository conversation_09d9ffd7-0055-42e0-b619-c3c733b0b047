'use client';

import { useState } from 'react';
import citiesData from '@/lib/helpers/cities.json';
import { FaShippingFast } from 'react-icons/fa';
import { IoLocationOutline } from 'react-icons/io5';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const DeliverySelector = () => {
  const [deliveryType, setDeliveryType] = useState<'standard' | 'express'>('standard');
  const [region, setRegion] = useState('');
  const [city, setCity] = useState('');
  const [deliveryFee, setDeliveryFee] = useState<number | null>(null);

  const deliveryFees: { [key: string]: number } = {
    'standard-Ahafo-Bechem': 15,
    'standard-Ashanti-Adum': 20,
    'express-Ashanti-Adum': 30,
    // Add more combinations as needed...
  };

  const handleRegionChange = (selectedRegion: string) => {
    setRegion(selectedRegion);
    setCity('');
    setDeliveryFee(null);
  };

  const handleCityChange = (selectedCity: string) => {
    setCity(selectedCity);
    const feeKey = `${deliveryType}-${region}-${selectedCity}`;
    const fee = deliveryFees[feeKey] ?? 30; // Default delivery fee if not in the object
    setDeliveryFee(fee);
  };

  const handleDeliveryTypeChange = (type: 'standard' | 'express') => {
    setDeliveryType(type);
    setDeliveryFee(null);
    setRegion('');
    setCity('');
  };

  return (
    <section className="my-2 bg-white py-3 px-2 w-full rounded shadow-sm">
      <h1 className="text-sm font-semibold text-gray-600 border-b pb-2">Delivery</h1>

      <div className="mt-3 flex gap-2">
        <button
          className={`flex-1 p-1 rounded border ${
            deliveryType === 'standard' ? 'bg-primary/10 text-primary border-primary/20' : 'bg-white text-gray-800 border-gray-300 hover:border-primary/20 hover:bg-gray-50 transition-colors duration-200'
          }`}
          onClick={() => handleDeliveryTypeChange('standard')}
        >
          Standard Delivery
        </button>
        <button
          className={`flex-1 p-1 rounded border ${
            deliveryType === 'express' ? 'bg-primary/10 text-primary border-primary/20' : 'bg-white text-gray-800 border-gray-300 hover:border-primary/20 hover:bg-gray-50 transition-colors duration-200'
          }`}
          onClick={() => handleDeliveryTypeChange('express')}
        >
          Flashy Express
        </button>
      </div>

      {deliveryType === 'express' && (
        <p className="text-xs text-gray-500 mt-1">
          Express Delivery in <span className="font-medium">less than 24 hrs.</span> Available in main cities only.
        </p>
      )}

      {deliveryType === 'standard' && (
        <p className="text-xs text-gray-500 mt-1">
          Standard Delivery takes <span className="font-medium">up to 1 to 2 weeks</span> depending on your location.
        </p>
      )}

      <div className="mt-3 space-y-4">
        {/* Region Selection */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Select Region</label>
          <Select onValueChange={handleRegionChange} value={region}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="-- Choose Region --" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(citiesData).map((reg) => (
                <SelectItem key={reg} value={reg}>
                  {reg}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* City Selection */}
        <div>
          <label className="block text-sm text-gray-700 mb-1">Select City</label>
          <Select onValueChange={handleCityChange} value={city} disabled={!region}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="-- Choose City --" />
            </SelectTrigger>
            <SelectContent>
              {region &&
                citiesData[region as keyof typeof citiesData].map((ct) => (
                  <SelectItem key={ct} value={ct}>
                    {ct}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>

        {deliveryFee !== null && (
          <div className="flex items-center gap-3 p-3 bg-primary-50 rounded border border-primary/10">
            <FaShippingFast className="text-primary" size={24} />
            <div>
              <p className="text-sm text-gray-700 flex items-center gap-1">
                <IoLocationOutline className="text-gray-600" />
                Delivery to {city}, {region}
              </p>
              <p className="text-sm font-semibold text-gray-800">Delivery Fee: GH₵ {deliveryFee}</p>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default DeliverySelector;
