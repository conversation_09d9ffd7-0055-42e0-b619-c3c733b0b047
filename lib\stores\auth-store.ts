import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AuthState, User, UserRole } from '@/lib/types/auth';

interface AuthActions {
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: User, token: string) => void;
  logout: () => void;
  clearError: () => void;
  updateUser: (updates: Partial<User>) => void;
  validateToken: () => void;
}

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setUser: (user: User) => {
        set({
          user,
          isAuthenticated: true,
          error: null,
        });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      login: (user: User, token: string) => {
        // Store token in localStorage with metadata using AuthManager
        localStorage.setItem('auth_token', token);
        // Note: AuthManager.setToken could be used here in the future for enhanced token management

        set({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      },

      logout: () => {
        // Clear token from localStorage
        localStorage.removeItem('auth_token');

        set({
          ...initialState,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates },
          });
        }
      },

      validateToken: () => {
        if (typeof window === 'undefined') return;

        const token = localStorage.getItem('auth_token');
        const currentState = get();

        // If we think we're authenticated but have no token, logout
        if (currentState.isAuthenticated && !token) {
          console.log('Auth store: No token found, logging out');
          get().logout();
          return;
        }

        // If we have a token, check if it's expired
        if (token && currentState.isAuthenticated) {
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const isExpired = payload.exp * 1000 < Date.now();

            if (isExpired) {
              console.log('Auth store: Token expired, logging out');
              get().logout();
              return;
            }
          } catch (error) {
            console.log('Auth store: Invalid token format, logging out');
            get().logout();
            return;
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Utility functions for token management (for backward compatibility)
export const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token');
  }
  return null;
};

// Refresh token removed - using single token approach

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch {
    return true;
  }
};

// Selectors for easier access to specific parts of the store
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    isBuyer: store.user?.role === 'buyer',
    isCreator: store.user?.role === 'creator',
  };
};

export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    setUser: store.setUser,
    setLoading: store.setLoading,
    setError: store.setError,
    login: store.login,
    logout: store.logout,
    clearError: store.clearError,
    updateUser: store.updateUser,
    validateToken: store.validateToken,
  };
};
