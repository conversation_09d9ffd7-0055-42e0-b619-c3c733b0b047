import { TbSmartHome } from "react-icons/tb";
import {  PiWalletBold } from "react-icons/pi";
import { GrMenu } from "react-icons/gr";
import { MdOutlineShoppingBag, MdOutlineStorefront } from "react-icons/md";
import { HiOutlineClipboardList } from "react-icons/hi";
import { cn } from "@/lib/utils";
import { BsPerson } from "react-icons/bs";
import { IoHeartOutline } from "react-icons/io5";

export const buyerBottombarLinks = [
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <TbSmartHome size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/",
    label: "Home",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <MdOutlineShoppingBag size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/catalog",
    label: "Explore",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <HiOutlineClipboardList size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/orders",
    label: "Orders",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <IoHeartOutline size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/wishlist",
    label: "Wishlist",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <BsPerson size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/everyfash-account",
    label: "Account",
  },
];


export const creatorbottombarLinks = [
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <TbSmartHome size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/creators",
    label: "Home",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <MdOutlineShoppingBag size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/creators/products",
    label: "Products",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <HiOutlineClipboardList size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/creators/orders",
    label: "Orders",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <PiWalletBold size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/creators/earnings",
    label: "Earnings",
  },
  {
    imgURL: ({ isActive }: { isActive: boolean }) => (
      <GrMenu size={22} className={cn(isActive ? "text-red-400" : "text-white")} />
    ),
    route: "/creators/menu",
    label: "Menu",
  },
];


export const adminBottomBarLinks = [
  {
    imgURL: <TbSmartHome size={22} />,
    route: "/admin",
    label: "Home",
  },
  {
    imgURL: <MdOutlineStorefront size={22} />,
    route: "/admin/creators",
    label: "Vendors",
  },
  {
    imgURL: <HiOutlineClipboardList size={22} />,
    route: "/admin/orders",
    label: "Orders",
  },
  {
    imgURL: <PiWalletBold size={22} />,
    route: "/admin/earnings",
    label: "Earnings",
  },
  {
    imgURL: <GrMenu size={22} />,
    route: "/admin/menu",
    label: "Menu",
  },
];
