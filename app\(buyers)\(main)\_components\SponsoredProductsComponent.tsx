import React from 'react'
import ProductCard from '@/components/ui/custom/ProductCard';
import ProductsCarousel from './ProductsCarousel';
import { products } from '@/lib/data';


const SponsoredProductsComponent = () => {
  return (
    <section className="flex flex-col w-full mt-6">
     {/* Flash Sales Header */}

        <div className="flex justify-between items-center bg-primary py-4 px-3 rounded-sm text-primary-foreground">
            <h1 className="text-base font-bold">Sponsored Products</h1>
        </div>

       {/* Carousel with Product Cards */}

        <ProductsCarousel>
            {products.map((product) => (
            <div key={product.id} className="p-1">
                <ProductCard product={product} cardType='normal' />
            </div>
            ))}
        </ProductsCarousel>

    </section>
  )
}

export default SponsoredProductsComponent
