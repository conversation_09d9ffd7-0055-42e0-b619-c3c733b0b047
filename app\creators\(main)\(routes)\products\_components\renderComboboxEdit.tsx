"use client"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import React, { useState } from "react"



export const renderComboboxEdit = (
    label: string,
    value: string,
    isOpen: boolean,
    options: { label: string; value: string }[],
    setIsOpen: (open: boolean) => void,
    onSelect: (val: string) => void
  ) => {


    return (
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" role="combobox" className="w-full justify-between">
              {value
                ? options.find((option) => option.value === value)?.label
                : `Select ${label}...`}
              <ChevronsUpDown className="opacity-50 ml-2" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder={`Search ${label}...`} className="h-9" />
              <CommandList>
                <CommandEmpty>No {label.toLowerCase()} found.</CommandEmpty>
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => {
                        onSelect(option.value === value ? "" : option.value)
                        setIsOpen(false)
                      }}
                    >
                      {option.label}
                      <Check className={cn("ml-auto", value === option.value ? "opacity-100" : "opacity-0")} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    )
  }